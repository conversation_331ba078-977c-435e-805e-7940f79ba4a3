﻿using CloudUnitTest.SampleData;
using CommonChecker.DB;
using Domain.Models.CommonModel;
using Domain.Models.Diseases;
using Domain.Models.MedicalExamination;
using Domain.Models.MstItem;
using Domain.Models.OrdInfDetails;
using Domain.Models.OrdInfs;
using Domain.Models.SystemConf;
using Entity.Tenant;
using Helper.Common;
using Helper.Constants;
using Infrastructure.Options;
using Infrastructure.Repositories;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Moq;
using static Helper.Constants.OrderInfConst;

namespace CloudUnitTest.Repository.CheckedSpecialItem;

public class CheckedOrderTest : BaseUT
{
    /// <summary>
    /// Check IgakuItem if it exist then return null
    /// </summary>
    [Test]
    public void IgakuTokusitu_001_Exist_IgakuItem()
    {
        // Arrange
        int hpId = 1, sinDate = 20221111, hokenId = 10, syosaisinKbn = 1;
        long raiinNo = 9412;
        bool isJouhou = true;
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                1,
                10,
                1,
                1,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113034010",
                10
            ),
             new OrdInfDetailModel(
                "113001810",
                10
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(1,2002,4,0,"",""),
            new SystemConfModel(1,4001,4,0,"","")
        };

        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        // Act
        var iagkutokusitu = medicalExaminationRepository.IgakuTokusitu(hpId, raiinNo, sinDate, hokenId, syosaisinKbn, byomeiModelList, ordInfDetailModels, isJouhou, systemConfModels);
        // Assert
        Assert.True(iagkutokusitu.Count == 0);
    }

    [Test]
    public void IgakuTokusitu_002_NotExist_TenMst()
    {
        // Arrange
        int hpId = -1, sinDate = 20221111, hokenId = 10, syosaisinKbn = 1;
        long raiinNo = 9412;
        bool isJouhou = true;
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                1,
                10,
                1,
                1,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113034011",
                10
            ),
             new OrdInfDetailModel(
                "113001811",
                10
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(1,2002,4,0,"",""),
            new SystemConfModel(1,4001,4,0,"","")
        };

        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        // Act
        var iagkutokusitu = medicalExaminationRepository.IgakuTokusitu(hpId, raiinNo, sinDate, hokenId, syosaisinKbn, byomeiModelList, ordInfDetailModels, isJouhou, systemConfModels);
        // Assert
        Assert.True(iagkutokusitu.Count == 0);
    }

    [Test]
    public void IgakuTokusitu_003_NotExist_TenMst()
    {
        // Arrange
        int hpId = -1, sinDate = 20221111, hokenId = 10, syosaisinKbn = 1;
        long raiinNo = 9412;
        bool isJouhou = false;
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                1,
                10,
                1,
                1,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113034011",
                10
            ),
             new OrdInfDetailModel(
                "113001811",
                10
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(1,2002,4,0,"",""),
            new SystemConfModel(1,4001,4,0,"","")
        };

        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        // Act
        var iagkutokusitu = medicalExaminationRepository.IgakuTokusitu(hpId, raiinNo, sinDate, hokenId, syosaisinKbn, byomeiModelList, ordInfDetailModels, isJouhou, systemConfModels);
        // Assert
        Assert.True(iagkutokusitu.Count == 0);
    }

    /// <summary>
    /// Check IsJouHou if it is true then check sinDate and tenMst, if it is false then only check tenMst
    /// </summary>
    [Test]
    public void IgakuTokusitu_004_IsJouhou()
    {
        // Arrange
        int hpId = 1, sinDate1 = 20221111, sinDate2 = 20210101, hokenId = 10, syosaisinKbn = 1;
        long raiinNo = 9412;
        bool isJouhou1 = true, isJouhou2 = false;
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                1,
                10,
                1,
                1,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113034010",
                10
            ), new OrdInfDetailModel(
                "113001810",
                10
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(1,2002,4,0,"",""),
            new SystemConfModel(1,4001,4,0,"","")
        };

        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        // Act
        var iagkutokusitu1 = medicalExaminationRepository.IgakuTokusitu(hpId, raiinNo, sinDate1, hokenId, syosaisinKbn, byomeiModelList, ordInfDetailModels, isJouhou1, systemConfModels);
        var iagkutokusitu2 = medicalExaminationRepository.IgakuTokusitu(hpId, raiinNo, sinDate2, hokenId, syosaisinKbn, byomeiModelList, ordInfDetailModels, isJouhou1, systemConfModels);
        var iagkutokusitu3 = medicalExaminationRepository.IgakuTokusitu(hpId, raiinNo, sinDate2, hokenId, syosaisinKbn, byomeiModelList, ordInfDetailModels, isJouhou2, systemConfModels);
        // Assert
        Assert.True(iagkutokusitu1.Count == 0 && iagkutokusitu2.Count == 0 && iagkutokusitu3.Count == 0);
    }

    /// <summary>
    /// Check Syosai
    /// </summary>
    [Test]
    public void IgakuTokusitu_005_Syosai()
    {
        // Arrange
        int hpId = 1, sinDate1 = 20221111, sinDate2 = 20210101, hokenId = 10, syosaisinKbn1 = 1, syosaisinKbn2 = 6, syosaisinKbn3 = 2, syosaisinKbn4 = 4, syosaisinKbn5 = 8;
        long raiinNo = 9412;
        bool isJouhou1 = true, isJouhou2 = false;
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                1,
                10,
                1,
                1,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "11303401012",
                10
            ), new OrdInfDetailModel(
                "**********3",
                10
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(1,2002,4,0,"",""),
            new SystemConfModel(1,4001,4,0,"","")
        };

        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        // Act
        var iagkutokusitu1 = medicalExaminationRepository.IgakuTokusitu(hpId, raiinNo, sinDate1, hokenId, syosaisinKbn1, byomeiModelList, ordInfDetailModels, isJouhou1, systemConfModels);
        var iagkutokusitu2 = medicalExaminationRepository.IgakuTokusitu(hpId, raiinNo, sinDate2, hokenId, syosaisinKbn2, byomeiModelList, ordInfDetailModels, isJouhou1, systemConfModels);
        var iagkutokusitu3 = medicalExaminationRepository.IgakuTokusitu(hpId, raiinNo, sinDate2, hokenId, syosaisinKbn3, byomeiModelList, ordInfDetailModels, isJouhou2, systemConfModels);
        var iagkutokusitu4 = medicalExaminationRepository.IgakuTokusitu(hpId, raiinNo, sinDate2, hokenId, syosaisinKbn4, byomeiModelList, ordInfDetailModels, isJouhou2, systemConfModels);
        var iagkutokusitu5 = medicalExaminationRepository.IgakuTokusitu(hpId, raiinNo, sinDate2, hokenId, syosaisinKbn5, byomeiModelList, ordInfDetailModels, isJouhou2, systemConfModels);
        // Assert
        Assert.True(iagkutokusitu1.Count == 0 && iagkutokusitu2.Count == 0 && iagkutokusitu3.Count == 0 && iagkutokusitu4.Count == 0 && iagkutokusitu5.Count == 0);
    }

    /// <summary>
    /// Check Special
    /// </summary>
    [Test]
    public void IgakuTokusitu_006_Disease_Special()
    {
        // Arrange
        int hpId = 1, sinDate = 20221111, hokenId = 10, syosaisinKbn = 15;
        long raiinNo = 9412;
        bool isJouhou1 = true, isJouhou2 = false;
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                5,
                10,
                0,
                20221212,
                1
            ),
             new PtDiseaseModel(
                5,
                0,
                1,
                20221010,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "**********31",
                10
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(1,2002,4,0,"",""),
            new SystemConfModel(1,4001,4,0,"","")
        };

        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        // Act
        var iagkutokusitu1 = medicalExaminationRepository.IgakuTokusitu(hpId, raiinNo, sinDate, hokenId, syosaisinKbn, byomeiModelList, ordInfDetailModels, isJouhou1, systemConfModels);
        var iagkutokusitu2 = medicalExaminationRepository.IgakuTokusitu(hpId, raiinNo, sinDate, hokenId, syosaisinKbn, byomeiModelList, ordInfDetailModels, isJouhou2, systemConfModels);
        Assert.True(iagkutokusitu1.Count == 1 && iagkutokusitu1.Any(i => i.CheckingContent == "\"特定疾患療養管理料（診療所・情報通信機器）\"を算定できる可能性があります。"));
        Assert.True(iagkutokusitu2.Count == 1 && iagkutokusitu2.Any(i => i.CheckingContent == "\"特定疾患療養管理料（診療所）\"を算定できる可能性があります。"));
    }

    [Test]
    public void IgakuTokusitu_006_2_Disease_Special()
    {
        // Arrange
        int hpId = 1, sinDate = 20221111, hokenId = 10, syosaisinKbn = 15;
        long raiinNo = 9412;
        bool isJouhou1 = true, isJouhou2 = false;
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                5,
                10,
                0,
                20221212,
                1
            ),
             new PtDiseaseModel(
                5,
                0,
                1,
                20221010,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "**********31",
                10
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(1,2002,4,0,"",""),
            new SystemConfModel(1,4001,4,0,"","")
        };
        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
        var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == hpId
            && p.GrpCd == 4001
            && p.GrpEdaNo == 0);
        var temp = systemConf?.Val ?? 0;
        if (systemConf != null) systemConf.Val = 0;
        else
        {
            systemConf = new SystemConf
            {
                HpId = 1,
                GrpCd = 4001,
                GrpEdaNo = 0,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 0
            };
            tenantTracking.SystemConfs.Add(systemConf);
        }
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        try
        {
            tenantTracking.SaveChanges();
            // Act
            var iagkutokusitu1 = medicalExaminationRepository.IgakuTokusitu(hpId, raiinNo, sinDate, hokenId, syosaisinKbn, byomeiModelList, ordInfDetailModels, isJouhou1, systemConfModels);
            var iagkutokusitu2 = medicalExaminationRepository.IgakuTokusitu(hpId, raiinNo, sinDate, hokenId, syosaisinKbn, byomeiModelList, ordInfDetailModels, isJouhou2, systemConfModels);
            Assert.True(iagkutokusitu1.Count == 1 && iagkutokusitu1.Any(i => i.CheckingContent == "\"特定疾患療養管理料（診療所・情報通信機器）\"を算定できる可能性があります。"));
            Assert.True(iagkutokusitu2.Count == 1 && iagkutokusitu2.Any(i => i.CheckingContent == "\"特定疾患療養管理料（診療所）\"を算定できる可能性があります。"));
        }
        finally
        {
            systemConf.Val = temp;
            tenantTracking.SaveChanges();
        }

    }

    /// <summary>
    /// Check Orther
    /// </summary>
    [Test]
    public void IgakuTokusitu_007_Disease_Other()
    {
        // Arrange
        int hpId = 1, sinDate = 20221111, hokenId = 10, syosaisinKbn = 15;
        long raiinNo = 9412;
        bool isJouhou1 = true, isJouhou2 = false;
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                8,
                10,
                0,
                20221212,
                1
            ),
             new PtDiseaseModel(
                8,
                0,
                1,
                20221010,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "**********31",
                10
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(1,2002,4,0,"",""),
            new SystemConfModel(1,4001,4,0,"","")
        };

        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        // Act
        var iagkutokusitu1 = medicalExaminationRepository.IgakuTokusitu(hpId, raiinNo, sinDate, hokenId, syosaisinKbn, byomeiModelList, ordInfDetailModels, isJouhou1, systemConfModels);
        var iagkutokusitu2 = medicalExaminationRepository.IgakuTokusitu(hpId, raiinNo, sinDate, hokenId, syosaisinKbn, byomeiModelList, ordInfDetailModels, isJouhou2, systemConfModels);
        Assert.True(iagkutokusitu1.Count == 1 && iagkutokusitu1.Any(i => i.CheckingContent == "\"特定疾患療養管理料（診療所・情報通信機器）\"を算定できる可能性があります。"));
        Assert.True(iagkutokusitu2.Count == 1 && iagkutokusitu2.Any(i => i.CheckingContent == "\"特定疾患療養管理料（診療所）\"を算定できる可能性があります。"));
    }

    /// <summary>
    /// Check Not Special and Other
    /// </summary>
    [Test]
    public void IgakuTokusitu_008_Disease_NoMainDisease()
    {
        // Arrange
        int hpId = 1, sinDate = 20221111, hokenId = 10, syosaisinKbn = 15;
        long raiinNo = 9412;
        bool isJouhou = true;
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                9,
                10,
                0,
                20221212,
                1
            ),
             new PtDiseaseModel(
                10,
                0,
                1,
                20221010,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "**********31",
                10
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(1,2002,4,0,"",""),
            new SystemConfModel(1,4001,4,0,"","")
        };
        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
        var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
            && p.GrpCd == 2002
            && p.GrpEdaNo == 4);
        var temp = systemConf?.Val ?? 0;
        if (systemConf != null) systemConf.Val = 1;
        else
        {
            systemConf = new SystemConf
            {
                HpId = 1,
                GrpCd = 2002,
                GrpEdaNo = 4,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenantTracking.SystemConfs.Add(systemConf);
        }

        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        try
        {
            tenantTracking.SaveChanges();
            // Act
            var iagkutokusitu1 = medicalExaminationRepository.IgakuTokusitu(hpId, raiinNo, sinDate, hokenId, syosaisinKbn, byomeiModelList, ordInfDetailModels, isJouhou, systemConfModels);
            Assert.True(iagkutokusitu1.Count == 0);
        }
        finally
        {
            systemConf.Val = temp;
            tenantTracking.SaveChanges();
        }
    }

    /// <summary>
    /// Check Not Special and Other
    /// </summary>
    [Test]
    public void IgakuTokusitu_009_Disease_NoDisease()
    {
        // Arrange
        int hpId = 1, sinDate = 20221111, hokenId = 10, syosaisinKbn = 15;
        long raiinNo = 9412;
        bool isJouhou = true;
        var byomeiModelList = new List<PtDiseaseModel>();
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "**********31",
                10
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(1,2002,4,0,"",""),
            new SystemConfModel(1,4001,4,0,"","")
        };
        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
        var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
            && p.GrpCd == 2002
            && p.GrpEdaNo == 4);
        var temp = systemConf?.Val ?? 0;
        if (systemConf != null) systemConf.Val = 1;
        else
        {
            systemConf = new SystemConf
            {
                HpId = 1,
                GrpCd = 2002,
                GrpEdaNo = 4,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenantTracking.SystemConfs.Add(systemConf);
        }

        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        try
        {
            tenantTracking.SaveChanges();
            // Act
            var iagkutokusitu1 = medicalExaminationRepository.IgakuTokusitu(hpId, raiinNo, sinDate, hokenId, syosaisinKbn, byomeiModelList, ordInfDetailModels, isJouhou, systemConfModels);
            Assert.True(iagkutokusitu1.Count == 0);
        }
        finally
        {
            systemConf.Val = temp;
            tenantTracking.SaveChanges();
        }
    }

    /// <summary>
    /// Check Item Sihifu of Order Detail
    /// </summary>
    [Test]
    public void SihifuToku1_010_ItemSihifu()
    {
        // Arrange
        int hpId = 1, sinDate = 20221111, ptId = 1, hokenId = 10, syosaisinKbn = 15, raiinNo = 1, oyaRaiinNo = 1;
        bool isJouhou = true;
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                8,
                10,
                0,
                20221212,
                1
            ),
             new PtDiseaseModel(
                8,
                0,
                1,
                20221010,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113000910",
                10
            ),
             new OrdInfDetailModel(
                "113000910",
                10
            ),
            new OrdInfDetailModel(
                "113034510",
                10
            ),
            new OrdInfDetailModel(
                "113002310",
                10
            ),
            new OrdInfDetailModel(
                "113034610",
                10
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(hpId,4001,2,0,"","")
        };
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        // Act
        var iagkutokusitu1 = medicalExaminationRepository.SihifuToku1(hpId, ptId, sinDate, hokenId, syosaisinKbn, raiinNo, oyaRaiinNo, byomeiModelList, ordInfDetailModels, isJouhou, systemConfModels);
        Assert.True(iagkutokusitu1.Count == 0);
    }

    [Test]
    public void SihifuToku1_011_NotTenMst()
    {
        // Arrange
        int hpId = -1, sinDate = 20221111, ptId = 1, hokenId = 10, syosaisinKbn = 15, raiinNo = 1, oyaRaiinNo = 1;
        bool isJouhou = true;
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                8,
                10,
                0,
                20221212,
                1
            ),
             new PtDiseaseModel(
                8,
                0,
                1,
                20221010,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113000911",
                10
            ),
             new OrdInfDetailModel(
                "113000911",
                10
            ),
            new OrdInfDetailModel(
                "113034511",
                10
            ),
            new OrdInfDetailModel(
                "113002311",
                10
            ),
            new OrdInfDetailModel(
                "113034611",
                10
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(hpId,4001,2,0,"","")
        };
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        // Act
        var iagkutokusitu1 = medicalExaminationRepository.SihifuToku1(hpId, ptId, sinDate, hokenId, syosaisinKbn, raiinNo, oyaRaiinNo, byomeiModelList, ordInfDetailModels, isJouhou, systemConfModels);
        Assert.True(iagkutokusitu1.Count == 0);
    }

    [Test]
    public void SihifuToku1_012_IsJouhou_NotTenMst()
    {
        // Arrange
        int hpId = -1, sinDate = 20220401, ptId = 1, hokenId = 10, syosaisinKbn = 15, raiinNo = 1, oyaRaiinNo = 1;
        bool isJouhou = true;
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                8,
                10,
                0,
                20221212,
                1
            ),
             new PtDiseaseModel(
                8,
                0,
                1,
                20221010,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113000911",
                10
            ),
             new OrdInfDetailModel(
                "113000911",
                10
            ),
            new OrdInfDetailModel(
                "113034511",
                10
            ),
            new OrdInfDetailModel(
                "113002311",
                10
            ),
            new OrdInfDetailModel(
                "113034611",
                10
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(hpId,4001,2,0,"","")
        };

        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        // Act
        var iagkutokusitu1 = medicalExaminationRepository.SihifuToku1(hpId, ptId, sinDate, hokenId, syosaisinKbn, raiinNo, oyaRaiinNo, byomeiModelList, ordInfDetailModels, isJouhou, systemConfModels);
        Assert.True(iagkutokusitu1.Count == 0);
    }

    [Test]
    public void SihifuToku1_013_IsJouhou_NotTenMst()
    {
        // Arrange
        int hpId = -1, sinDate = 20220331, ptId = 1, hokenId = 10, syosaisinKbn = 15, raiinNo = 1, oyaRaiinNo = 1;
        bool isJouhou = false;
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                8,
                10,
                0,
                20221212,
                1
            ),
             new PtDiseaseModel(
                8,
                0,
                1,
                20221010,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113000911",
                10
            ),
             new OrdInfDetailModel(
                "113000911",
                10
            ),
            new OrdInfDetailModel(
                "113034511",
                10
            ),
            new OrdInfDetailModel(
                "113002311",
                10
            ),
            new OrdInfDetailModel(
                "113034611",
                10
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(hpId,4001,2,0,"","")
        };
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        // Act
        var iagkutokusitu1 = medicalExaminationRepository.SihifuToku1(hpId, ptId, sinDate, hokenId, syosaisinKbn, raiinNo, oyaRaiinNo, byomeiModelList, ordInfDetailModels, isJouhou, systemConfModels);
        Assert.True(iagkutokusitu1.Count == 0);
    }

    [Test]
    public void SihifuToku1_014_IsJouhou_NotTenMst()
    {
        // Arrange
        int hpId = -1, sinDate = 20220331, ptId = 1, hokenId = 10, syosaisinKbn = 15, raiinNo = 1, oyaRaiinNo = 1;
        bool isJouhou = true;
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                8,
                10,
                0,
                20221212,
                1
            ),
             new PtDiseaseModel(
                8,
                0,
                1,
                20221010,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113000911",
                10
            ),
             new OrdInfDetailModel(
                "113000911",
                10
            ),
            new OrdInfDetailModel(
                "113034511",
                10
            ),
            new OrdInfDetailModel(
                "113002311",
                10
            ),
            new OrdInfDetailModel(
                "113034611",
                10
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(hpId,4001,2,0,"","")
        };
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        // Act
        var iagkutokusitu1 = medicalExaminationRepository.SihifuToku1(hpId, ptId, sinDate, hokenId, syosaisinKbn, raiinNo, oyaRaiinNo, byomeiModelList, ordInfDetailModels, isJouhou, systemConfModels);
        Assert.True(iagkutokusitu1.Count == 0);
    }

    [Test]
    public void SihifuToku1_015_hifukaSetting()
    {
        // Arrange
        int hpId = 1, sinDate = 20220402, ptId = 1, hokenId = 10, syosaisinKbn = 15, raiinNo = 1, oyaRaiinNo = 1;
        bool isJouhou = true;
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                8,
                10,
                0,
                20221212,
                1
            ),
             new PtDiseaseModel(
                8,
                0,
                1,
                20221010,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113000911",
                10
            ),
             new OrdInfDetailModel(
                "113000911",
                10
            ),
            new OrdInfDetailModel(
                "113034511",
                10
            ),
            new OrdInfDetailModel(
                "113002311",
                10
            ),
            new OrdInfDetailModel(
                "113034611",
                10
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(hpId,4001,2,0,"","")
        };
        bool isModify = false;
        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
        var systemGenerationConf = tenantTracking.SystemGenerationConfs.FirstOrDefault(p => p.HpId == hpId
            && p.GrpCd == 8001
            && p.GrpEdaNo == 1 && p.Val == 1 && p.StartDate <= sinDate && p.EndDate >= sinDate);
        if (systemGenerationConf != null)
        {
            isModify = true;
            systemGenerationConf.Val = 0;
        }
        else
        {
            systemGenerationConf = new SystemGenerationConf
            {
                HpId = 1,
                GrpCd = 8001,
                GrpEdaNo = 1,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 0
            };
            tenantTracking.SystemGenerationConfs.Add(systemGenerationConf);
        }
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        // Act
        try
        {
            tenantTracking.SaveChanges();
            var iagkutokusitu1 = medicalExaminationRepository.SihifuToku1(hpId, ptId, sinDate, hokenId, syosaisinKbn, raiinNo, oyaRaiinNo, byomeiModelList, ordInfDetailModels, isJouhou, systemConfModels);
            Assert.True(iagkutokusitu1.Count == 0);
        }
        finally
        {
            if (isModify)
            {
                systemGenerationConf.Val = 1;
                systemGenerationConf.CreateDate = systemGenerationConf.CreateDate.ToUniversalTime();
                systemGenerationConf.UpdateDate = systemGenerationConf.UpdateDate.ToUniversalTime();
                systemGenerationConf.Val = 1;
                tenantTracking.SystemGenerationConfs.Update(systemGenerationConf);
            }
            else
            {
                tenantTracking.SystemGenerationConfs.Remove(systemGenerationConf);
            }

            tenantTracking.SaveChanges();
        }

    }

    [Test]
    public void SihifuToku1_016_hifukaSetting()
    {
        // Arrange
        int hpId = 1, sinDate = 20220402, ptId = 1, hokenId = 10, syosaisinKbn = 15, raiinNo = 1, oyaRaiinNo = 1;
        bool isJouhou = true;
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                8,
                10,
                0,
                20221212,
                1
            ),
             new PtDiseaseModel(
                8,
                0,
                1,
                20221010,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113000911",
                10
            ),
             new OrdInfDetailModel(
                "113000911",
                10
            ),
            new OrdInfDetailModel(
                "113034511",
                10
            ),
            new OrdInfDetailModel(
                "113002311",
                10
            ),
            new OrdInfDetailModel(
                "113034611",
                10
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(hpId,4001,2,0,"","")
        };
        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
        var systemGenerationConf = tenantTracking.SystemGenerationConfs.FirstOrDefault(p => p.HpId == hpId
            && p.GrpCd == 8001
            && p.GrpEdaNo == 1 && p.StartDate <= sinDate && p.EndDate >= sinDate);
        if (systemGenerationConf != null && systemGenerationConf.Val != 1)
        {
            systemGenerationConf.Val = 1;
        }
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        // Act
        try
        {
            tenantTracking.SaveChanges();
            var iagkutokusitu1 = medicalExaminationRepository.SihifuToku1(hpId, ptId, sinDate, hokenId, syosaisinKbn, raiinNo, oyaRaiinNo, byomeiModelList, ordInfDetailModels, isJouhou, systemConfModels);
            Assert.True(iagkutokusitu1.Count == 0);
        }
        finally
        {
        }
    }

    // [Test]
    // public void SihifuToku1_017_existByoMeiSkin1()
    // {
    //     // Arrange
    //     int hpId = 1, sinDate = 20220402, ptId = 1, hokenId = 10, syosaisinKbn = 15, raiinNo = 1, oyaRaiinNo = 1;
    //     bool isJouhou = true;
    //     var byomeiModelList = new List<PtDiseaseModel>()
    //     {
    //         new PtDiseaseModel(
    //             3,
    //             10,
    //             0,
    //             20221212,
    //             1
    //         ),
    //          new PtDiseaseModel(
    //             8,
    //             0,
    //             1,
    //             20221010,
    //             1
    //         )
    //     };
    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(
    //             "113000911",
    //             10
    //         ),
    //          new OrdInfDetailModel(
    //             "113000911",
    //             10
    //         ),
    //         new OrdInfDetailModel(
    //             "113034511",
    //             10
    //         ),
    //         new OrdInfDetailModel(
    //             "113002311",
    //             10
    //         ),
    //         new OrdInfDetailModel(
    //             "113034611",
    //             10
    //         )
    //     };
    //     var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
    //     var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == hpId
    //         && p.GrpCd == 4001
    //         && p.GrpEdaNo == 2);
    //     bool isUpdate = false;
    //     bool isCreate = false;
    //     double value = 0;
    //     if (systemConf == null)
    //     {
    //         systemConf = new SystemConf()
    //         {
    //             HpId = hpId,
    //             GrpCd = 4001,
    //             GrpEdaNo = 2,
    //             Val = 1
    //         };
    //         tenantTracking.Add(systemConf);
    //         isCreate = true;
    //     }
    //     else if (systemConf != null && systemConf.Val != 1)
    //     {
    //         value = systemConf.Val;
    //         systemConf.Val = 1;
    //         isUpdate = true;
    //     }

    //     var mockConfiguration = new Mock<IConfiguration>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);
    //     // Act
    //     try
    //     {
    //         tenantTracking.SaveChanges();
    //         var iagkutokusitu1 = medicalExaminationRepository.SihifuToku1(hpId, ptId, sinDate, hokenId, syosaisinKbn, raiinNo, oyaRaiinNo, byomeiModelList, ordInfDetailModels, isJouhou);
    //         Assert.True(iagkutokusitu1.Count == 1);
    //     }
    //     finally
    //     {
    //         if (isUpdate)
    //         {
    //             systemConf.Val = value;
    //             systemConf.CreateDate = systemConf.CreateDate.ToUniversalTime();
    //             systemConf.UpdateDate = systemConf.UpdateDate.ToUniversalTime();
    //             tenantTracking.Update(systemConf);
    //         }
    //         else if (isCreate)
    //         {
    //             tenantTracking.Remove(systemConf);
    //         }
    //         tenantTracking.SaveChanges();
    //     }
    // }

    // [Test]
    // public void SihifuToku1_018_existByoMeiSkin1()
    // {
    //     // Arrange
    //     int hpId = 1, sinDate = 20220402, ptId = 1, hokenId = 10, syosaisinKbn = 15, raiinNo = 1, oyaRaiinNo = 1;
    //     bool isJouhou = true;
    //     var byomeiModelList = new List<PtDiseaseModel>()
    //     {
    //         new PtDiseaseModel(
    //             3,
    //             10,
    //             0,
    //             20221212,
    //             1
    //         ),
    //          new PtDiseaseModel(
    //             8,
    //             0,
    //             1,
    //             20221010,
    //             1
    //         )
    //     };
    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(
    //             "113000911",
    //             10
    //         ),
    //          new OrdInfDetailModel(
    //             "113000911",
    //             10
    //         ),
    //         new OrdInfDetailModel(
    //             "113034511",
    //             10
    //         ),
    //         new OrdInfDetailModel(
    //             "113002311",
    //             10
    //         ),
    //         new OrdInfDetailModel(
    //             "113034611",
    //             10
    //         )
    //     };
    //     var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
    //     var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == hpId
    //         && p.GrpCd == 4001
    //         && p.GrpEdaNo == 2);
    //     bool isUpdate = false;
    //     bool isCreate = false;
    //     double value = 0;
    //     if (systemConf == null)
    //     {
    //         systemConf = new SystemConf()
    //         {
    //             HpId = hpId,
    //             GrpCd = 4001,
    //             GrpEdaNo = 2,
    //             Val = 0
    //         };
    //         tenantTracking.Add(systemConf);
    //         isCreate = true;
    //     }
    //     else if (systemConf != null && systemConf.Val != 0)
    //     {
    //         value = systemConf.Val;
    //         systemConf.Val = 0;
    //         isUpdate = true;
    //     }

    //     var mockConfiguration = new Mock<IConfiguration>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);
    //     // Act
    //     try
    //     {
    //         tenantTracking.SaveChanges();
    //         var iagkutokusitu1 = medicalExaminationRepository.SihifuToku1(hpId, ptId, sinDate, hokenId, syosaisinKbn, raiinNo, oyaRaiinNo, byomeiModelList, ordInfDetailModels, isJouhou);
    //         Assert.True(iagkutokusitu1.Count == 1);
    //     }
    //     finally
    //     {
    //         if (isUpdate)
    //         {
    //             systemConf.Val = value;
    //             systemConf.CreateDate = systemConf.CreateDate.ToUniversalTime();
    //             systemConf.UpdateDate = systemConf.UpdateDate.ToUniversalTime();
    //             tenantTracking.Update(systemConf);
    //         }
    //         else if (isCreate)
    //         {
    //             tenantTracking.Remove(systemConf);
    //         }
    //         tenantTracking.SaveChanges();
    //     }
    // }

    /// <summary>
    /// Check TenMst follow IsJouHou
    /// </summary>
    [Test]
    public void SihifuToku1_019_TenMst()
    {
        // Arrange
        int hpId = 1, sinDate1 = 20220331, ptId = 1, sinDate2 = 20220430, hokenId = 10, syosaisinKbn = 15, raiinNo = 1, oyaRaiinNo = 1;
        bool isJouhou1 = true, isJouhou2 = false;
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                10,
                10,
                0,
                20221212,
                1
            ),
             new PtDiseaseModel(
                10,
                0,
                1,
                20221010,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113034510",
                10
            ),
             new OrdInfDetailModel(
                "113000910",
                10
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(hpId,4001,2,0,"","")
        };
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        // Act
        var iagkutokusitu1 = medicalExaminationRepository.SihifuToku1(hpId, ptId, sinDate1, hokenId, syosaisinKbn, raiinNo, oyaRaiinNo, byomeiModelList, ordInfDetailModels, isJouhou1, systemConfModels);
        var iagkutokusitu2 = medicalExaminationRepository.SihifuToku1(hpId, ptId, sinDate2, hokenId, syosaisinKbn, raiinNo, oyaRaiinNo, byomeiModelList, ordInfDetailModels, isJouhou1, systemConfModels);
        var iagkutokusitu3 = medicalExaminationRepository.SihifuToku1(hpId, ptId, sinDate2, hokenId, syosaisinKbn, raiinNo, oyaRaiinNo, byomeiModelList, ordInfDetailModels, isJouhou2, systemConfModels);
        Assert.True(iagkutokusitu1.Count == 0 && iagkutokusitu2.Count == 0 && iagkutokusitu3.Count == 0);
    }

    /// <summary>
    /// Check MeiSkin
    /// </summary>
    [Test]
    public void SihifuToku1_020_MeiSkin()
    {
        // Arrange
        int hpId = 1, sinDate = 20220501, hokenId = 10, syosaisinKbn1 = 1;
        long ptId = **********, raiinNo = 70096280111231300, oyaRaiinNo = 1;
        bool isJouhou = true;
        int randomKey = 10;
        var tenant = TenantProvider.GetNoTrackingDataContext();
        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
        var raiinInfs = CheckedOrderData.ReadRainInf(randomKey);
        var odrInfs = CheckedOrderData.ReadOdrInf(randomKey);
        var odrInfDetails = CheckedOrderData.ReadOdrInfDetail(randomKey);
        var systemGenerationConf = tenantTracking.SystemGenerationConfs.FirstOrDefault(p => p.HpId == 1
            && p.GrpCd == 8001
            && p.GrpEdaNo == 1
            && p.StartDate <= sinDate
            && p.EndDate >= sinDate);
        var temp = systemGenerationConf?.Val ?? 0;
        if (systemGenerationConf != null) systemGenerationConf.Val = 1;
        else
        {
            systemGenerationConf = new SystemGenerationConf
            {
                HpId = 1,
                GrpCd = 8001,
                GrpEdaNo = 1,
                StartDate = 0,
                EndDate = 99999999,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenantTracking.SystemGenerationConfs.Add(systemGenerationConf);
        }
        tenant.RaiinInfs.AddRange(raiinInfs);
        tenant.OdrInfs.AddRange(odrInfs);
        tenant.OdrInfDetails.AddRange(odrInfDetails);
        tenant.SaveChanges();
        tenantTracking.SaveChanges();
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                15,
                10,
                0,
                20221212,
                1
            ),
             new PtDiseaseModel(
                15,
                0,
                1,
                20221010,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113034510121",
                10
            ),
             new OrdInfDetailModel(
                "113000910122",
                10
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(hpId,4001,2,0,"","")
        };
        var mockConfiguration = new Mock<IConfiguration>();
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        try
        {
            // Act
            var iagkutokusitu1 = medicalExaminationRepository.SihifuToku1(hpId, ptId, sinDate, hokenId, syosaisinKbn1, raiinNo, oyaRaiinNo, byomeiModelList, ordInfDetailModels, isJouhou, systemConfModels);
            Assert.True(iagkutokusitu1.Count == 0);
            systemGenerationConf.Val = temp;

        }
        finally
        {
            tenant.RaiinInfs.RemoveRange(raiinInfs);
            tenant.OdrInfs.RemoveRange(odrInfs);
            tenant.OdrInfDetails.RemoveRange(odrInfDetails);
            tenant.SaveChanges();
            tenantTracking.SaveChanges();
        }
    }

    /// <summary>
    /// Check Syosai
    /// </summary>
    [Test]
    public void SihifuToku1_021_Syosai()
    {
        // Arrange
        int hpId = 1, sinDate = 20220501, hokenId = 10, syosaisinKbn1 = 1, syosaisinKbn2 = 6, syosaisinKbn3 = 2, syosaisinKbn4 = 4, syosaisinKbn5 = 8;
        long ptId = **********, raiinNo = 70096280111231300, oyaRaiinNo = 1;
        bool isJouhou = true;
        int randomKey = 11;
        var tenant = TenantProvider.GetNoTrackingDataContext();
        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
        var raiinInfs = CheckedOrderData.ReadRainInf(randomKey);
        var odrInfs = CheckedOrderData.ReadOdrInf(randomKey);
        var odrInfDetails = CheckedOrderData.ReadOdrInfDetail(randomKey);
        var systemGenerationConf = tenantTracking.SystemGenerationConfs.FirstOrDefault(p => p.HpId == 1
            && p.GrpCd == 8001
            && p.GrpEdaNo == 1
            && p.StartDate <= sinDate
            && p.EndDate >= sinDate);
        var temp = systemGenerationConf?.Val ?? 0;
        if (systemGenerationConf != null) systemGenerationConf.Val = 1;
        else
        {
            systemGenerationConf = new SystemGenerationConf
            {
                HpId = 1,
                GrpCd = 8001,
                GrpEdaNo = 1,
                StartDate = 0,
                EndDate = 99999999,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenantTracking.SystemGenerationConfs.Add(systemGenerationConf);
        }
        tenant.RaiinInfs.AddRange(raiinInfs);
        tenant.OdrInfs.AddRange(odrInfs);
        tenant.OdrInfDetails.AddRange(odrInfDetails);
        tenant.SaveChanges();
        tenantTracking.SaveChanges();
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                3,
                10,
                0,
                20221212,
                1
            ),
             new PtDiseaseModel(
                3,
                0,
                1,
                20221010,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113034510121",
                10
            ),
             new OrdInfDetailModel(
                "113000910122",
                10
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(hpId,4001,2,0,"","")
        };
        var mockConfiguration = new Mock<IConfiguration>();
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);

        try
        {
            // Act
            var iagkutokusitu1 = medicalExaminationRepository.SihifuToku1(hpId, ptId, sinDate, hokenId, syosaisinKbn1, raiinNo, oyaRaiinNo, byomeiModelList, ordInfDetailModels, isJouhou, systemConfModels);
            var iagkutokusitu2 = medicalExaminationRepository.SihifuToku1(hpId, ptId, sinDate, hokenId, syosaisinKbn2, raiinNo, oyaRaiinNo, byomeiModelList, ordInfDetailModels, isJouhou, systemConfModels);
            var iagkutokusitu3 = medicalExaminationRepository.SihifuToku1(hpId, ptId, sinDate, hokenId, syosaisinKbn3, raiinNo, oyaRaiinNo, byomeiModelList, ordInfDetailModels, isJouhou, systemConfModels);
            var iagkutokusitu4 = medicalExaminationRepository.SihifuToku1(hpId, ptId, sinDate, hokenId, syosaisinKbn4, raiinNo, oyaRaiinNo, byomeiModelList, ordInfDetailModels, isJouhou, systemConfModels);
            var iagkutokusitu5 = medicalExaminationRepository.SihifuToku1(hpId, ptId, sinDate, hokenId, syosaisinKbn5, raiinNo, oyaRaiinNo, byomeiModelList, ordInfDetailModels, isJouhou, systemConfModels);
            Assert.True(iagkutokusitu1.Count == 0 && iagkutokusitu2.Count == 0 && iagkutokusitu3.Count == 0 && iagkutokusitu4.Count == 0 && iagkutokusitu5.Count == 0);
            systemGenerationConf.Val = temp;

        }
        finally
        {
            tenant.RaiinInfs.RemoveRange(raiinInfs);
            tenant.OdrInfs.RemoveRange(odrInfs);
            tenant.OdrInfDetails.RemoveRange(odrInfDetails);
            tenant.SaveChanges();
            tenantTracking.SaveChanges();
        }
    }

    // /// <summary>
    // /// True
    // /// </summary>
    // [Test]
    // public void SihifuToku1_022_True()
    // {
    //     // Arrange
    //     int hpId = 1, sinDate = 20220822, hokenId = 10, syosaisinKbn1 = 20;
    //     long ptId = **********, raiinNo = 70096280111231, oyaRaiinNo = 1957703;
    //     bool isJouhou1 = true, isJouhou2 = false;
    //     int randomKey = 12;
    //     var tenant = TenantProvider.GetNoTrackingDataContext();
    //     var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
    //     var raiinInfs = CheckedOrderData.ReadRainInf(randomKey);
    //     var odrInfs = CheckedOrderData.ReadOdrInf(randomKey);
    //     var odrInfDetails = CheckedOrderData.ReadOdrInfDetail(randomKey);
    //     var systemGenerationConf = tenantTracking.SystemGenerationConfs.FirstOrDefault(p => p.HpId == 1
    //         && p.GrpCd == 8001
    //         && p.GrpEdaNo == 1
    //         && p.StartDate <= sinDate
    //         && p.EndDate >= sinDate);
    //     var temp = systemGenerationConf?.Val ?? 0;
    //     if (systemGenerationConf != null) systemGenerationConf.Val = 1;
    //     else
    //     {
    //         systemGenerationConf = new SystemGenerationConf
    //         {
    //             HpId = 1,
    //             GrpCd = 8001,
    //             GrpEdaNo = 1,
    //             StartDate = 0,
    //             EndDate = 99999999,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 1
    //         };
    //         tenantTracking.SystemGenerationConfs.Add(systemGenerationConf);
    //     }
    //     tenant.RaiinInfs.AddRange(raiinInfs);
    //     tenant.OdrInfs.AddRange(odrInfs);
    //     tenant.OdrInfDetails.AddRange(odrInfDetails);
    //     tenant.SaveChanges();
    //     tenantTracking.SaveChanges();
    //     var byomeiModelList = new List<PtDiseaseModel>()
    //     {
    //         new PtDiseaseModel(
    //             3,
    //             10,
    //             0,
    //             20221212,
    //             1
    //         ),
    //          new PtDiseaseModel(
    //             3,
    //             0,
    //             1,
    //             20221010,
    //             1
    //         )
    //     };
    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(
    //             "113034510121",
    //             10
    //         ),
    //          new OrdInfDetailModel(
    //             "113000910122",
    //             10
    //         )
    //     };

    //     var mockConfiguration = new Mock<IConfiguration>();
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);

    //     try
    //     {
    //         // Act
    //         var iagkutokusitu1 = medicalExaminationRepository.SihifuToku1(hpId, ptId, sinDate, hokenId, syosaisinKbn1, raiinNo, oyaRaiinNo, byomeiModelList, ordInfDetailModels, isJouhou1);
    //         var iagkutokusitu2 = medicalExaminationRepository.SihifuToku1(hpId, ptId, sinDate, hokenId, syosaisinKbn1, raiinNo, oyaRaiinNo, byomeiModelList, ordInfDetailModels, isJouhou2);
    //         Assert.True(iagkutokusitu1.Count > 0);
    //         Assert.True(iagkutokusitu1.Count == 1 && iagkutokusitu1.Any(i => i.CheckingContent == "\"皮膚科特定疾患指導管理料（１）（情報通信機器）\"を算定できる可能性があります。"));
    //         Assert.True(iagkutokusitu2.Count == 1 && iagkutokusitu2.Any(i => i.CheckingContent == "\"皮膚科特定疾患指導管理料（１）\"を算定できる可能性があります。"));
    //         systemGenerationConf.Val = temp;

    //     }
    //     finally
    //     {
    //         tenant.RaiinInfs.RemoveRange(raiinInfs);
    //         tenant.OdrInfs.RemoveRange(odrInfs);
    //         tenant.OdrInfDetails.RemoveRange(odrInfDetails);
    //         tenant.SaveChanges();
    //         tenantTracking.SaveChanges();
    //     }
    // }

    /// <summary>
    /// Check Item Sihifu of Order Detail
    /// </summary>
    [Test]
    public void SihifuToku2_023_Sihifu()
    {
        // Arrange
        int hpId = 1, sinDate = 20221111, ptId = 1, hokenId = 10, syosaisinKbn = 15, raiinNo = 1, oyaRaiinNo = 1, iBirthDay = 30;
        bool isJouhou = true;
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                8,
                10,
                0,
                20221212,
                1
            ),
             new PtDiseaseModel(
                8,
                0,
                1,
                20221010,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113000910",
                10
            ),
             new OrdInfDetailModel(
                "113034510",
                10
            ),
            new OrdInfDetailModel(
                "113002310",
                10
            ),
            new OrdInfDetailModel(
                "113034610",
                10
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(hpId,4001,2,0,"","")
        };
        var odrInfs = new List<int> { 1, 2, 3 };
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        // Act
        var sihifu = medicalExaminationRepository.SihifuToku2(hpId, ptId, sinDate, hokenId, iBirthDay, raiinNo, syosaisinKbn, oyaRaiinNo, byomeiModelList, ordInfDetailModels, odrInfs, isJouhou, systemConfModels);
        Assert.True(sihifu.Count == 0);
    }

    /// <summary>
    /// Check TenMst follow IsJouHou
    /// </summary>
    [Test]
    public void SihifuToku2_024_TenMst()
    {
        // Arrange
        int hpId = 1, sinDate1 = 20220331, ptId = 1, sinDate2 = 20220430, hokenId = 10, syosaisinKbn = 15, raiinNo = 1, oyaRaiinNo = 1, iBirthDay = 30;
        bool isJouhou1 = true, isJouhou2 = false;
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                10,
                10,
                0,
                20221212,
                1
            ),
             new PtDiseaseModel(
                10,
                0,
                1,
                20221010,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "11303451011",
                10
            ),
             new OrdInfDetailModel(
                "11300091011",
                10
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(hpId,4001,2,0,"","")
        };
        var odrInfs = new List<int> { 1, 2, 3 };

        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        // Act
        var iagkutokusitu1 = medicalExaminationRepository.SihifuToku2(hpId, ptId, sinDate1, hokenId, iBirthDay, syosaisinKbn, raiinNo, oyaRaiinNo, byomeiModelList, ordInfDetailModels, odrInfs, isJouhou1, systemConfModels);
        var iagkutokusitu2 = medicalExaminationRepository.SihifuToku2(hpId, ptId, sinDate2, hokenId, iBirthDay, syosaisinKbn, raiinNo, oyaRaiinNo, byomeiModelList, ordInfDetailModels, odrInfs, isJouhou1, systemConfModels);
        var iagkutokusitu3 = medicalExaminationRepository.SihifuToku2(hpId, ptId, sinDate2, hokenId, iBirthDay, syosaisinKbn, raiinNo, oyaRaiinNo, byomeiModelList, ordInfDetailModels, odrInfs, isJouhou2, systemConfModels);
        Assert.True(iagkutokusitu1.Count == 0 && iagkutokusitu2.Count == 0 && iagkutokusitu3.Count == 0);
    }

    /// <summary>
    /// Check TenMst follow IsJouHou, null TenMst
    /// </summary>
    [Test]
    public void SihifuToku2_025_TenMst()
    {
        // Arrange
        int hpId = -1, sinDate1 = 20220401, ptId = 1, sinDate2 = 20220430, hokenId = 10, syosaisinKbn = 15, raiinNo = 1, oyaRaiinNo = 1, iBirthDay = 30;
        bool isJouhou1 = true, isJouhou2 = false;
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                10,
                10,
                0,
                20221212,
                1
            ),
             new PtDiseaseModel(
                10,
                0,
                1,
                20221010,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "11303451011",
                10
            ),
             new OrdInfDetailModel(
                "11300091011",
                10
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(hpId,4001,2,0,"","")
        };
        var odrInfs = new List<int> { 1, 2, 3 };

        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        // Act
        var iagkutokusitu1 = medicalExaminationRepository.SihifuToku2(hpId, ptId, sinDate1, hokenId, iBirthDay, syosaisinKbn, raiinNo, oyaRaiinNo, byomeiModelList, ordInfDetailModels, odrInfs, isJouhou1, systemConfModels);
        var iagkutokusitu2 = medicalExaminationRepository.SihifuToku2(hpId, ptId, sinDate2, hokenId, iBirthDay, syosaisinKbn, raiinNo, oyaRaiinNo, byomeiModelList, ordInfDetailModels, odrInfs, isJouhou1, systemConfModels);
        var iagkutokusitu3 = medicalExaminationRepository.SihifuToku2(hpId, ptId, sinDate2, hokenId, iBirthDay, syosaisinKbn, raiinNo, oyaRaiinNo, byomeiModelList, ordInfDetailModels, odrInfs, isJouhou2, systemConfModels);
        Assert.True(iagkutokusitu1.Count == 0 && iagkutokusitu2.Count == 0 && iagkutokusitu3.Count == 0);
    }

    /// <summary>
    /// Check MeiSkin
    /// </summary>
    [Test]
    public void SihifuToku2_026_Hifuka()
    {
        // Arrange
        int hpId = 1, sinDate = 20220501, hokenId = 10, syosaisinKbn1 = 1, iBirthDay = 30;
        long ptId = **********, raiinNo = 70096280111231300, oyaRaiinNo = 1;
        bool isJouhou = true;
        int randomKey = 15;
        var tenant = TenantProvider.GetNoTrackingDataContext();
        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
        var raiinInfs = CheckedOrderData.ReadRainInf(randomKey);
        var odrInfs = CheckedOrderData.ReadOdrInf(randomKey);
        var odrInfDetails = CheckedOrderData.ReadOdrInfDetail(randomKey);
        var systemGenerationConf = tenantTracking.SystemGenerationConfs.FirstOrDefault(p => p.HpId == 1
            && p.GrpCd == 8001
            && p.GrpEdaNo == 1
            && p.StartDate <= sinDate
            && p.EndDate >= sinDate);
        var temp = systemGenerationConf?.Val ?? 0;
        if (systemGenerationConf != null) systemGenerationConf.Val = 0;
        else
        {
            systemGenerationConf = new SystemGenerationConf
            {
                HpId = 1,
                GrpCd = 8001,
                GrpEdaNo = 1,
                StartDate = 0,
                EndDate = 99999999,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 0
            };
            tenantTracking.SystemGenerationConfs.Add(systemGenerationConf);
        }
        tenant.RaiinInfs.AddRange(raiinInfs);
        tenant.OdrInfs.AddRange(odrInfs);
        tenant.OdrInfDetails.AddRange(odrInfDetails);

        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                15,
                10,
                0,
                20221212,
                1
            ),
             new PtDiseaseModel(
                15,
                0,
                1,
                20221010,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113034510121",
                10
            ),
             new OrdInfDetailModel(
                "113000910122",
                10
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(hpId,4001,2,0,"","")
        };
        var odrInfInputs = new List<int> { 1, 2, 3 };

        var mockConfiguration = new Mock<IConfiguration>();
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        try
        {
            tenant.SaveChanges();
            tenantTracking.SaveChanges();
            // Act
            var iagkutokusitu1 = medicalExaminationRepository.SihifuToku2(hpId, ptId, sinDate, hokenId, iBirthDay, raiinNo, syosaisinKbn1, oyaRaiinNo, byomeiModelList, ordInfDetailModels, odrInfInputs, isJouhou, systemConfModels);

            Assert.True(iagkutokusitu1.Count == 0);
        }
        finally
        {
            systemGenerationConf.Val = temp;
            tenant.RaiinInfs.RemoveRange(raiinInfs);
            tenant.OdrInfs.RemoveRange(odrInfs);
            tenant.OdrInfDetails.RemoveRange(odrInfDetails);
            tenant.SaveChanges();
            tenantTracking.SaveChanges();
        }
    }

    /// <summary>
    /// Check SihifuToku1 Skin2 contain L20
    /// </summary>
    [Test]
    public void SihifuToku2_027_Skin2_L20()
    {
        // Arrange
        int hpId = 1, sinDate = 20220501, hokenId = 10, syosaisinKbn1 = 1, iBirthDay1 = 30, iBirthDay2 = 15;
        long ptId = **********, raiinNo = 70096280111231300, oyaRaiinNo = 1;
        bool isJouhou = true;
        int randomKey = 16;
        var tenant = TenantProvider.GetNoTrackingDataContext();
        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
        var raiinInfs = CheckedOrderData.ReadRainInf(randomKey);
        var odrInfs = CheckedOrderData.ReadOdrInf(randomKey);
        var odrInfDetails = CheckedOrderData.ReadOdrInfDetail(randomKey);
        var systemGenerationConf = tenantTracking.SystemGenerationConfs.FirstOrDefault(p => p.HpId == 1
            && p.GrpCd == 8001
            && p.GrpEdaNo == 1
            && p.StartDate <= sinDate
            && p.EndDate >= sinDate);
        var temp = systemGenerationConf?.Val ?? 0;
        if (systemGenerationConf != null) systemGenerationConf.Val = 1;
        else
        {
            systemGenerationConf = new SystemGenerationConf
            {
                HpId = 1,
                GrpCd = 8001,
                GrpEdaNo = 1,
                StartDate = 0,
                EndDate = 99999999,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenantTracking.SystemGenerationConfs.Add(systemGenerationConf);
        }
        tenant.RaiinInfs.AddRange(raiinInfs);
        tenant.OdrInfs.AddRange(odrInfs);
        tenant.OdrInfDetails.AddRange(odrInfDetails);

        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                4,
                10,
                0,
                20221212,
                1,
                "L2010"
            ),
             new PtDiseaseModel(
                4,
                0,
                1,
                20221010,
                1,
                "L2010"
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113034510121",
                10
            ),
             new OrdInfDetailModel(
                "113000910122",
                10
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(hpId,4001,2,0,"","")
        };
        var odrInfInput1s = new List<int> { 1, 2, 3 };
        var odrInfInput2s = new List<int> { 1, 23, 3 };

        var mockConfiguration = new Mock<IConfiguration>();
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        try
        {
            tenant.SaveChanges();
            tenantTracking.SaveChanges();
            // Act
            var iagkutokusitu1 = medicalExaminationRepository.SihifuToku2(hpId, ptId, sinDate, hokenId, iBirthDay1, raiinNo, syosaisinKbn1, oyaRaiinNo, byomeiModelList, ordInfDetailModels, odrInfInput1s, isJouhou, systemConfModels);
            var iagkutokusitu2 = medicalExaminationRepository.SihifuToku2(hpId, ptId, sinDate, hokenId, iBirthDay2, raiinNo, syosaisinKbn1, oyaRaiinNo, byomeiModelList, ordInfDetailModels, odrInfInput2s, isJouhou, systemConfModels);
            Assert.True(iagkutokusitu1.Count == 0 && iagkutokusitu2.Count == 0);
        }
        finally
        {
            systemGenerationConf.Val = temp;
            tenant.RaiinInfs.RemoveRange(raiinInfs);
            tenant.OdrInfs.RemoveRange(odrInfs);
            tenant.OdrInfDetails.RemoveRange(odrInfDetails);
            tenant.SaveChanges();
            tenantTracking.SaveChanges();
        }


    }

    /// <summary>
    /// Check SihifuToku1 Skin2 contain L20
    /// </summary>
    [Test]
    public void SihifuToku2_028_Skin2_L20()
    {
        // Arrange
        int hpId = 1, sinDate = 20220501, hokenId = 10, syosaisinKbn1 = 1, iBirthDay1 = 30, iBirthDay2 = 20210202;
        long ptId = **********, raiinNo = 70096280111231300, oyaRaiinNo = 1;
        bool isJouhou = true;
        int randomKey = 16;
        var tenant = TenantProvider.GetNoTrackingDataContext();
        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
        var raiinInfs = CheckedOrderData.ReadRainInf(randomKey);
        var odrInfs = CheckedOrderData.ReadOdrInf(randomKey);
        var odrInfDetails = CheckedOrderData.ReadOdrInfDetail(randomKey);
        var systemGenerationConf = tenantTracking.SystemGenerationConfs.FirstOrDefault(p => p.HpId == 1
            && p.GrpCd == 8001
            && p.GrpEdaNo == 1
            && p.StartDate <= sinDate
            && p.EndDate >= sinDate);
        var temp = systemGenerationConf?.Val ?? 0;
        if (systemGenerationConf != null) systemGenerationConf.Val = 1;
        else
        {
            systemGenerationConf = new SystemGenerationConf
            {
                HpId = 1,
                GrpCd = 8001,
                GrpEdaNo = 1,
                StartDate = 0,
                EndDate = 99999999,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenantTracking.SystemGenerationConfs.Add(systemGenerationConf);
        }
        tenant.RaiinInfs.AddRange(raiinInfs);
        tenant.OdrInfs.AddRange(odrInfs);
        tenant.OdrInfDetails.AddRange(odrInfDetails);

        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                4,
                10,
                0,
                20221212,
                1,
                "L2010"
            ),
             new PtDiseaseModel(
                4,
                0,
                1,
                20221010,
                1,
                "L2010"
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113034510121",
                10
            ),
             new OrdInfDetailModel(
                "113000910122",
                10
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(hpId,4001,2,0,"","")
        };
        var odrInfInput1s = new List<int> { 1, 2, 3 };
        var odrInfInput2s = new List<int> { 1, 23, 3 };

        var mockConfiguration = new Mock<IConfiguration>();
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        try
        {
            tenant.SaveChanges();
            tenantTracking.SaveChanges();
            // Act
            var iagkutokusitu1 = medicalExaminationRepository.SihifuToku2(hpId, ptId, sinDate, hokenId, iBirthDay1, raiinNo, syosaisinKbn1, oyaRaiinNo, byomeiModelList, ordInfDetailModels, odrInfInput1s, isJouhou, systemConfModels);
            var iagkutokusitu2 = medicalExaminationRepository.SihifuToku2(hpId, ptId, sinDate, hokenId, iBirthDay2, raiinNo, syosaisinKbn1, oyaRaiinNo, byomeiModelList, ordInfDetailModels, odrInfInput2s, isJouhou, systemConfModels);
            Assert.True(iagkutokusitu1.Count == 0 && iagkutokusitu2.Count == 0);
        }
        finally
        {
            systemGenerationConf.Val = temp;
            tenant.RaiinInfs.RemoveRange(raiinInfs);
            tenant.OdrInfs.RemoveRange(odrInfs);
            tenant.OdrInfDetails.RemoveRange(odrInfDetails);
            tenant.SaveChanges();
            tenantTracking.SaveChanges();
        }
    }

    /// <summary>
    /// Check Skin2 doesn't no L20
    /// </summary>
    [Test]
    public void SihifuToku2_029_Skin2_No_L20()
    {
        // Arrange
        int hpId = 1, sinDate = 20220501, hokenId = 10, syosaisinKbn1 = 1, iBirthDay1 = 30, iBirthDay2 = 15;
        long ptId = **********, raiinNo = 70096280111231300, oyaRaiinNo = 1;
        bool isJouhou = true;
        int randomKey = 17;
        var tenant = TenantProvider.GetNoTrackingDataContext();
        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
        var raiinInfs = CheckedOrderData.ReadRainInf(randomKey);
        var odrInfs = CheckedOrderData.ReadOdrInf(randomKey);
        var odrInfDetails = CheckedOrderData.ReadOdrInfDetail(randomKey);
        var systemGenerationConf = tenantTracking.SystemGenerationConfs.FirstOrDefault(p => p.HpId == 1
            && p.GrpCd == 8001
            && p.GrpEdaNo == 1
            && p.StartDate <= sinDate
            && p.EndDate >= sinDate);
        var temp = systemGenerationConf?.Val ?? 0;
        if (systemGenerationConf != null) systemGenerationConf.Val = 1;
        else
        {
            systemGenerationConf = new SystemGenerationConf
            {
                HpId = 1,
                GrpCd = 8001,
                GrpEdaNo = 1,
                StartDate = 0,
                EndDate = 99999999,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenantTracking.SystemGenerationConfs.Add(systemGenerationConf);
        }
        tenant.RaiinInfs.AddRange(raiinInfs);
        tenant.OdrInfs.AddRange(odrInfs);
        tenant.OdrInfDetails.AddRange(odrInfDetails);
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                5,
                10,
                0,
                202210504,
                1,
                "M23"
            ),
             new PtDiseaseModel(
                5,
                0,
                1,
                20220504,
                1,
                "M23"
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113034510121",
                10
            ),
             new OrdInfDetailModel(
                "113000910122",
                10
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(hpId,4001,2,0,"","")
        };
        var odrInfInput1s = new List<int> { 1, 2, 3 };
        var odrInfInput2s = new List<int> { 1, 23, 3 };

        var mockConfiguration = new Mock<IConfiguration>();
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        try
        {
            tenant.SaveChanges();
            tenantTracking.SaveChanges();
            // Act
            var iagkutokusitu1 = medicalExaminationRepository.SihifuToku2(hpId, ptId, sinDate, hokenId, iBirthDay1, raiinNo, syosaisinKbn1, oyaRaiinNo, byomeiModelList, ordInfDetailModels, odrInfInput1s, isJouhou, systemConfModels);
            var iagkutokusitu2 = medicalExaminationRepository.SihifuToku2(hpId, ptId, sinDate, hokenId, iBirthDay2, raiinNo, syosaisinKbn1, oyaRaiinNo, byomeiModelList, ordInfDetailModels, odrInfInput2s, isJouhou, systemConfModels);
            Assert.True(iagkutokusitu1.Count == 0 && iagkutokusitu2.Count == 0);
        }
        finally
        {
            systemGenerationConf.Val = temp;
            tenant.RaiinInfs.RemoveRange(raiinInfs);
            tenant.OdrInfs.RemoveRange(odrInfs);
            tenant.OdrInfDetails.RemoveRange(odrInfDetails);
            tenant.SaveChanges();
            tenantTracking.SaveChanges();
        }
    }

    /// <summary>
    /// Check Syosai
    /// </summary>
    [Test]
    public void SihifuToku2_030_Syosai()
    {
        // Arrange
        int hpId = 1, sinDate = 20220501, hokenId = 10, syosaisinKbn1 = 1, syosaisinKbn2 = 6, syosaisinKbn3 = 2, syosaisinKbn4 = 4, syosaisinKbn5 = 8, iBirthDay = 30;
        long ptId = **********, raiinNo = 70096280111231300, oyaRaiinNo = 1;
        bool isJouhou = true;
        int randomKey = 18;
        var tenant = TenantProvider.GetNoTrackingDataContext();
        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
        var raiinInfs = CheckedOrderData.ReadRainInf(randomKey);
        var odrInfs = CheckedOrderData.ReadOdrInf(randomKey);
        var odrInfDetails = CheckedOrderData.ReadOdrInfDetail(randomKey);
        var systemGenerationConf = tenantTracking.SystemGenerationConfs.FirstOrDefault(p => p.HpId == 1
            && p.GrpCd == 8001
            && p.GrpEdaNo == 1
            && p.StartDate <= sinDate
            && p.EndDate >= sinDate);
        var temp = systemGenerationConf?.Val ?? 0;
        if (systemGenerationConf != null) systemGenerationConf.Val = 1;
        else
        {
            systemGenerationConf = new SystemGenerationConf
            {
                HpId = 1,
                GrpCd = 8001,
                GrpEdaNo = 1,
                StartDate = 0,
                EndDate = 99999999,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenantTracking.SystemGenerationConfs.Add(systemGenerationConf);
        }
        tenant.RaiinInfs.AddRange(raiinInfs);
        tenant.OdrInfs.AddRange(odrInfs);
        tenant.OdrInfDetails.AddRange(odrInfDetails);
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                4,
                10,
                0,
                20221212,
                1
            ),
             new PtDiseaseModel(
                4,
                0,
                1,
                20221010,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113034510121",
                10
            ),
             new OrdInfDetailModel(
                "113000910122",
                10
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(hpId,4001,2,0,"","")
        };
        var odrInfInput1s = new List<int> { 1, 2, 3 };

        var mockConfiguration = new Mock<IConfiguration>();
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        try
        {
            tenant.SaveChanges();
            tenantTracking.SaveChanges();
            // Act
            var iagkutokusitu1 = medicalExaminationRepository.SihifuToku2(hpId, ptId, sinDate, hokenId, iBirthDay, raiinNo, syosaisinKbn1, oyaRaiinNo, byomeiModelList, ordInfDetailModels, odrInfInput1s, isJouhou, systemConfModels);
            var iagkutokusitu2 = medicalExaminationRepository.SihifuToku2(hpId, ptId, sinDate, hokenId, iBirthDay, raiinNo, syosaisinKbn2, oyaRaiinNo, byomeiModelList, ordInfDetailModels, odrInfInput1s, isJouhou, systemConfModels);
            var iagkutokusitu3 = medicalExaminationRepository.SihifuToku2(hpId, ptId, sinDate, hokenId, iBirthDay, raiinNo, syosaisinKbn3, oyaRaiinNo, byomeiModelList, ordInfDetailModels, odrInfInput1s, isJouhou, systemConfModels);
            var iagkutokusitu4 = medicalExaminationRepository.SihifuToku2(hpId, ptId, sinDate, hokenId, iBirthDay, raiinNo, syosaisinKbn4, oyaRaiinNo, byomeiModelList, ordInfDetailModels, odrInfInput1s, isJouhou, systemConfModels);
            var iagkutokusitu5 = medicalExaminationRepository.SihifuToku2(hpId, ptId, sinDate, hokenId, iBirthDay, raiinNo, syosaisinKbn5, oyaRaiinNo, byomeiModelList, ordInfDetailModels, odrInfInput1s, isJouhou, systemConfModels);
            Assert.True(iagkutokusitu1.Count == 0 && iagkutokusitu2.Count == 0 && iagkutokusitu3.Count == 0 && iagkutokusitu4.Count == 0 && iagkutokusitu5.Count == 0);
        }
        finally
        {
            systemGenerationConf.Val = temp;
            tenant.RaiinInfs.RemoveRange(raiinInfs);
            tenant.OdrInfs.RemoveRange(odrInfs);
            tenant.OdrInfDetails.RemoveRange(odrInfDetails);
            tenant.SaveChanges();
            tenantTracking.SaveChanges();
        }

    }

    /// <summary>
    /// Check True
    /// </summary>
    [Test]
    public void SihifuToku2_031_True()
    {
        // Arrange
        int hpId = 1, sinDate = 20220501, hokenId = 10, syosaisinKbn1 = 20, iBirthDay = 30;
        long ptId = **********, raiinNo = 70096280111231300, oyaRaiinNo = 1;
        bool isJouhou = true;
        int randomKey = 19;
        var tenant = TenantProvider.GetNoTrackingDataContext();
        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
        var raiinInfs = CheckedOrderData.ReadRainInf(randomKey);
        var odrInfs = CheckedOrderData.ReadOdrInf(randomKey);
        var odrInfDetails = CheckedOrderData.ReadOdrInfDetail(randomKey);
        var systemGenerationConf = tenantTracking.SystemGenerationConfs.FirstOrDefault(p => p.HpId == 1
            && p.GrpCd == 8001
            && p.GrpEdaNo == 1
            && p.StartDate <= sinDate
            && p.EndDate >= sinDate);
        var temp = systemGenerationConf?.Val ?? 0;
        if (systemGenerationConf != null) systemGenerationConf.Val = 1;
        else
        {
            systemGenerationConf = new SystemGenerationConf
            {
                HpId = 1,
                GrpCd = 8001,
                GrpEdaNo = 1,
                StartDate = 0,
                EndDate = 99999999,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenantTracking.SystemGenerationConfs.Add(systemGenerationConf);
        }
        tenant.RaiinInfs.AddRange(raiinInfs);
        tenant.OdrInfs.AddRange(odrInfs);
        tenant.OdrInfDetails.AddRange(odrInfDetails);
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                4,
                10,
                0,
                20221212,
                1
            ),
             new PtDiseaseModel(
                4,
                0,
                1,
                20221010,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113034510121",
                10
            ),
             new OrdInfDetailModel(
                "113000910122",
                10
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(hpId,4001,2,0,"","")
        };
        var odrInfInput1s = new List<int> { 1, 2, 3 };

        var mockConfiguration = new Mock<IConfiguration>();
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        try
        {
            tenant.SaveChanges();
            tenantTracking.SaveChanges();
            // Act
            var iagkutokusitu1 = medicalExaminationRepository.SihifuToku2(hpId, ptId, sinDate, hokenId, iBirthDay, raiinNo, syosaisinKbn1, oyaRaiinNo, byomeiModelList, ordInfDetailModels, odrInfInput1s, isJouhou, systemConfModels);
            Assert.True(iagkutokusitu1.Count > 0);
        }
        finally
        {
            systemGenerationConf.Val = temp;
            tenant.RaiinInfs.RemoveRange(raiinInfs);
            tenant.OdrInfs.RemoveRange(odrInfs);
            tenant.OdrInfDetails.RemoveRange(odrInfDetails);
            tenant.SaveChanges();
            tenantTracking.SaveChanges();
        }

    }

    /// <summary>
    /// Check True, check santeiKanren
    /// </summary>
    [Test]
    public void SihifuToku2_032_True()
    {
        // Arrange
        int hpId = 1, sinDate = 20220501, hokenId = 10, syosaisinKbn1 = 20, iBirthDay = 30;
        long ptId = **********, raiinNo = 70096280111231300, oyaRaiinNo = 1;
        bool isJouhou = true;
        int randomKey = 19;
        var tenant = TenantProvider.GetNoTrackingDataContext();
        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
        var raiinInfs = CheckedOrderData.ReadRainInf(randomKey);
        var odrInfs = CheckedOrderData.ReadOdrInf(randomKey);
        var odrInfDetails = CheckedOrderData.ReadOdrInfDetail(randomKey);
        var systemGenerationConf = tenantTracking.SystemGenerationConfs.FirstOrDefault(p => p.HpId == 1
            && p.GrpCd == 8001
            && p.GrpEdaNo == 1
            && p.StartDate <= sinDate
            && p.EndDate >= sinDate);
        var temp = systemGenerationConf?.Val ?? 0;
        if (systemGenerationConf != null) systemGenerationConf.Val = 1;
        else
        {
            systemGenerationConf = new SystemGenerationConf
            {
                HpId = 1,
                GrpCd = 8001,
                GrpEdaNo = 1,
                StartDate = 0,
                EndDate = 99999999,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenantTracking.SystemGenerationConfs.Add(systemGenerationConf);
        }
        var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == hpId
            && p.GrpCd == 4001
            && p.GrpEdaNo == 2);
        bool isUpdate = false;
        bool isCreate = false;
        double value = 0;
        if (systemConf == null)
        {
            systemConf = new SystemConf()
            {
                HpId = hpId,
                GrpCd = 4001,
                GrpEdaNo = 2,
                Val = 0
            };
            tenantTracking.Add(systemConf);
            isCreate = true;
        }
        else if (systemConf != null && systemConf.Val != 0)
        {
            value = systemConf.Val;
            systemConf.Val = 0;
            isUpdate = true;
        }
        tenant.RaiinInfs.AddRange(raiinInfs);
        tenant.OdrInfs.AddRange(odrInfs);
        tenant.OdrInfDetails.AddRange(odrInfDetails);
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                4,
                10,
                0,
                20221212,
                1
            ),
             new PtDiseaseModel(
                4,
                0,
                1,
                20221010,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113034510121",
                10
            ),
             new OrdInfDetailModel(
                "113000910122",
                10
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(hpId,4001,2,0,"","")
        };
        var odrInfInput1s = new List<int> { 1, 2, 3 };

        var mockConfiguration = new Mock<IConfiguration>();
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        try
        {
            tenant.SaveChanges();
            tenantTracking.SaveChanges();
            // Act
            var iagkutokusitu1 = medicalExaminationRepository.SihifuToku2(hpId, ptId, sinDate, hokenId, iBirthDay, raiinNo, syosaisinKbn1, oyaRaiinNo, byomeiModelList, ordInfDetailModels, odrInfInput1s, isJouhou, systemConfModels);
            Assert.True(iagkutokusitu1.Count > 0);
        }
        finally
        {
            if (isUpdate)
            {
                systemConf.Val = value;
                systemConf.CreateDate = systemConf.CreateDate.ToUniversalTime();
                systemConf.UpdateDate = systemConf.UpdateDate.ToUniversalTime();
                tenantTracking.Update(systemConf);
            }
            else if (isCreate)
            {
                tenantTracking.Remove(systemConf);
            }
            systemGenerationConf.Val = temp;
            tenant.RaiinInfs.RemoveRange(raiinInfs);
            tenant.OdrInfs.RemoveRange(odrInfs);
            tenant.OdrInfDetails.RemoveRange(odrInfDetails);
            tenant.SaveChanges();
            tenantTracking.SaveChanges();
        }
    }

    [Test]
    public void IgakuTenkan_031_Igaku()
    {
        // Arrange
        int hpId = 1, sinDate = 20221111, hokenId = 10, syosaisinKbn = 15;
        bool isJouhou = true;
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                8,
                10,
                0,
                20221212,
                1
            ),
             new PtDiseaseModel(
                8,
                0,
                1,
                20221010,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113002850",
                10
            ),
             new OrdInfDetailModel(
                "113029610",
                10
            )
        };
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        // Act
        var igakuTenka = medicalExaminationRepository.IgakuTenkan(hpId, sinDate, hokenId, syosaisinKbn, byomeiModelList, ordInfDetailModels, isJouhou);
        Assert.True(igakuTenka.Count == 0);
    }

    [Test]
    public void IgakuTenkan_032_TenMst()
    {
        // Arrange
        int hpId = -1, sinDate = 20221111, hokenId = 10, syosaisinKbn = 15;
        bool isJouhou = true;
        bool isJouhou2 = false;
        var byomeiModelList = new List<PtDiseaseModel>()
    {
        new PtDiseaseModel(
            8,
            10,
            0,
            20221212,
            1
        ),
         new PtDiseaseModel(
            8,
            0,
            1,
            20221010,
            1
        )
    };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
    {
        new OrdInfDetailModel(
            "113002851",
            10
        ),
         new OrdInfDetailModel(
            "113029611",
            10
        )
    };
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        // Act
        var igakuTenka = medicalExaminationRepository.IgakuTenkan(hpId, sinDate, hokenId, syosaisinKbn, byomeiModelList, ordInfDetailModels, isJouhou);
        var igakuTenka2 = medicalExaminationRepository.IgakuTenkan(hpId, sinDate, hokenId, syosaisinKbn, byomeiModelList, ordInfDetailModels, isJouhou2);
        Assert.True(igakuTenka.Count == 0 && igakuTenka2.Count == 0);
    }

    /// <summary>
    /// Check Syosai
    /// </summary>
    [Test]
    public void IgakuTenkan_033_Syosai()
    {
        // Arrange
        int hpId = 1, sinDate = 20220501, hokenId = 10, syosaisinKbn1 = 1, syosaisinKbn2 = 6, syosaisinKbn3 = 2, syosaisinKbn4 = 4, syosaisinKbn5 = 8;
        bool isJouhou = true;
        int randomKey = 21;
        var tenant = TenantProvider.GetNoTrackingDataContext();
        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
        var raiinInfs = CheckedOrderData.ReadRainInf(randomKey);
        var odrInfs = CheckedOrderData.ReadOdrInf(randomKey);
        var odrInfDetails = CheckedOrderData.ReadOdrInfDetail(randomKey);
        var systemGenerationConf = tenantTracking.SystemGenerationConfs.FirstOrDefault(p => p.HpId == 1
            && p.GrpCd == 8001
            && p.GrpEdaNo == 1
            && p.StartDate <= sinDate
            && p.EndDate >= sinDate);
        var temp = systemGenerationConf?.Val ?? 0;
        if (systemGenerationConf != null) systemGenerationConf.Val = 1;
        else
        {
            systemGenerationConf = new SystemGenerationConf
            {
                HpId = 1,
                GrpCd = 8001,
                GrpEdaNo = 1,
                StartDate = 0,
                EndDate = 99999999,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenantTracking.SystemGenerationConfs.Add(systemGenerationConf);
        }
        tenant.RaiinInfs.AddRange(raiinInfs);
        tenant.OdrInfs.AddRange(odrInfs);
        tenant.OdrInfDetails.AddRange(odrInfDetails);
        tenant.SaveChanges();
        tenantTracking.SaveChanges();
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                4,
                10,
                0,
                20221212,
                1
            ),
             new PtDiseaseModel(
                4,
                0,
                1,
                20221010,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113034510121",
                10
            ),
             new OrdInfDetailModel(
                "113000910122",
                10
            )
        };

        var mockConfiguration = new Mock<IConfiguration>();
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        try
        {
            // Act
            var iagkutokusitu1 = medicalExaminationRepository.IgakuTenkan(hpId, sinDate, hokenId, syosaisinKbn1, byomeiModelList, ordInfDetailModels, isJouhou);
            var iagkutokusitu2 = medicalExaminationRepository.IgakuTenkan(hpId, sinDate, hokenId, syosaisinKbn2, byomeiModelList, ordInfDetailModels, isJouhou);
            var iagkutokusitu3 = medicalExaminationRepository.IgakuTenkan(hpId, sinDate, hokenId, syosaisinKbn3, byomeiModelList, ordInfDetailModels, isJouhou);
            var iagkutokusitu4 = medicalExaminationRepository.IgakuTenkan(hpId, sinDate, hokenId, syosaisinKbn4, byomeiModelList, ordInfDetailModels, isJouhou);
            var iagkutokusitu5 = medicalExaminationRepository.IgakuTenkan(hpId, sinDate, hokenId, syosaisinKbn5, byomeiModelList, ordInfDetailModels, isJouhou);
            Assert.True(iagkutokusitu1.Count == 0 && iagkutokusitu2.Count == 0 && iagkutokusitu3.Count == 0 && iagkutokusitu4.Count == 0 && iagkutokusitu5.Count == 0);
            systemGenerationConf.Val = temp;
        }
        finally
        {
            tenant.RaiinInfs.RemoveRange(raiinInfs);
            tenant.OdrInfs.RemoveRange(odrInfs);
            tenant.OdrInfDetails.RemoveRange(odrInfDetails);
            tenant.SaveChanges();
            tenantTracking.SaveChanges();
        }
    }

    // /// <summary>
    // /// Check meiEpi
    // /// </summary>
    // [Test]
    // public void IgakuTenkan_034_MeiEpi()
    // {
    //     // Arrange
    //     int hpId = 1, sinDate = 20220501, hokenId = 10, syosaisinKbn1 = 20;
    //     bool isJouhou1 = true, isJouhou2 = false;
    //     int randomKey = 22;
    //     var tenant = TenantProvider.GetNoTrackingDataContext();
    //     var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
    //     var raiinInfs = CheckedOrderData.ReadRainInf(randomKey);
    //     var odrInfs = CheckedOrderData.ReadOdrInf(randomKey);
    //     var odrInfDetails = CheckedOrderData.ReadOdrInfDetail(randomKey);
    //     var systemGenerationConf = tenantTracking.SystemGenerationConfs.FirstOrDefault(p => p.HpId == 1
    //         && p.GrpCd == 8001
    //         && p.GrpEdaNo == 1
    //         && p.StartDate <= sinDate
    //         && p.EndDate >= sinDate);
    //     var temp = systemGenerationConf?.Val ?? 0;
    //     if (systemGenerationConf != null) systemGenerationConf.Val = 1;
    //     else
    //     {
    //         systemGenerationConf = new SystemGenerationConf
    //         {
    //             HpId = 1,
    //             GrpCd = 8001,
    //             GrpEdaNo = 1,
    //             StartDate = 0,
    //             EndDate = 99999999,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 1
    //         };
    //         tenantTracking.SystemGenerationConfs.Add(systemGenerationConf);
    //     }
    //     tenant.RaiinInfs.AddRange(raiinInfs);
    //     tenant.OdrInfs.AddRange(odrInfs);
    //     tenant.OdrInfDetails.AddRange(odrInfDetails);
    //     tenant.SaveChanges();
    //     tenantTracking.SaveChanges();
    //     var byomeiModelList = new List<PtDiseaseModel>()
    //     {
    //         new PtDiseaseModel(
    //             7,
    //             10,
    //             0,
    //             20221212,
    //             1
    //         ),
    //          new PtDiseaseModel(
    //             7,
    //             0,
    //             1,
    //             20221010,
    //             1
    //         )
    //     };
    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(
    //             "113034510121",
    //             10
    //         ),
    //          new OrdInfDetailModel(
    //             "113000910122",
    //             10
    //         )
    //     };

    //     var mockConfiguration = new Mock<IConfiguration>();
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);
    //     try
    //     {
    //         // Act
    //         var iagkutokusitu1 = medicalExaminationRepository.IgakuTenkan(hpId, sinDate, hokenId, syosaisinKbn1, byomeiModelList, ordInfDetailModels, isJouhou1);
    //         var iagkutokusitu2 = medicalExaminationRepository.IgakuTenkan(hpId, sinDate, hokenId, syosaisinKbn1, byomeiModelList, ordInfDetailModels, isJouhou2);
    //         Assert.True(iagkutokusitu1.Count == 1 && iagkutokusitu1.Any(i => i.CheckingContent == "\"てんかん指導料（情報通信機器）\"を算定できる可能性があります。"));
    //         Assert.True(iagkutokusitu2.Count == 1 && iagkutokusitu2.Any(i => i.CheckingContent == "\"てんかん指導料\"を算定できる可能性があります。"));
    //         systemGenerationConf.Val = temp;
    //     }
    //     finally
    //     {
    //         tenant.RaiinInfs.RemoveRange(raiinInfs);
    //         tenant.OdrInfs.RemoveRange(odrInfs);
    //         tenant.OdrInfDetails.RemoveRange(odrInfDetails);
    //         tenant.SaveChanges();
    //         tenantTracking.SaveChanges();
    //     }
    // }

    // /// <summary>
    // /// Check other
    // /// </summary>
    // [Test]
    // public void IgakuTenkan_035_Orther()
    // {
    //     // Arrange
    //     int hpId = 1, sinDate = 20220501, hokenId = 10, syosaisinKbn1 = 20;
    //     bool isJouhou1 = true, isJouhou2 = false;
    //     int randomKey = 23;
    //     var tenant = TenantProvider.GetNoTrackingDataContext();
    //     var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
    //     var raiinInfs = CheckedOrderData.ReadRainInf(randomKey);
    //     var odrInfs = CheckedOrderData.ReadOdrInf(randomKey);
    //     var odrInfDetails = CheckedOrderData.ReadOdrInfDetail(randomKey);
    //     var systemGenerationConf = tenantTracking.SystemGenerationConfs.FirstOrDefault(p => p.HpId == 1
    //         && p.GrpCd == 8001
    //         && p.GrpEdaNo == 1
    //         && p.StartDate <= sinDate
    //         && p.EndDate >= sinDate);
    //     var temp = systemGenerationConf?.Val ?? 0;
    //     if (systemGenerationConf != null) systemGenerationConf.Val = 1;
    //     else
    //     {
    //         systemGenerationConf = new SystemGenerationConf
    //         {
    //             HpId = 1,
    //             GrpCd = 8001,
    //             GrpEdaNo = 1,
    //             StartDate = 0,
    //             EndDate = 99999999,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 1
    //         };
    //         tenantTracking.SystemGenerationConfs.Add(systemGenerationConf);
    //     }
    //     tenant.RaiinInfs.AddRange(raiinInfs);
    //     tenant.OdrInfs.AddRange(odrInfs);
    //     tenant.OdrInfDetails.AddRange(odrInfDetails);
    //     tenant.SaveChanges();
    //     tenantTracking.SaveChanges();
    //     var byomeiModelList = new List<PtDiseaseModel>()
    //     {
    //         new PtDiseaseModel(
    //             8,
    //             10,
    //             0,
    //             20221212,
    //             1
    //         ),
    //          new PtDiseaseModel(
    //             8,
    //             0,
    //             1,
    //             20221010,
    //             1
    //         )
    //     };
    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(
    //             "113034510121",
    //             10
    //         ),
    //          new OrdInfDetailModel(
    //             "113000910122",
    //             10
    //         )
    //     };

    //     var mockConfiguration = new Mock<IConfiguration>();
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);
    //     try
    //     {
    //         // Act
    //         var iagkutokusitu1 = medicalExaminationRepository.IgakuTenkan(hpId, sinDate, hokenId, syosaisinKbn1, byomeiModelList, ordInfDetailModels, isJouhou1);
    //         var iagkutokusitu2 = medicalExaminationRepository.IgakuTenkan(hpId, sinDate, hokenId, syosaisinKbn1, byomeiModelList, ordInfDetailModels, isJouhou2);
    //         var iagkutokusitu3 = medicalExaminationRepository.IgakuTenkan(hpId, sinDate, hokenId, syosaisinKbn1, new(), ordInfDetailModels, isJouhou2);
    //         Assert.True(iagkutokusitu1.Count == 1 && iagkutokusitu1.Any(i => i.CheckingContent == "\"てんかん指導料（情報通信機器）\"を算定できる可能性があります。"));
    //         Assert.True(iagkutokusitu2.Count == 1 && iagkutokusitu2.Any(i => i.CheckingContent == "\"てんかん指導料\"を算定できる可能性があります。"));
    //         systemGenerationConf.Val = temp;

    //     }
    //     finally
    //     {
    //         tenant.RaiinInfs.RemoveRange(raiinInfs);
    //         tenant.OdrInfs.RemoveRange(odrInfs);
    //         tenant.OdrInfDetails.RemoveRange(odrInfDetails);
    //         tenant.SaveChanges();
    //         tenantTracking.SaveChanges();
    //     }
    // }

    /// <summary>
    /// Check some itemCd is Nanbyo
    /// </summary>
    [Test]
    public void IgakuNanbyo_036_IgakuNanByo()
    {
        // Arrange
        int hpId = 1, sinDate = 20221111, hokenId = 10, syosaisinKbn = 15;
        bool isJouhou = true;
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                8,
                10,
                0,
                20221212,
                1
            ),
             new PtDiseaseModel(
                8,
                0,
                1,
                20221010,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113002910",
                10
            ),
             new OrdInfDetailModel(
                "113029710",
                10
            )
        };
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        // Act
        var igakuTenka = medicalExaminationRepository.IgakuNanbyo(hpId, sinDate, hokenId, syosaisinKbn, byomeiModelList, ordInfDetailModels, isJouhou);
        Assert.True(igakuTenka.Count == 0);
    }

    [Test]
    public void IgakuNanbyo_037_TenMst()
    {
        // Arrange
        int hpId = -1, sinDate = 20221111, hokenId = 10, syosaisinKbn = 15;
        bool isJouhou = true;
        bool isJouhou2 = false;
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                8,
                10,
                0,
                20221212,
                1
            ),
             new PtDiseaseModel(
                8,
                0,
                1,
                20221010,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113002911",
                10
            ),
             new OrdInfDetailModel(
                "113029711",
                10
            )
        };
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        // Act
        var igakuTenka = medicalExaminationRepository.IgakuNanbyo(hpId, sinDate, hokenId, syosaisinKbn, byomeiModelList, ordInfDetailModels, isJouhou);
        var igakuTenka2 = medicalExaminationRepository.IgakuNanbyo(hpId, sinDate, hokenId, syosaisinKbn, byomeiModelList, ordInfDetailModels, isJouhou2);
        Assert.True(igakuTenka.Count == 0 && igakuTenka2.Count == 0);
    }

    /// <summary>
    /// Check Syosai
    /// </summary>
    [Test]
    public void IgakuNanbyo_038_Syosai()
    {
        // Arrange
        int hpId = 1, sinDate = 20220501, hokenId = 10, syosaisinKbn1 = 1, syosaisinKbn2 = 6, syosaisinKbn3 = 2, syosaisinKbn4 = 4, syosaisinKbn5 = 8;
        bool isJouhou = true;
        int randomKey = 25;
        var tenant = TenantProvider.GetNoTrackingDataContext();
        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
        var raiinInfs = CheckedOrderData.ReadRainInf(randomKey);
        var odrInfs = CheckedOrderData.ReadOdrInf(randomKey);
        var odrInfDetails = CheckedOrderData.ReadOdrInfDetail(randomKey);
        var systemGenerationConf = tenantTracking.SystemGenerationConfs.FirstOrDefault(p => p.HpId == 1
            && p.GrpCd == 8001
            && p.GrpEdaNo == 1
            && p.StartDate <= sinDate
            && p.EndDate >= sinDate);
        var temp = systemGenerationConf?.Val ?? 0;
        if (systemGenerationConf != null) systemGenerationConf.Val = 1;
        else
        {
            systemGenerationConf = new SystemGenerationConf
            {
                HpId = 1,
                GrpCd = 8001,
                GrpEdaNo = 1,
                StartDate = 0,
                EndDate = 99999999,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenantTracking.SystemGenerationConfs.Add(systemGenerationConf);
        }
        tenant.RaiinInfs.AddRange(raiinInfs);
        tenant.OdrInfs.AddRange(odrInfs);
        tenant.OdrInfDetails.AddRange(odrInfDetails);
        tenant.SaveChanges();
        tenantTracking.SaveChanges();
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                4,
                10,
                0,
                20221212,
                1
            ),
             new PtDiseaseModel(
                4,
                0,
                1,
                20221010,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113034510121",
                10
            ),
             new OrdInfDetailModel(
                "113000910122",
                10
            )
        };

        var mockConfiguration = new Mock<IConfiguration>();
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);

        try
        {
            //  Act
            var iagkutokusitu1 = medicalExaminationRepository.IgakuNanbyo(hpId, sinDate, hokenId, syosaisinKbn1, byomeiModelList, ordInfDetailModels, isJouhou);
            var iagkutokusitu2 = medicalExaminationRepository.IgakuNanbyo(hpId, sinDate, hokenId, syosaisinKbn2, byomeiModelList, ordInfDetailModels, isJouhou);
            var iagkutokusitu3 = medicalExaminationRepository.IgakuNanbyo(hpId, sinDate, hokenId, syosaisinKbn3, byomeiModelList, ordInfDetailModels, isJouhou);
            var iagkutokusitu4 = medicalExaminationRepository.IgakuNanbyo(hpId, sinDate, hokenId, syosaisinKbn4, byomeiModelList, ordInfDetailModels, isJouhou);
            var iagkutokusitu5 = medicalExaminationRepository.IgakuNanbyo(hpId, sinDate, hokenId, syosaisinKbn5, byomeiModelList, ordInfDetailModels, isJouhou);
            Assert.True(iagkutokusitu1.Count == 0 && iagkutokusitu2.Count == 0 && iagkutokusitu3.Count == 0 && iagkutokusitu4.Count == 0 && iagkutokusitu5.Count == 0);
            systemGenerationConf.Val = temp;
        }
        finally
        {
            tenant.RaiinInfs.RemoveRange(raiinInfs);
            tenant.OdrInfs.RemoveRange(odrInfs);
            tenant.OdrInfDetails.RemoveRange(odrInfDetails);
            tenant.SaveChanges();
            tenantTracking.SaveChanges();
        }
    }

    /// <summary>
    /// Check IsJouhou
    /// </summary>
    [Test]
    public void IgakuNanbyo_039_IsJouhou()
    {
        // Arrange
        int hpId = 1, sinDate = 20220301, hokenId = 10, syosaisinKbn1 = 1;
        bool isJouhou = true;
        int randomKey = 26;
        var tenant = TenantProvider.GetNoTrackingDataContext();
        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
        var raiinInfs = CheckedOrderData.ReadRainInf(randomKey);
        var odrInfs = CheckedOrderData.ReadOdrInf(randomKey);
        var odrInfDetails = CheckedOrderData.ReadOdrInfDetail(randomKey);
        var systemGenerationConf = tenantTracking.SystemGenerationConfs.FirstOrDefault(p => p.HpId == 1
            && p.GrpCd == 8001
            && p.GrpEdaNo == 1
            && p.StartDate <= sinDate
            && p.EndDate >= sinDate);
        var temp = systemGenerationConf?.Val ?? 0;
        if (systemGenerationConf != null) systemGenerationConf.Val = 1;
        else
        {
            systemGenerationConf = new SystemGenerationConf
            {
                HpId = 1,
                GrpCd = 8001,
                GrpEdaNo = 1,
                StartDate = 0,
                EndDate = 99999999,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenantTracking.SystemGenerationConfs.Add(systemGenerationConf);
        }
        tenant.RaiinInfs.AddRange(raiinInfs);
        tenant.OdrInfs.AddRange(odrInfs);
        tenant.OdrInfDetails.AddRange(odrInfDetails);
        tenant.SaveChanges();
        tenantTracking.SaveChanges();
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                4,
                10,
                0,
                20220302,
                1
            ),
             new PtDiseaseModel(
                4,
                0,
                1,
                20220302,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113034510121",
                10
            ),
             new OrdInfDetailModel(
                "113000910122",
                10
            )
        };

        var mockConfiguration = new Mock<IConfiguration>();
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        try
        {
            // Act
            var iagkutokusitu1 = medicalExaminationRepository.IgakuNanbyo(hpId, sinDate, hokenId, syosaisinKbn1, byomeiModelList, ordInfDetailModels, isJouhou);
            Assert.True(iagkutokusitu1.Count == 0);
            systemGenerationConf.Val = temp;
        }
        finally
        {
            tenant.RaiinInfs.RemoveRange(raiinInfs);
            tenant.OdrInfs.RemoveRange(odrInfs);
            tenant.OdrInfDetails.RemoveRange(odrInfDetails);
            tenant.SaveChanges();
            tenantTracking.SaveChanges();
        }
    }

    /// <summary>
    /// Check santeigai
    /// </summary>
    [Test]
    public void IgakuNanbyo_040_SanteiGai()
    {
        // Arrange
        int hpId = 1, sinDate = 20221212, hokenId = 10, syosaisinKbn1 = 20;
        bool isJouhou1 = true, isJouhou2 = false;
        int randomKey = 27;
        var tenant = TenantProvider.GetNoTrackingDataContext();
        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
        var raiinInfs = CheckedOrderData.ReadRainInf(randomKey);
        var odrInfs = CheckedOrderData.ReadOdrInf(randomKey);
        var odrInfDetails = CheckedOrderData.ReadOdrInfDetail(randomKey);
        var systemGenerationConf = tenantTracking.SystemGenerationConfs.FirstOrDefault(p => p.HpId == 1
            && p.GrpCd == 8001
            && p.GrpEdaNo == 1
            && p.StartDate <= sinDate
            && p.EndDate >= sinDate);
        var temp = systemGenerationConf?.Val ?? 0;
        if (systemGenerationConf != null) systemGenerationConf.Val = 1;
        else
        {
            systemGenerationConf = new SystemGenerationConf
            {
                HpId = 1,
                GrpCd = 8001,
                GrpEdaNo = 1,
                StartDate = 0,
                EndDate = 99999999,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenantTracking.SystemGenerationConfs.Add(systemGenerationConf);
        }
        tenant.RaiinInfs.AddRange(raiinInfs);
        tenant.OdrInfs.AddRange(odrInfs);
        tenant.OdrInfDetails.AddRange(odrInfDetails);
        tenant.SaveChanges();
        tenantTracking.SaveChanges();
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                20,
                10,
                0,
                20221212,
                1,
                "abc",
                9
            ),
             new PtDiseaseModel(
                20,
                0,
                1,
                20221010,
                1,
                "abc",
                9
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113034510121",
                10
            ),
             new OrdInfDetailModel(
                "113000910122",
                10
            )
        };

        var mockConfiguration = new Mock<IConfiguration>();
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        try
        {
            // Act
            var iagkutokusitu1 = medicalExaminationRepository.IgakuNanbyo(hpId, sinDate, hokenId, syosaisinKbn1, byomeiModelList, ordInfDetailModels, isJouhou1);
            var iagkutokusitu2 = medicalExaminationRepository.IgakuNanbyo(hpId, sinDate, hokenId, syosaisinKbn1, byomeiModelList, ordInfDetailModels, isJouhou2);
            Assert.True(iagkutokusitu1.Count == 1 && iagkutokusitu1.Any(i => i.CheckingContent == "\"難病外来指導管理料（情報通信機器）\"を算定できる可能性があります。"));
            Assert.True(iagkutokusitu2.Count == 1 && iagkutokusitu2.Any(i => i.CheckingContent == "\"難病外来指導管理料\"を算定できる可能性があります。"));
            systemGenerationConf.Val = temp;

        }
        finally
        {
            tenant.RaiinInfs.RemoveRange(raiinInfs);
            tenant.OdrInfs.RemoveRange(odrInfs);
            tenant.OdrInfDetails.RemoveRange(odrInfDetails);
            tenant.SaveChanges();
            tenantTracking.SaveChanges();
        }
    }

    /// <summary>
    /// Check santeigai
    /// </summary>
    [Test]
    public void IgakuNanbyo_041_SanteiGai()
    {
        // Arrange
        int hpId = 1, sinDate = 20221212, hokenId = 10, syosaisinKbn1 = 20;
        bool isJouhou1 = true, isJouhou2 = false;
        int randomKey = 27;
        var tenant = TenantProvider.GetNoTrackingDataContext();
        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
        var raiinInfs = CheckedOrderData.ReadRainInf(randomKey);
        var odrInfs = CheckedOrderData.ReadOdrInf(randomKey);
        var odrInfDetails = CheckedOrderData.ReadOdrInfDetail(randomKey);
        var systemGenerationConf = tenantTracking.SystemGenerationConfs.FirstOrDefault(p => p.HpId == 1
            && p.GrpCd == 8001
            && p.GrpEdaNo == 1
            && p.StartDate <= sinDate
            && p.EndDate >= sinDate);
        var temp = systemGenerationConf?.Val ?? 0;
        if (systemGenerationConf != null) systemGenerationConf.Val = 1;
        else
        {
            systemGenerationConf = new SystemGenerationConf
            {
                HpId = 1,
                GrpCd = 8001,
                GrpEdaNo = 1,
                StartDate = 0,
                EndDate = 99999999,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenantTracking.SystemGenerationConfs.Add(systemGenerationConf);
        }
        var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == hpId
            && p.GrpCd == 4001
            && p.GrpEdaNo == 7);
        bool isUpdate = false;
        bool isCreate = false;
        double value = 0;
        if (systemConf == null)
        {
            systemConf = new SystemConf()
            {
                HpId = hpId,
                GrpCd = 4001,
                GrpEdaNo = 7,
                Val = 1
            };
            tenantTracking.Add(systemConf);
            isCreate = true;
        }
        else if (systemConf != null && systemConf.Val != 1)
        {
            value = systemConf.Val;
            systemConf.Val = 1;
            isUpdate = true;
        }
        tenant.RaiinInfs.AddRange(raiinInfs);
        tenant.OdrInfs.AddRange(odrInfs);
        tenant.OdrInfDetails.AddRange(odrInfDetails);
        tenant.SaveChanges();
        tenantTracking.SaveChanges();
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                20,
                10,
                0,
                20221212,
                1,
                "abc",
                9
            ),
             new PtDiseaseModel(
                20,
                0,
                1,
                20221010,
                1,
                "abc",
                9
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113034510121",
                10
            ),
             new OrdInfDetailModel(
                "113000910122",
                10
            )
        };

        var mockConfiguration = new Mock<IConfiguration>();
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        try
        {
            // Act
            var iagkutokusitu1 = medicalExaminationRepository.IgakuNanbyo(hpId, sinDate, hokenId, syosaisinKbn1, byomeiModelList, ordInfDetailModels, isJouhou1);
            Assert.True(iagkutokusitu1.Count == 1 && iagkutokusitu1.Any(i => i.CheckingContent == "\"難病外来指導管理料（情報通信機器）\"を算定できる可能性があります。"));
            systemGenerationConf.Val = temp;

        }
        finally
        {
            if (isUpdate)
            {
                systemConf.Val = value;
                systemConf.CreateDate = systemConf.CreateDate.ToUniversalTime();
                systemConf.UpdateDate = systemConf.UpdateDate.ToUniversalTime();
                tenantTracking.Update(systemConf);
            }
            else if (isCreate)
            {
                tenantTracking.Remove(systemConf);
            }
            tenantTracking.SaveChanges();
            tenant.RaiinInfs.RemoveRange(raiinInfs);
            tenant.OdrInfs.RemoveRange(odrInfs);
            tenant.OdrInfDetails.RemoveRange(odrInfDetails);
            tenant.SaveChanges();
            tenantTracking.SaveChanges();
        }
    }

    /// <summary>
    /// Check true
    /// </summary>
    [Test]
    public void IgakuNanbyo_042_True()
    {
        // Arrange
        int hpId = 1, sinDate = 20220501, hokenId = 10, syosaisinKbn1 = 20;
        bool isJouhou = true;
        int randomKey = 28;
        var tenant = TenantProvider.GetNoTrackingDataContext();
        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
        var raiinInfs = CheckedOrderData.ReadRainInf(randomKey);
        var odrInfs = CheckedOrderData.ReadOdrInf(randomKey);
        var odrInfDetails = CheckedOrderData.ReadOdrInfDetail(randomKey);
        var systemGenerationConf = tenantTracking.SystemGenerationConfs.FirstOrDefault(p => p.HpId == 1
            && p.GrpCd == 8001
            && p.GrpEdaNo == 1
            && p.StartDate <= sinDate
            && p.EndDate >= sinDate);
        var temp = systemGenerationConf?.Val ?? 0;
        if (systemGenerationConf != null) systemGenerationConf.Val = 1;
        else
        {
            systemGenerationConf = new SystemGenerationConf
            {
                HpId = 1,
                GrpCd = 8001,
                GrpEdaNo = 1,
                StartDate = 0,
                EndDate = 99999999,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenantTracking.SystemGenerationConfs.Add(systemGenerationConf);
        }
        tenant.RaiinInfs.AddRange(raiinInfs);
        tenant.OdrInfs.AddRange(odrInfs);
        tenant.OdrInfDetails.AddRange(odrInfDetails);
        tenant.SaveChanges();
        tenantTracking.SaveChanges();
        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                20,
                10,
                0,
                20221212,
                1
            ),
             new PtDiseaseModel(
                20,
                0,
                1,
                20221010,
                1
            )
        };
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113034510121",
                10
            ),
             new OrdInfDetailModel(
                "113000910122",
                10
            )
        };

        var mockConfiguration = new Mock<IConfiguration>();
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        try
        {
            // Act
            var iagkutokusitu1 = medicalExaminationRepository.IgakuNanbyo(hpId, sinDate, hokenId, syosaisinKbn1, byomeiModelList, ordInfDetailModels, isJouhou);
            Assert.True(iagkutokusitu1.Count == 0);
            systemGenerationConf.Val = temp;
        }
        finally
        {
            tenant.RaiinInfs.RemoveRange(raiinInfs);
            tenant.OdrInfs.RemoveRange(odrInfs);
            tenant.OdrInfDetails.RemoveRange(odrInfDetails);
            tenant.SaveChanges();
            tenantTracking.SaveChanges();
        }
    }

    /// <summary>
    /// Check some ItemCd are IgakuNanbyo
    /// </summary>
    [Test]
    public void InitPriorityCheckDetail_043_IgakuNanbyo()
    {
        // Arrange
        var mockConfiguration = new Mock<IConfiguration>();
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        var checkOrders = new List<CheckedOrderModel>() {
            new CheckedOrderModel(
                    CheckingType.Order,
                    true,
                    "Checked Order Model",
                    "113002910",
                    1,
                    "Item 1",
                    1
                ),
            new CheckedOrderModel(
                    CheckingType.Order,
                    true,
                    "Checked Order Model",
                    "113001810",
                    1,
                    "Item 2",
                    1
                ),
            new CheckedOrderModel(
                    CheckingType.Order,
                    true,
                    "Checked Order Model",
                    "113002310",
                    1,
                    "Item 3",
                    1
                ),
              new CheckedOrderModel(
                    CheckingType.Order,
                    true,
                    "Checked Order Model",
                    "113002850",
                    1,
                    "Item 4",
                    1
                ),
            new CheckedOrderModel(
                    CheckingType.Order,
                    true,
                    "Checked Order Model",
                    "113000910",
                    1,
                    "Item 5",
                    1
                )
        };

        // Act
        checkOrders = medicalExaminationRepository.InitPriorityCheckDetail(checkOrders);

        //Assert
        Assert.False(checkOrders.Any(c => c.Santei && c.ItemCd != "113002910"));
    }

    /// <summary>
    /// Check ItemCd are IakuTenkan
    /// </summary>
    [Test]
    public void InitPriorityCheckDetail_044_IgakuTenkan()
    {
        // Arrange
        var mockConfiguration = new Mock<IConfiguration>();
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        var checkOrders = new List<CheckedOrderModel>() {
            new CheckedOrderModel(
                    CheckingType.Order,
                    true,
                    "Checked Order Model",
                    "113002850",
                    1,
                    "Item 1",
                    1
                ),    new CheckedOrderModel(
                    CheckingType.Order,
                    true,
                    "Checked Order Model",
                    "113001810",
                    1,
                    "Item 2",
                    1
                ),
            new CheckedOrderModel(
                    CheckingType.Order,
                    true,
                    "Checked Order Model",
                    "113002310",
                    1,
                    "Item 3",
                    1
                ),
              new CheckedOrderModel(
                    CheckingType.Order,
                    true,
                    "Checked Order Model",
                    "113000910",
                    1,
                    "Item 4",
                    1
                )
        };

        // Act
        checkOrders = medicalExaminationRepository.InitPriorityCheckDetail(checkOrders);

        //Assert
        Assert.False(checkOrders.Any(c => c.Santei && c.ItemCd != "113002850"));
    }

    /// <summary>
    /// Check some ItemCd are SihifuToku
    /// </summary>
    [Test]
    public void InitPriorityCheckDetail_045_SihifuToku1()
    {
        // Arrange
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        var checkOrders = new List<CheckedOrderModel>() {
            new CheckedOrderModel(
                    CheckingType.Order,
                    true,
                    "Checked Order Model",
                    "113000910",
                    1,
                    "Item 1",
                    1
                ),    new CheckedOrderModel(
                    CheckingType.Order,
                    true,
                    "Checked Order Model",
                    "113001810",
                    1,
                    "Item 2",
                    1
                ),
            new CheckedOrderModel(
                    CheckingType.Order,
                    true,
                    "Checked Order Model",
                    "113002310",
                    1,
                    "Item 3",
                    1
                )
        };

        // Act
        checkOrders = medicalExaminationRepository.InitPriorityCheckDetail(checkOrders);

        //Assert
        Assert.False(checkOrders.Any(c => c.Santei && c.ItemCd != "113000910"));
    }

    /// <summary>
    /// Check some ItemCd are IgakuTokusitu
    /// </summary>
    [Test]
    public void InitPriorityCheckDetail_046_IgakuTokusitu()
    {
        // Arrange
        var mockConfiguration = new Mock<IConfiguration>();
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        var checkOrders = new List<CheckedOrderModel>() {
            new CheckedOrderModel(
                    CheckingType.Order,
                    true,
                    "Checked Order Model",
                    "113001810",
                    1,
                    "Item 1",
                    1
                ),    new CheckedOrderModel(
                    CheckingType.Order,
                    true,
                    "Checked Order Model",
                    "113002310",
                    1,
                    "Item 2",
                    1
                )
        };

        // Act
        checkOrders = medicalExaminationRepository.InitPriorityCheckDetail(checkOrders);

        //Assert
        Assert.False(checkOrders.Any(c => c.Santei && c.ItemCd != "113001810"));
    }

    /// <summary>
    /// Check tikiHokatu
    /// </summary>
    [Test]
    public void ChikiHokatu_047_TikiHokatu()
    {
        //Arrange
        int hpId = 1, userId = 1, sinDate = 20220101, primaryDoctor = 1, tantoId = 1, syosaisinKbn = 1;
        long ptId = 1;
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "112021770",
                10
            ),
             new OrdInfDetailModel(
                "112017270",
                10
            ),
            new OrdInfDetailModel(
                "112021870",
                10
            ),
            new OrdInfDetailModel(
                "112017570",
                10
            )
        };

        // Act
        var checkModels = medicalExaminationRepository.ChikiHokatu(hpId, ptId, userId, sinDate, primaryDoctor, tantoId, ordInfDetailModels, syosaisinKbn);

        //Assert
        Assert.True(checkModels.Count == 0);
    }

    /// <summary>
    /// Check SyosaisinKbn
    /// </summary>
    [Test]
    public void ChikiHokatu_049_SyosaisinKbn()
    {
        //Arrange
        int hpId = 1, userId = 1, sinDate = 20220101, primaryDoctor = 1, tantoId = 1, syosaisinKbn = 1;
        long ptId = 1;
        var mockConfiguration = new Mock<IConfiguration>();
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "1120217701231",
                10
            ),
             new OrdInfDetailModel(
                "11201727011",
                10
            ),
            new OrdInfDetailModel(
                "112021870131",
                10
            ),
            new OrdInfDetailModel(
                "112017570131",
                10
            )
        };

        // Act
        var checkModels = medicalExaminationRepository.ChikiHokatu(hpId, ptId, userId, sinDate, primaryDoctor, tantoId, ordInfDetailModels, syosaisinKbn);

        //Assert
        Assert.True(checkModels.Count == 0);
    }

    [Test]
    public void ChikiHokatu_050_TiikiSantei()
    {
        //Arrange
        int randomKey = 35;
        var tenant = TenantProvider.GetNoTrackingDataContext();
        var ptSanteiConfs = CheckedOrderData.ReadPtSanteiConf(randomKey);
        tenant.PtSanteiConfs.AddRange(ptSanteiConfs);

        int hpId = 1, userId = 1, sinDate = 20220101, primaryDoctor = 1, tantoId = 1, syosaisinKbn = 3;
        long ptId = long.MaxValue;
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "1120217701231",
                10
            ),
             new OrdInfDetailModel(
                "11201727011",
                10
            ),
            new OrdInfDetailModel(
                "112021870131",
                10
            ),
            new OrdInfDetailModel(
                "112017570131",
                10
            )
        };

        try
        {
            tenant.SaveChanges();
            // Act
            var checkModels = medicalExaminationRepository.ChikiHokatu(hpId, ptId, userId, sinDate, primaryDoctor, tantoId, ordInfDetailModels, syosaisinKbn);
            //Assert
            Assert.True(checkModels.Count == 0);
        }
        finally
        {
            tenant.RemoveRange(ptSanteiConfs);
            tenant.SaveChanges();
        }
    }

    /// <summary>
    /// Check Primary doctor
    /// </summary>
    [Test]
    public void ChikiHokatu_051_PrimaryDoctor()
    {
        //Arrange
        int randomKey = 36;
        var tenant = TenantProvider.GetNoTrackingDataContext();
        var ptSanteiConfs = CheckedOrderData.ReadPtSanteiConfToNoCheckSantei(randomKey);
        tenant.PtSanteiConfs.AddRange(ptSanteiConfs);
        int hpId = 1, userId = 1, sinDate = 20220101, primaryDoctor = 0, tantoId = 1, syosaisinKbn = 3;
        long ptId = long.MaxValue;
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "1120217701231",
                10
            ),
             new OrdInfDetailModel(
                "11201727011",
                10
            ),
            new OrdInfDetailModel(
                "112021870131",
                10
            ),
            new OrdInfDetailModel(
                "112017570131",
                10
            )
        };
        try
        {
            tenant.SaveChanges();
            // Act
            var checkModels = medicalExaminationRepository.ChikiHokatu(hpId, ptId, userId, sinDate, primaryDoctor, tantoId, ordInfDetailModels, syosaisinKbn);
            //Assert
            Assert.True(checkModels.Count == 0);
        }
        finally
        {
            tenant.RemoveRange(ptSanteiConfs);
            tenant.SaveChanges();
        }
    }

    /// <summary>
    /// Check TantoId
    /// </summary>
    [Test]
    public void ChikiHokatu_052_TantoId()
    {
        //Arrange
        int randomKey = 37;
        var tenant = TenantProvider.GetNoTrackingDataContext();
        var ptSanteiConfs = CheckedOrderData.ReadPtSanteiConfToNoCheckSantei(randomKey);
        var users = CheckedOrderData.ReadUserMst(randomKey);
        tenant.PtSanteiConfs.AddRange(ptSanteiConfs);
        tenant.UserMsts.AddRange(users);
        int hpId = 1, userId = 99999, sinDate = 20110101, primaryDoctor = 2, tantoId = 1, syosaisinKbn = 3;
        long ptId = long.MaxValue;
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "114001610",
                14
            )
        };
        try
        {
            tenant.SaveChanges();
            // Act
            var checkModels = medicalExaminationRepository.ChikiHokatu(hpId, ptId, userId, sinDate, primaryDoctor, tantoId, ordInfDetailModels, syosaisinKbn);
            //Assert
            Assert.True(checkModels.Count == 0);
        }
        finally
        {
            tenant.RemoveRange(ptSanteiConfs);
            tenant.RemoveRange(users);
            tenant.SaveChanges();
        }
    }

    /// <summary>
    /// Check Oshin
    /// </summary>
    [Test]
    public void ChikiHokatu_053_Oshin()
    {
        //Arrange
        int randomKey = 38;
        var tenant = TenantProvider.GetNoTrackingDataContext();
        var ptSanteiConfs = CheckedOrderData.ReadPtSanteiConfToNoCheckSantei(randomKey);
        var users = CheckedOrderData.ReadUserMst(randomKey);
        tenant.PtSanteiConfs.AddRange(ptSanteiConfs);
        tenant.UserMsts.AddRange(users);

        int hpId = 1, userId = 99999, sinDate1 = 20110101, sinDate2 = 20180402, primaryDoctor = 1, tantoId = 1, syosaisinKbn = 3;
        long ptId = long.MaxValue;
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "114001610",
                14
            ),
             new OrdInfDetailModel(
                "114042810",
                14
            )
        };
        try
        {
            tenant.SaveChanges();
            // Act
            var checkModel1s = medicalExaminationRepository.ChikiHokatu(hpId, ptId, userId, sinDate1, primaryDoctor, tantoId, ordInfDetailModels, syosaisinKbn);
            var checkModel2s = medicalExaminationRepository.ChikiHokatu(hpId, ptId, userId, sinDate2, primaryDoctor, tantoId, ordInfDetailModels, syosaisinKbn);
            //Assert
            Assert.True(checkModel1s.Count == 0 && checkModel2s.Count == 0);
        }
        finally
        {
            tenant.RemoveRange(ptSanteiConfs);
            tenant.RemoveRange(users);
            tenant.SaveChanges();
        }
    }

    // [Test]
    // public void ChikiHokatu_054_JidoSantei()
    // {
    //     //Arrange
    //     int randomKey = 39;
    //     var tenant = TenantProvider.GetNoTrackingDataContext();
    //     var ptSanteiConfs = CheckedOrderData.ReadPtSanteiConfToNoCheckSantei(randomKey);
    //     var users = CheckedOrderData.ReadUserMst(randomKey);
    //     tenant.PtSanteiConfs.AddRange(ptSanteiConfs);
    //     tenant.UserMsts.AddRange(users);
    //     int hpId = 1, userId = 99999, sinDate = 20220402, primaryDoctor = 1, tantoId = 1, syosaisinKbn = 3;
    //     long ptId = long.MaxValue - randomKey;
    //     var mockConfiguration = new Mock<IConfiguration>();
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);
    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(
    //             "Y90298",
    //             1
    //         )
    //     };
    //     try
    //     {
    //         tenant.SaveChanges();
    //         // Act
    //         var checkModels = medicalExaminationRepository.ChikiHokatu(hpId, ptId, userId, sinDate, primaryDoctor, tantoId, ordInfDetailModels, syosaisinKbn);
    //         //Assert
    //         Assert.True(checkModels.Count > 0);
    //     }
    //     finally
    //     {
    //         tenant.RemoveRange(ptSanteiConfs);
    //         tenant.RemoveRange(users);
    //         tenant.SaveChanges();
    //     }
    // }

    // [Test]
    // public void ChikiHokatu_055_JidoSantei()
    // {
    //     //Arrange
    //     int randomKey = 39;
    //     var tenant = TenantProvider.GetTrackingTenantDataContext();
    //     var ptSanteiConfs = CheckedOrderData.ReadPtSanteiConfToNoCheckSantei(randomKey);
    //     var users = CheckedOrderData.ReadUserMst(randomKey);
    //     tenant.PtSanteiConfs.AddRange(ptSanteiConfs);
    //     tenant.UserMsts.AddRange(users);
    //     int hpId = 1, userId = 99999, sinDate = 20220402, primaryDoctor = 1, tantoId = 1, syosaisinKbn = 3;
    //     long ptId = long.MaxValue - randomKey;
    //     var systemConf = tenant.SystemConfs.FirstOrDefault(p => p.HpId == hpId
    //     && p.GrpCd == 4001
    //     && p.GrpEdaNo == 8);
    //     bool isUpdate = false;
    //     bool isCreate = false;
    //     double value = 0;
    //     if (systemConf == null)
    //     {
    //         systemConf = new SystemConf()
    //         {
    //             HpId = hpId,
    //             GrpCd = 4001,
    //             GrpEdaNo = 8,
    //             Val = 1
    //         };
    //         tenant.Add(systemConf);
    //         isCreate = true;
    //     }
    //     else if (systemConf != null && systemConf.Val != 1)
    //     {
    //         value = systemConf.Val;
    //         systemConf.Val = 1;
    //         isUpdate = true;
    //     }
    //     var mockConfiguration = new Mock<IConfiguration>();
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);
    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(
    //             "Y90298",
    //             1
    //         )
    //     };
    //     try
    //     {
    //         tenant.SaveChanges();
    //         // Act
    //         var checkModels = medicalExaminationRepository.ChikiHokatu(hpId, ptId, userId, sinDate, primaryDoctor, tantoId, ordInfDetailModels, syosaisinKbn);
    //         //Assert
    //         Assert.True(checkModels.Count > 0);
    //     }
    //     finally
    //     {
    //         if (isUpdate)
    //         {
    //             systemConf.Val = value;
    //             systemConf.CreateDate = systemConf.CreateDate.ToUniversalTime();
    //             systemConf.UpdateDate = systemConf.UpdateDate.ToUniversalTime();
    //             tenant.Update(systemConf);
    //         }
    //         else if (isCreate)
    //         {
    //             tenant.Remove(systemConf);
    //         }
    //         tenant.RemoveRange(ptSanteiConfs);
    //         tenant.RemoveRange(users);
    //         tenant.SaveChanges();
    //     }
    // }

    // /// <summary>
    // ///check ninTiikiSanteiConf
    // /// </summary>
    // [Test]
    // public void ChikiHokatu_056_JidoSantei()
    // {
    //     //Arrange
    //     int randomKey = 39;
    //     var tenant = TenantProvider.GetTrackingTenantDataContext();
    //     var ptSanteiConfs = CheckedOrderData.ReadPtSanteiConfToNoCheckSantei(randomKey);
    //     foreach (var i in ptSanteiConfs)
    //     {
    //         i.EdaNo = 2;
    //     }
    //     var users = CheckedOrderData.ReadUserMst(randomKey);
    //     tenant.PtSanteiConfs.AddRange(ptSanteiConfs);
    //     tenant.UserMsts.AddRange(users);
    //     int hpId = 1, userId = 99999, sinDate = 20220402, primaryDoctor = 1, tantoId = 1, syosaisinKbn = 3;
    //     long ptId = long.MaxValue - randomKey;
    //     var systemConf = tenant.SystemConfs.FirstOrDefault(p => p.HpId == hpId
    //     && p.GrpCd == 4001
    //     && p.GrpEdaNo == 8);
    //     bool isUpdate = false;
    //     bool isCreate = false;
    //     double value = 0;
    //     if (systemConf == null)
    //     {
    //         systemConf = new SystemConf()
    //         {
    //             HpId = hpId,
    //             GrpCd = 4001,
    //             GrpEdaNo = 8,
    //             Val = 1
    //         };
    //         tenant.Add(systemConf);
    //         isCreate = true;
    //     }
    //     else if (systemConf != null && systemConf.Val != 1)
    //     {
    //         value = systemConf.Val;
    //         systemConf.Val = 1;
    //         isUpdate = true;
    //     }
    //     var mockConfiguration = new Mock<IConfiguration>();
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);
    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(
    //             "Y90298",
    //             1
    //         )
    //     };
    //     try
    //     {
    //         tenant.SaveChanges();
    //         // Act
    //         var checkModels = medicalExaminationRepository.ChikiHokatu(hpId, ptId, userId, sinDate, primaryDoctor, tantoId, ordInfDetailModels, syosaisinKbn);
    //         //Assert
    //         Assert.True(checkModels.Count > 0);
    //     }
    //     finally
    //     {
    //         if (isUpdate)
    //         {
    //             systemConf.Val = value;
    //             systemConf.CreateDate = systemConf.CreateDate.ToUniversalTime();
    //             systemConf.UpdateDate = systemConf.UpdateDate.ToUniversalTime();
    //             tenant.Update(systemConf);
    //         }
    //         else if (isCreate)
    //         {
    //             tenant.Remove(systemConf);
    //         }
    //         tenant.RemoveRange(ptSanteiConfs);
    //         tenant.RemoveRange(users);
    //         tenant.SaveChanges();
    //     }
    // }

    // /// <summary>
    // ///check ninTiikiSanteiConf
    // /// </summary>
    // [Test]
    // public void ChikiHokatu_057_JidoSantei()
    // {
    //     //Arrange
    //     int randomKey = 39;
    //     var tenant = TenantProvider.GetTrackingTenantDataContext();
    //     var ptSanteiConfs = CheckedOrderData.ReadPtSanteiConfToNoCheckSantei(randomKey);
    //     foreach (var i in ptSanteiConfs)
    //     {
    //         i.EdaNo = 2;
    //     }
    //     var users = CheckedOrderData.ReadUserMst(randomKey);
    //     tenant.PtSanteiConfs.AddRange(ptSanteiConfs);
    //     tenant.UserMsts.AddRange(users);
    //     int hpId = 1, userId = 99999, sinDate = 20220402, primaryDoctor = 1, tantoId = 1, syosaisinKbn = 3;
    //     long ptId = long.MaxValue - randomKey;
    //     var systemConf = tenant.SystemConfs.FirstOrDefault(p => p.HpId == hpId
    //     && p.GrpCd == 4001
    //     && p.GrpEdaNo == 8);
    //     bool isUpdate = false;
    //     bool isCreate = false;
    //     double value = 0;
    //     if (systemConf == null)
    //     {
    //         systemConf = new SystemConf()
    //         {
    //             HpId = hpId,
    //             GrpCd = 4001,
    //             GrpEdaNo = 8,
    //             Val = 10
    //         };
    //         tenant.Add(systemConf);
    //         isCreate = true;
    //     }
    //     else if (systemConf != null && systemConf.Val != 0)
    //     {
    //         value = systemConf.Val;
    //         systemConf.Val = 0;
    //         isUpdate = true;
    //     }
    //     var mockConfiguration = new Mock<IConfiguration>();
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);
    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(
    //             "Y90298",
    //             1
    //         )
    //     };
    //     try
    //     {
    //         tenant.SaveChanges();
    //         // Act
    //         var checkModels = medicalExaminationRepository.ChikiHokatu(hpId, ptId, userId, sinDate, primaryDoctor, tantoId, ordInfDetailModels, syosaisinKbn);
    //         //Assert
    //         Assert.True(checkModels.Count > 0);
    //     }
    //     finally
    //     {
    //         if (isUpdate)
    //         {
    //             systemConf.Val = value;
    //             systemConf.CreateDate = systemConf.CreateDate.ToUniversalTime();
    //             systemConf.UpdateDate = systemConf.UpdateDate.ToUniversalTime();
    //             tenant.Update(systemConf);
    //         }
    //         else if (isCreate)
    //         {
    //             tenant.Remove(systemConf);
    //         }
    //         tenant.RemoveRange(ptSanteiConfs);
    //         tenant.RemoveRange(users);
    //         tenant.SaveChanges();
    //     }
    // }

    [Test]
    public void ChikiHokatu_058_PrimaryDoctor()
    {
        //Arrange
        int randomKey = 39;
        var tenant = TenantProvider.GetTrackingTenantDataContext();
        var ptSanteiConfs = CheckedOrderData.ReadPtSanteiConfToNoCheckSantei(randomKey);
        foreach (var i in ptSanteiConfs)
        {
            i.EdaNo = 2;
        }
        var users = CheckedOrderData.ReadUserMst(randomKey);
        tenant.PtSanteiConfs.AddRange(ptSanteiConfs);
        tenant.UserMsts.AddRange(users);
        int hpId = 1, userId = 99999, sinDate = 20220402, primaryDoctor = 0, tantoId = 1, syosaisinKbn = 3;
        long ptId = long.MaxValue - randomKey;
        var systemConf = tenant.SystemConfs.FirstOrDefault(p => p.HpId == hpId
        && p.GrpCd == 4001
        && p.GrpEdaNo == 8);
        bool isUpdate = false;
        bool isCreate = false;
        double value = 0;
        if (systemConf == null)
        {
            systemConf = new SystemConf()
            {
                HpId = hpId,
                GrpCd = 4001,
                GrpEdaNo = 8,
                Val = 10
            };
            tenant.Add(systemConf);
            isCreate = true;
        }
        else if (systemConf != null && systemConf.Val != 0)
        {
            value = systemConf.Val;
            systemConf.Val = 0;
            isUpdate = true;
        }
        var mockConfiguration = new Mock<IConfiguration>();
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "Y90298",
                1
            )
        };
        try
        {
            tenant.SaveChanges();
            // Act
            var checkModels = medicalExaminationRepository.ChikiHokatu(hpId, ptId, userId, sinDate, primaryDoctor, tantoId, ordInfDetailModels, syosaisinKbn);
            //Assert
            Assert.True(checkModels.Count == 0);
        }
        finally
        {
            if (isUpdate)
            {
                systemConf.Val = value;
                systemConf.CreateDate = systemConf.CreateDate.ToUniversalTime();
                systemConf.UpdateDate = systemConf.UpdateDate.ToUniversalTime();
                tenant.Update(systemConf);
            }
            else if (isCreate)
            {
                tenant.Remove(systemConf);
            }
            tenant.RemoveRange(ptSanteiConfs);
            tenant.RemoveRange(users);
            tenant.SaveChanges();
        }
    }

    /// <summary>
    /// Check Sname null, primaryDoctor != tantoId
    /// </summary>
    [Test]
    public void ChikiHokatu_059_CHeckSName()
    {
        //Arrange
        int randomKey = 39;
        var tenant = TenantProvider.GetTrackingTenantDataContext();
        var ptSanteiConfs = CheckedOrderData.ReadPtSanteiConfToNoCheckSantei(randomKey);
        var users = CheckedOrderData.ReadUserMst(randomKey);
        tenant.PtSanteiConfs.AddRange(ptSanteiConfs);
        tenant.UserMsts.AddRange(users);
        int hpId = 1, userId = -99999, userId2 = 99999, sinDate = 20220402, primaryDoctor = 1, tantoId = 1, tantoId2 = 2, syosaisinKbn = 3;
        long ptId = long.MaxValue - randomKey;
        var systemConf = tenant.SystemConfs.FirstOrDefault(p => p.HpId == hpId
        && p.GrpCd == 4001
        && p.GrpEdaNo == 8);
        bool isUpdate = false;
        bool isCreate = false;
        double value = 0;
        if (systemConf == null)
        {
            systemConf = new SystemConf()
            {
                HpId = hpId,
                GrpCd = 4001,
                GrpEdaNo = 8,
                Val = 10
            };
            tenant.Add(systemConf);
            isCreate = true;
        }
        else if (systemConf != null && systemConf.Val != 0)
        {
            value = systemConf.Val;
            systemConf.Val = 0;
            isUpdate = true;
        }
        var mockConfiguration = new Mock<IConfiguration>();
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "Y90298",
                1
            )
        };
        try
        {
            tenant.SaveChanges();
            // Act
            var checkModels = medicalExaminationRepository.ChikiHokatu(hpId, ptId, userId, sinDate, primaryDoctor, tantoId, ordInfDetailModels, syosaisinKbn);
            var checkModels2 = medicalExaminationRepository.ChikiHokatu(hpId, ptId, userId2, sinDate, primaryDoctor, tantoId2, ordInfDetailModels, syosaisinKbn);
            //Assert
            Assert.True(checkModels.Count == 0 && checkModels2.Count == 0);
        }
        finally
        {
            if (isUpdate)
            {
                systemConf.Val = value;
                systemConf.CreateDate = systemConf.CreateDate.ToUniversalTime();
                systemConf.UpdateDate = systemConf.UpdateDate.ToUniversalTime();
                tenant.Update(systemConf);
            }
            else if (isCreate)
            {
                tenant.Remove(systemConf);
            }
            tenant.RemoveRange(ptSanteiConfs);
            tenant.RemoveRange(users);
            tenant.SaveChanges();
        }
    }

    /// <summary>
    /// check TenMst
    /// </summary>
    [Test]
    public void ChikiHokatu_060_OshinDetails()
    {
        //Arrange
        int randomKey = 39;
        var tenant = TenantProvider.GetTrackingTenantDataContext();
        var ptSanteiConfs = CheckedOrderData.ReadPtSanteiConfToNoCheckSantei(randomKey);
        var users = CheckedOrderData.ReadUserMst(randomKey);
        var tenMsts = CheckedOrderData.ReadTenMst();
        tenant.PtSanteiConfs.AddRange(ptSanteiConfs);
        tenant.UserMsts.AddRange(users);
        tenant.TenMsts.AddRange(tenMsts);
        int hpId = 1, userId = 99999, sinDate = 20220402, primaryDoctor = 1, tantoId = 1, syosaisinKbn = 3;
        long ptId = long.MaxValue - randomKey;
        var systemConf = tenant.SystemConfs.FirstOrDefault(p => p.HpId == hpId
        && p.GrpCd == 4001
        && p.GrpEdaNo == 8);
        bool isUpdate = false;
        bool isCreate = false;
        double value = 0;
        if (systemConf == null)
        {
            systemConf = new SystemConf()
            {
                HpId = hpId,
                GrpCd = 4001,
                GrpEdaNo = 8,
                Val = 10
            };
            tenant.Add(systemConf);
            isCreate = true;
        }
        else if (systemConf != null && systemConf.Val != 0)
        {
            value = systemConf.Val;
            systemConf.Val = 0;
            isUpdate = true;
        }
        var mockConfiguration = new Mock<IConfiguration>();
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        var ordInfDetailModels1 = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "Y90298",
                14
            )
        };
        var ordInfDetailModels2 = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "Y90299",
                14
            )
        };
        try
        {
            tenant.SaveChanges();
            // Act
            var checkModels = medicalExaminationRepository.ChikiHokatu(hpId, ptId, userId, sinDate, primaryDoctor, tantoId, ordInfDetailModels1, syosaisinKbn);
            var checkModels2 = medicalExaminationRepository.ChikiHokatu(hpId, ptId, userId, sinDate, primaryDoctor, tantoId, ordInfDetailModels2, syosaisinKbn);
            //Assert
            Assert.True(checkModels.Count == 0 && checkModels2.Count == 0);
        }
        finally
        {
            if (isUpdate)
            {
                systemConf.Val = value;
                systemConf.CreateDate = systemConf.CreateDate.ToUniversalTime();
                systemConf.UpdateDate = systemConf.UpdateDate.ToUniversalTime();
                tenant.Update(systemConf);
            }
            else if (isCreate)
            {
                tenant.Remove(systemConf);
            }
            tenant.RemoveRange(ptSanteiConfs);
            tenant.RemoveRange(users);
            tenant.RemoveRange(tenMsts);
            tenant.SaveChanges();
        }
    }

    /// <summary>
    /// Check some ItemCd are Yakkuzai
    /// </summary>
    [Test]
    public void YakkuZai_061_Item()
    {
        //Arrange
        int hpId = 1, birthDay = 20, sinDate = 20220402;
        long ptId = long.MaxValue;
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "120002370",
                1
            )
        };

        // Act
        var checkModels = medicalExaminationRepository.YakkuZai(hpId, ptId, sinDate, birthDay, ordInfDetailModels, new());

        //Assert
        Assert.True(checkModels.Count == 0);
    }

    [Test]
    public void YakkuZai_062_IsDrug()
    {
        //Arrange
        int hpId = 1, birthDay = 20, sinDate = 20220402;
        long ptId = long.MaxValue;
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "12000237012",
                1
            )
        };
        var ordInfs = new List<OrdInfModel>() {
            new OrdInfModel(
                0,
                25,
                new()
                )
        };
        // Act
        var checkModels = medicalExaminationRepository.YakkuZai(hpId, ptId, sinDate, birthDay, ordInfDetailModels, ordInfs);

        //Assert
        Assert.True(checkModels.Count == 0);
    }

    // /// <summary>
    // /// Check Age
    // /// </summary>
    // [Test]
    // public void YakkuZai_063_Age()
    // {
    //     //Arrange
    //     int hpId = 1, birthDay = CIUtil.DateTimeToInt(DateTime.UtcNow.AddYears(-1)), sinDate = CIUtil.DateTimeToInt(DateTime.UtcNow);
    //     long ptId = long.MaxValue;
    //     var tenantTracking = TenantProvider.GetTrackingTenantDataContext();

    //     var systemGenerationConf = tenantTracking.SystemGenerationConfs.FirstOrDefault(p => p.HpId == 1
    //       && p.GrpCd == 8001
    //       && p.GrpEdaNo == 1
    //       && p.StartDate <= sinDate
    //       && p.EndDate >= sinDate);
    //     var temp = systemGenerationConf?.Val ?? 0;
    //     if (systemGenerationConf != null) systemGenerationConf.Val = 1;
    //     else
    //     {
    //         systemGenerationConf = new SystemGenerationConf
    //         {
    //             HpId = 1,
    //             GrpCd = 8001,
    //             GrpEdaNo = 1,
    //             StartDate = 0,
    //             EndDate = 99999999,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 1
    //         };
    //         tenantTracking.SystemGenerationConfs.Add(systemGenerationConf);
    //     }

    //     var autoSanteiCheck = tenantTracking.AutoSanteiMsts.Any(e =>
    //              e.HpId == hpId &&
    //              e.ItemCd == "113003510" &&
    //              e.StartDate <= sinDate &&
    //              e.EndDate >= sinDate);
    //     var autoSanteiMst = new AutoSanteiMst
    //     {
    //         HpId = 1,
    //         ItemCd = "113003510",
    //         SeqNo = 1,
    //         StartDate = 0,
    //         EndDate = 99999999,
    //         CreateId = 1,
    //         CreateDate = DateTime.UtcNow,
    //         UpdateId = 1
    //     };
    //     if (!autoSanteiCheck)
    //     {
    //         tenantTracking.Add(
    //             autoSanteiMst
    //        );

    //     }


    //     var mockConfiguration = new Mock<IConfiguration>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);
    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(
    //             "12000237012",
    //             1
    //         )
    //     };
    //     var ordInfs = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             0,
    //             21,
    //             new()
    //             )
    //     };
    //     try
    //     {
    //         tenantTracking.SaveChanges();
    //         // Act
    //         var checkModels = medicalExaminationRepository.YakkuZai(hpId, ptId, sinDate, birthDay, ordInfDetailModels, ordInfs);
    //         //Assert
    //         Assert.True(checkModels.Count == 0);
    //     }
    //     finally
    //     {
    //         systemGenerationConf.Val = temp;
    //         if (autoSanteiMst.Id > 0) tenantTracking.RemoveRange(autoSanteiMst);
    //         tenantTracking.SaveChanges();
    //     }


    // }

    /// <summary>
    /// Check YakuzaiJoho
    /// </summary>
    [Test]
    public void YakkuZai_064_YakuzaiJoho()
    {
        //Arrange
        int hpId = 1, birthDay = 19900101, sinDate = 999999999;
        long ptId = long.MaxValue;
        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();

        var systemGenerationConf = tenantTracking.SystemGenerationConfs.FirstOrDefault(p => p.HpId == 1
          && p.GrpCd == 8001
          && p.GrpEdaNo == 1
          && p.StartDate <= sinDate
          && p.EndDate >= sinDate);
        var temp = systemGenerationConf?.Val ?? 0;
        if (systemGenerationConf != null) systemGenerationConf.Val = 1;
        else
        {
            systemGenerationConf = new SystemGenerationConf
            {
                HpId = 1,
                GrpCd = 8001,
                GrpEdaNo = 1,
                StartDate = 0,
                EndDate = 99999999,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenantTracking.SystemGenerationConfs.Add(systemGenerationConf);
        }

        var autoSanteiCheck = tenantTracking.AutoSanteiMsts.Any(e =>
                 e.HpId == hpId &&
                 e.ItemCd == "113003510" &&
                 e.StartDate <= sinDate &&
                 e.EndDate >= sinDate);
        var autoSanteiMst = new AutoSanteiMst
        {
            HpId = 1,
            ItemCd = "113003510",
            SeqNo = 1,
            StartDate = 0,
            EndDate = 99999999,
            CreateId = 1,
            CreateDate = DateTime.UtcNow,
            UpdateId = 1
        };
        if (!autoSanteiCheck)
        {
            tenantTracking.Add(
                autoSanteiMst
           );

        }


        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "12000237012",
                1
            )
        };
        var ordInfs = new List<OrdInfModel>() {
            new OrdInfModel(
                0,
                21,
                new()
                )
        };
        try
        {
            tenantTracking.SaveChanges();
            // Act
            var checkModels = medicalExaminationRepository.YakkuZai(hpId, ptId, sinDate, birthDay, ordInfDetailModels, ordInfs);
            //Assert
            Assert.True(checkModels.Count == 0);
        }
        finally
        {
            systemGenerationConf.Val = temp;
            tenantTracking.RemoveRange(autoSanteiMst);
            tenantTracking.SaveChanges();
        }
    }

    /// <summary>
    /// Check YakuzaiJohoTeiyo
    /// </summary>
    [Test]
    public void YakkuZai_065_YakuzaiJohoTeiyo()
    {
        //Arrange
        int hpId = 1, birthDay = 19900101, sinDate = 999999999;
        long ptId = long.MaxValue;
        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();

        var systemGenerationConf = tenantTracking.SystemGenerationConfs.FirstOrDefault(p => p.HpId == 1
          && p.GrpCd == 8001
          && p.GrpEdaNo == 1
          && p.StartDate <= sinDate
          && p.EndDate >= sinDate);
        var temp = systemGenerationConf?.Val ?? 0;
        if (systemGenerationConf != null) systemGenerationConf.Val = 1;
        else
        {
            systemGenerationConf = new SystemGenerationConf
            {
                HpId = 1,
                GrpCd = 8001,
                GrpEdaNo = 1,
                StartDate = 0,
                EndDate = 99999999,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenantTracking.SystemGenerationConfs.Add(systemGenerationConf);
        }

        var autoSanteiCheck = tenantTracking.AutoSanteiMsts.Any(e =>
                 e.HpId == hpId &&
                 e.ItemCd == "113003510" &&
                 e.StartDate <= sinDate &&
                 e.EndDate >= sinDate);
        var autoSanteiMst = new AutoSanteiMst
        {
            HpId = 1,
            ItemCd = "113003510",
            SeqNo = 1,
            StartDate = 0,
            EndDate = 99999999,
            CreateId = 1,
            CreateDate = DateTime.UtcNow,
            UpdateId = 1
        };
        if (!autoSanteiCheck)
        {
            tenantTracking.Add(
                autoSanteiMst
           );

        }



        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113701310",
                1
            )
        };
        var ordInfs = new List<OrdInfModel>() {
            new OrdInfModel(
                0,
                21,
                new()
                )
        };
        try
        {
            tenantTracking.SaveChanges();
            // Act
            var checkModels = medicalExaminationRepository.YakkuZai(hpId, ptId, sinDate, birthDay, ordInfDetailModels, ordInfs);
            //Assert
            Assert.True(checkModels.Count == 0);
        }
        finally
        {
            systemGenerationConf.Val = temp;
            tenantTracking.RemoveRange(autoSanteiMst);
            tenantTracking.SaveChanges();
        }

    }


    /// <summary>
    /// Check Exist message which are returned
    /// </summary>
    [Test]
    public void YakkuZai_066_ExistMessage()
    {
        //Arrange
        int hpId = 1, birthDay = 19900101, sinDate = 21000101;
        long ptId = long.MaxValue;
        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
        var systemGenerationConf = tenantTracking.SystemGenerationConfs.FirstOrDefault(p => p.HpId == 1
          && p.GrpCd == 8001
          && p.GrpEdaNo == 1
          && p.StartDate <= sinDate
          && p.EndDate >= sinDate);
        var temp = systemGenerationConf?.Val ?? 0;
        if (systemGenerationConf != null) systemGenerationConf.Val = 1;
        else
        {
            systemGenerationConf = new SystemGenerationConf
            {
                HpId = 1,
                GrpCd = 8001,
                GrpEdaNo = 1,
                StartDate = 0,
                EndDate = 99999999,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenantTracking.SystemGenerationConfs.Add(systemGenerationConf);
        }

        var autoSanteiCheck = tenantTracking.AutoSanteiMsts.Any(e =>
                 e.HpId == hpId &&
                 e.ItemCd == "113003510" &&
                 e.StartDate <= sinDate &&
                 e.EndDate >= sinDate);
        var autoSanteiMst = new AutoSanteiMst
        {
            HpId = 1,
            ItemCd = "113003510",
            SeqNo = 1,
            StartDate = 0,
            EndDate = 99999999,
            CreateId = 1,
            CreateDate = DateTime.UtcNow,
            UpdateId = 1
        };
        if (!autoSanteiCheck)
        {
            tenantTracking.Add(
                autoSanteiMst
           );

        }


        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113701310",
                1
            )
        };
        var ordInfs = new List<OrdInfModel>() {
            new OrdInfModel(
                0,
                21,
                new()
                )
        };
        try
        {
            tenantTracking.SaveChanges();
            // Act
            var checkModels = medicalExaminationRepository.YakkuZai(hpId, ptId, sinDate, birthDay, ordInfDetailModels, ordInfs);
            //Assert
            Assert.True(checkModels.Count == 2);
        }
        finally
        {
            systemGenerationConf.Val = temp;
            if (autoSanteiMst.Id > 0) tenantTracking.RemoveRange(autoSanteiMst);
            tenantTracking.SaveChanges();
        }
    }

    [Test]
    public void YakkuZai_066_2_ExistMessage()
    {
        //Arrange
        int hpId = 1, birthDay = 19900101, sinDate = 21000101;
        long ptId = long.MaxValue;
        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
        var systemGenerationConf = tenantTracking.SystemGenerationConfs.FirstOrDefault(p => p.HpId == 1
          && p.GrpCd == 8001
          && p.GrpEdaNo == 1
          && p.StartDate <= sinDate
          && p.EndDate >= sinDate);
        var systemConf = tenantTracking.SystemConfs.FirstOrDefault(i => i.HpId == hpId && i.GrpCd == 4001 && i.GrpEdaNo == 5);
        var tempSystemConf = systemConf?.Val ?? 0;
        var temp = systemGenerationConf?.Val ?? 0;
        if (systemGenerationConf != null) systemGenerationConf.Val = 1;
        else
        {
            systemGenerationConf = new SystemGenerationConf
            {
                HpId = 1,
                GrpCd = 8001,
                GrpEdaNo = 1,
                StartDate = 0,
                EndDate = 99999999,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenantTracking.SystemGenerationConfs.Add(systemGenerationConf);
        }
        if (systemConf != null) systemConf.Val = 2;
        else
        {
            systemConf = new SystemConf
            {
                HpId = hpId,
                GrpCd = 4001,
                GrpEdaNo = 5,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 2
            };
            tenantTracking.SystemGenerationConfs.Add(systemGenerationConf);
        }
        var autoSanteiCheck = tenantTracking.AutoSanteiMsts.Any(e =>
                 e.HpId == hpId &&
                 e.ItemCd == "113003510" &&
                 e.StartDate <= sinDate &&
                 e.EndDate >= sinDate);
        var autoSanteiMst = new AutoSanteiMst
        {
            HpId = 1,
            ItemCd = "113003510",
            SeqNo = 1,
            StartDate = 0,
            EndDate = 99999999,
            CreateId = 1,
            CreateDate = DateTime.UtcNow,
            UpdateId = 1
        };
        if (!autoSanteiCheck)
        {
            tenantTracking.Add(
                autoSanteiMst
           );

        }


        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113701310",
                1
            )
        };
        var ordInfs = new List<OrdInfModel>() {
            new OrdInfModel(
                0,
                21,
                new()
                )
        };
        try
        {
            tenantTracking.SaveChanges();
            // Act
            var checkModels = medicalExaminationRepository.YakkuZai(hpId, ptId, sinDate, birthDay, ordInfDetailModels, ordInfs);
            //Assert
            Assert.True(checkModels.Count == 2);
        }
        finally
        {
            systemGenerationConf.Val = temp;
            systemConf.Val = temp;
            if (autoSanteiMst.Id > 0) tenantTracking.RemoveRange(autoSanteiMst);
            tenantTracking.SaveChanges();
        }
    }

    /// <summary>
    /// Check Some ItemCd are SiIkuji
    /// </summary>
    [Test]
    public void SiIkuji_067_Item()
    {
        //Arrange
        int hpId = 1, birthDay = 19900101, sinDate = 21000101;

        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "111000470",
                1
            ),
            new OrdInfDetailModel(
                "113037110",
                1
            )
        };

        // Act
        var checkModels = medicalExaminationRepository.SiIkuji(hpId, sinDate, birthDay, ordInfDetailModels, true, 1);

        //Assert
        Assert.True(checkModels.Count == 0);
    }

    /// <summary>
    /// Check isjouhou
    /// </summary>
    [Test]
    public void SiIkuji_068_IsJouhou()
    {
        //Arrange
        int hpId = 1, birthDay = 19900101, sinDate1 = 20220330, sinDate2 = 999999999;

        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "**********",
                1
            ),
            new OrdInfDetailModel(
                "**********",
                1
            )
        };

        // Act
        var checkModel1s = medicalExaminationRepository.SiIkuji(hpId, sinDate1, birthDay, ordInfDetailModels, true, 1);
        var checkModel2s = medicalExaminationRepository.SiIkuji(hpId, sinDate2, birthDay, ordInfDetailModels, true, 1);
        var checkModel3s = medicalExaminationRepository.SiIkuji(hpId, sinDate2, birthDay, ordInfDetailModels, false, 1);
        //Assert
        Assert.True(checkModel1s.Count == 0 && checkModel2s.Count == 0 && checkModel3s.Count == 0);
    }

    /// <summary>
    /// Check shonika
    /// </summary>
    [Test]
    public void SiIkuji_069_Shonika()
    {
        //Arrange
        int hpId = 1, birthDay = 19900101, sinDate = 21000101;
        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();

        var systemGenerationConf = tenantTracking.SystemGenerationConfs.FirstOrDefault(p => p.HpId == 1
          && p.GrpCd == 8001
          && p.GrpEdaNo == 0
          && p.StartDate <= sinDate
          && p.EndDate >= sinDate);
        var temp = systemGenerationConf?.Val ?? 0;
        if (systemGenerationConf != null) systemGenerationConf.Val = 0;
        else
        {
            systemGenerationConf = new SystemGenerationConf
            {
                HpId = 1,
                GrpCd = 8001,
                GrpEdaNo = 0,
                StartDate = 0,
                EndDate = 99999999,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 0
            };
            tenantTracking.SystemGenerationConfs.Add(systemGenerationConf);
        }


        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "113701310",
                1
            )
        };
        try
        {
            tenantTracking.SaveChanges();
            // Act
            var checkModels = medicalExaminationRepository.SiIkuji(hpId, sinDate, birthDay, ordInfDetailModels, true, 1);
            //Assert
            Assert.True(checkModels.Count == 0);
        }
        finally
        {
            systemGenerationConf.Val = temp;
            tenantTracking.SaveChanges();
        }
    }

    /// <summary>
    /// Check autosantei
    /// </summary>
    [Test]
    public void SiIkuji_070_AutoSantei()
    {
        //Arrange
        int hpId = 1, birthDay = 19900101, sinDate = 21000101;
        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();

        var systemGenerationConf = tenantTracking.SystemGenerationConfs.FirstOrDefault(p => p.HpId == 1
          && p.GrpCd == 8001
          && p.GrpEdaNo == 0
          && p.StartDate <= sinDate
          && p.EndDate >= sinDate);
        var temp = systemGenerationConf?.Val ?? 0;
        if (systemGenerationConf != null) systemGenerationConf.Val = 1;
        else
        {
            systemGenerationConf = new SystemGenerationConf
            {
                HpId = 1,
                GrpCd = 8001,
                GrpEdaNo = 0,
                StartDate = 0,
                EndDate = 99999999,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenantTracking.SystemGenerationConfs.Add(systemGenerationConf);
        }

        var autoSanteiCheck = tenantTracking.AutoSanteiMsts.Any(e =>
                 e.HpId == hpId &&
                 e.ItemCd == "113003510" &&
                 e.StartDate <= sinDate &&
                 e.EndDate >= sinDate);
        var autoSanteiMst = new AutoSanteiMst
        {
            HpId = 1,
            ItemCd = "113003510",
            SeqNo = 1,
            StartDate = 0,
            EndDate = 99999999,
            CreateId = 1,
            CreateDate = DateTime.UtcNow,
            UpdateId = 1
        };
        if (!autoSanteiCheck)
        {
            tenantTracking.Add(
                autoSanteiMst
           );

        }


        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "12000237012",
                1
            )
        };
        try
        {
            tenantTracking.SaveChanges();
            // Act
            var checkModels = medicalExaminationRepository.SiIkuji(hpId, sinDate, birthDay, ordInfDetailModels, true, 1);
            //Assert
            Assert.True(checkModels.Count == 0);
        }
        finally
        {
            systemGenerationConf.Val = temp;
            if (autoSanteiMst.Id > 0) tenantTracking.RemoveRange(autoSanteiMst);
            tenantTracking.SaveChanges();
        }
    }

    /// <summary>
    /// Check Age
    /// </summary>
    [Test]
    public void SiIkuji_071_Age()
    {
        //Arrange
        int hpId = 1, birthDay = 21000101, sinDate = 21100101;
        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();

        var systemGenerationConf = tenantTracking.SystemGenerationConfs.FirstOrDefault(p => p.HpId == 1
          && p.GrpCd == 8001
          && p.GrpEdaNo == 0
          && p.StartDate <= sinDate
          && p.EndDate >= sinDate);
        var temp = systemGenerationConf?.Val ?? 0;
        if (systemGenerationConf != null) systemGenerationConf.Val = 1;
        else
        {
            systemGenerationConf = new SystemGenerationConf
            {
                HpId = 1,
                GrpCd = 8001,
                GrpEdaNo = 0,
                StartDate = 0,
                EndDate = 99999999,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenantTracking.SystemGenerationConfs.Add(systemGenerationConf);
        }

        var autoSanteis = tenantTracking.AutoSanteiMsts.Where(e =>
                 e.HpId == hpId &&
                 e.ItemCd == "113003510" &&
                 e.StartDate <= sinDate &&
                 e.EndDate >= sinDate).ToList();

        if (autoSanteis.Count > 0)
        {
            tenantTracking.RemoveRange(autoSanteis);
        }



        var mockConfiguration = new Mock<IConfiguration>();
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "12000237012",
                1
            )
        };
        try
        {
            tenantTracking.SaveChanges();
            // Act
            var checkModels = medicalExaminationRepository.SiIkuji(hpId, sinDate, birthDay, ordInfDetailModels, true, 1);
            //Assert
            Assert.True(checkModels.Count == 0);
        }
        finally
        {
            systemGenerationConf.Val = temp;
            if (autoSanteis.Count > 0) tenantTracking.AddRange(autoSanteis);
            tenantTracking.SaveChanges();
        }

    }

    /// <summary>
    /// Check Syosin
    /// </summary>
    [Test]
    public void SiIkuji_072_Syosin()
    {
        //Arrange
        int hpId = 1, birthDay = 21000101, sinDate = 21000101;
        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();

        var systemGenerationConf = tenantTracking.SystemGenerationConfs.FirstOrDefault(p => p.HpId == 1
          && p.GrpCd == 8001
          && p.GrpEdaNo == 0
          && p.StartDate <= sinDate
          && p.EndDate >= sinDate);
        var temp = systemGenerationConf?.Val ?? 0;
        if (systemGenerationConf != null) systemGenerationConf.Val = 1;
        else
        {
            systemGenerationConf = new SystemGenerationConf
            {
                HpId = 1,
                GrpCd = 8001,
                GrpEdaNo = 0,
                StartDate = 0,
                EndDate = 99999999,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenantTracking.SystemGenerationConfs.Add(systemGenerationConf);
        }

        var autoSanteis = tenantTracking.AutoSanteiMsts.Where(e =>
                 e.HpId == hpId &&
                 e.ItemCd == "113003510" &&
                 e.StartDate <= sinDate &&
                 e.EndDate >= sinDate).ToList();

        if (autoSanteis.Count > 0)
        {
            tenantTracking.RemoveRange(autoSanteis);
        }


        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "12000237012",
                1
            )
        };
        try
        {
            tenantTracking.SaveChanges();
            // Act
            var checkModels = medicalExaminationRepository.SiIkuji(hpId, sinDate, birthDay, ordInfDetailModels, true, 5);
            //Assert
            Assert.True(checkModels.Count == 0);
        }
        finally
        {
            systemGenerationConf.Val = temp;
            if (autoSanteis.Count > 0) tenantTracking.AddRange(autoSanteis);
            tenantTracking.SaveChanges();
        }
    }

    /// <summary>
    /// Check when messages are returned
    /// </summary>
    [Test]
    public void SiIkuji_073_ExistMessage()
    {
        //Arrange
        int hpId = 1, birthDay = 21000101, sinDate = 21000101;
        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();

        var systemGenerationConf = tenantTracking.SystemGenerationConfs.FirstOrDefault(p => p.HpId == 1
          && p.GrpCd == 8001
          && p.GrpEdaNo == 0
          && p.StartDate <= sinDate
          && p.EndDate >= sinDate);
        var temp = systemGenerationConf?.Val ?? 0;
        if (systemGenerationConf != null) systemGenerationConf.Val = 1;
        else
        {
            systemGenerationConf = new SystemGenerationConf
            {
                HpId = 1,
                GrpCd = 8001,
                GrpEdaNo = 0,
                StartDate = 0,
                EndDate = 99999999,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenantTracking.SystemGenerationConfs.Add(systemGenerationConf);
        }

        var autoSanteis = tenantTracking.AutoSanteiMsts.Where(e =>
                 e.HpId == hpId &&
                 e.ItemCd == "113003510" &&
                 e.StartDate <= sinDate &&
                 e.EndDate >= sinDate).ToList();

        if (autoSanteis.Count > 0)
        {
            tenantTracking.RemoveRange(autoSanteis);
        }



        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "12000237012",
                1
            )
        };
        try
        {
            tenantTracking.SaveChanges();
            // Act
            var checkModel1s = medicalExaminationRepository.SiIkuji(hpId, sinDate, birthDay, ordInfDetailModels, true, 1);
            var checkModel2s = medicalExaminationRepository.SiIkuji(hpId, sinDate, birthDay, ordInfDetailModels, true, 6);
            var checkModel3s = medicalExaminationRepository.SiIkuji(hpId, sinDate, birthDay, ordInfDetailModels, false, 1);
            var checkModel4s = medicalExaminationRepository.SiIkuji(hpId, sinDate, birthDay, ordInfDetailModels, false, 6);

            //Assert
            Assert.True(checkModel1s.Count == 1 && checkModel1s.Any(i => i.CheckingContent == "\"乳幼児育児栄養指導料（情報通信機器）\"を算定できる可能性があります。"));
            Assert.True(checkModel2s.Count == 1 && checkModel2s.Any(i => i.CheckingContent == "\"乳幼児育児栄養指導料（情報通信機器）\"を算定できる可能性があります。"));
            Assert.True(checkModel3s.Count == 1 && checkModel3s.Any(i => i.CheckingContent == "\"乳幼児育児栄養指導料\"を算定できる可能性があります。"));
            Assert.True(checkModel4s.Count == 1 && checkModel4s.Any(i => i.CheckingContent == "\"乳幼児育児栄養指導料\"を算定できる可能性があります。"));
        }
        finally
        {
            systemGenerationConf.Val = temp;
            if (autoSanteis.Count > 0) tenantTracking.AddRange(autoSanteis);
            tenantTracking.SaveChanges();
        }
    }

    /// <summary>
    /// Check some ItemCd are zanyaku
    /// </summary>
    [Test]
    public void Zanyaku_074_Item()
    {
        //Arrange
        int hpId = 1, sinDate = 21000101;
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "@ZANGIGI",
                1
            ),
            new OrdInfDetailModel(
                "@ZANTEIKYO",
                1
            )
        };
        var ordInfs = new List<OrdInfModel>() {
            new OrdInfModel(
                0,
                21,
                new()
                )
        };

        // Act
        var checkModel1s = medicalExaminationRepository.Zanyaku(hpId, sinDate, ordInfDetailModels, ordInfs);

        //Assert
        Assert.True(checkModel1s.Count == 0);
    }

    [Test]
    public void Zanyaku_075_Check_ExistOutOrder()
    {
        //Arrange
        int hpId = 1, sinDate = 21000101;
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);

        // Act
        var checkModel1s = medicalExaminationRepository.Zanyaku(hpId, sinDate, new(), new());

        //Assert
        Assert.True(checkModel1s.Count == 0);
    }

    /// <summary>
    /// Check Zanyaku is drug
    /// </summary>
    [Test]
    public void Zanyaku_076_Drug()
    {
        //Arrange
        int hpId = 1, sinDate = 21000101;
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockSystemConf = new Mock<ISystemConfRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, mockSystemConf.Object, mockMstItem.Object, mockCommon.Object);
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "123421341",
                1
            ),
            new OrdInfDetailModel(
                "123421342",
                1
            )
        };
        var ordInfs = new List<OrdInfModel>() {
            new OrdInfModel(
                1,
                21,
                new()
                )
        };

        var tenItem = new TenItemModel();

        mockMstItem.Setup(finder => finder.GetTenMstInfo(It.IsAny<int>(), It.IsAny<string>(), It.IsAny<int>()))
            .Returns((int hpId, string itemCd, int sinDate) => tenItem);

        // Act
        var checkModel1s = medicalExaminationRepository.Zanyaku(hpId, sinDate, ordInfDetailModels, ordInfs);

        //Assert
        Assert.True(checkModel1s.Count > 0);
    }

    /// <summary>
    /// Check some ItemCd are TouyakutokusyoSyoho
    /// </summary>
    [Test]
    public void TouyakuTokusyoSyoho_077_Item()
    {
        //Arrange
        int hpId = 1, sinDate = 21000101, hokenId = 10;
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "120002270",
                1
            ),
            new OrdInfDetailModel(
                "120003170",
                1
            ),
            new OrdInfDetailModel(
                "120002570",
                1
            ),
            new OrdInfDetailModel(
                "120003270",
                1
            )
        };
        var ordInfs = new List<OrdInfModel>() {
            new OrdInfModel(
                1,
                21,
                new()
                )
        };

        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                1,
                10,
                1,
                1,
                1
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(hpId,2002,0,0,"",""),
            new SystemConfModel(hpId,2002,1,0,"",""),
            new SystemConfModel(hpId,2002,2,0,"",""),
            new SystemConfModel(hpId,2002,3,0,"",""),
            new SystemConfModel(hpId,2002,5,0,"",""),
            new SystemConfModel(hpId,2002,6,0,"","")
        };

        // Act
        var checkModel1s = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModels, ordInfs, systemConfModels);

        //Assert
        Assert.True(checkModel1s.Count == 0);
    }

    /// <summary>
    /// Check TouyakuTokusyoSyoho is drug
    /// </summary>
    [Test]
    public void TouyakuTokusyoSyoho_078_Drug()
    {
        //Arrange
        int hpId = 1, sinDate = 21000101, hokenId = 10;
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "**********",
                1
            ),
            new OrdInfDetailModel(
                "**********",
                1
            ),
            new OrdInfDetailModel(
                "**********",
                1
            ),
            new OrdInfDetailModel(
                "**********",
                1
            )
        };
        var ordInfs = new List<OrdInfModel>() {
            new OrdInfModel(
                1,
                25,
                new()
                )
        };

        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                1,
                10,
                1,
                1,
                1
            )
        };
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(hpId,2002,0,0,"",""),
            new SystemConfModel(hpId,2002,1,0,"",""),
            new SystemConfModel(hpId,2002,2,0,"",""),
            new SystemConfModel(hpId,2002,3,0,"",""),
            new SystemConfModel(hpId,2002,5,0,"",""),
            new SystemConfModel(hpId,2002,6,0,"","")
        };
        // Act
        var checkModel1s = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModels, ordInfs, systemConfModels);

        //Assert
        Assert.True(checkModel1s.Count == 0);
    }


    /// <summary>
    /// Check TouyakuTokusyoSyoho is true
    /// </summary>
    [Test]
    public void TouyakuTokusyoSyoho_079_True()
    {
        //Arrange
        int hpId = 1, sinDate = 21000101, hokenId = 10;
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);
        var ordInfDetailModel1s = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "**********",
                20,
                1
            ),
            new OrdInfDetailModel(
                "Z101",
                20
            ),
            new OrdInfDetailModel(
                "120000710",
                1
            ),
            new OrdInfDetailModel(
                "120001010",
                1
            ),
            new OrdInfDetailModel(
                21,
                28
            )
        };

        var ordInfDetailModel2s = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "**********",
                20,
                1
            ),
            new OrdInfDetailModel(
                "Z101",
                20
            ),
            new OrdInfDetailModel(
                "120000710",
                1
            ),
            new OrdInfDetailModel(
                "120001010",
                1
            ),
            new OrdInfDetailModel(
                21,
                21
            )
        };
        var ordInf1s = new List<OrdInfModel>() {
            new OrdInfModel(
                1,
                21,
                ordInfDetailModel1s
                ),

        };

        var ordInf2s = new List<OrdInfModel>() {
            new OrdInfModel(
                1,
                21,
                ordInfDetailModel2s
                ),
        };

        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                5,
                10,
                0,
                1,
                1
            )
        };

        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
        var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
            && p.GrpCd == 2002
            && p.GrpEdaNo == 0);
        var systemConf2 = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
          && p.GrpCd == 2002
          && p.GrpEdaNo == 1);
        var temp = systemConf?.Val ?? 0;
        if (systemConf != null) systemConf.Val = 1;
        else
        {
            systemConf = new SystemConf
            {
                HpId = 1,
                GrpCd = 2002,
                GrpEdaNo = 0,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenantTracking.SystemConfs.Add(systemConf);
        }
        if (systemConf2 != null) systemConf2.Val = 0;
        else
        {
            systemConf2 = new SystemConf
            {
                HpId = 1,
                GrpCd = 2002,
                GrpEdaNo = 1,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 0
            };
            tenantTracking.SystemConfs.Add(systemConf);
        }
        try
        {
            tenantTracking.SaveChanges();
            var systemConfModels = new List<SystemConfModel>()
            {
                new SystemConfModel(hpId,2002,0,0,"",""),
                new SystemConfModel(hpId,2002,1,0,"",""),
                new SystemConfModel(hpId,2002,2,0,"",""),
                new SystemConfModel(hpId,2002,3,0,"",""),
                new SystemConfModel(hpId,2002,5,0,"",""),
                new SystemConfModel(hpId,2002,6,0,"","")
            };
            // Act
            var checkModel1s = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModel1s, ordInf1s, systemConfModels);
            var checkModel2s = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModel1s, ordInf2s, systemConfModels);

            //Assert
            Assert.True(checkModel1s.Count > 0 && checkModel2s.Count > 0);
        }
        finally
        {
            systemConf.Val = temp;
            systemConf2.Val = temp;
            tenantTracking.SaveChanges();
        }
    }

    /// <summary>
    /// Check special
    /// </summary>
    [Test]
    public void CheckByoMei_080_Special()
    {
        //Arrange
        int hpId = 1, sinDate = 21100101, hokenId = 10, inoutKbn = 0;
        bool isCheckShuByomeiOnly1 = true, isCheckTeikyoByomei1 = true, isCheckTeikyoByomei2 = false, isCheckShuByomeiOnly2 = false;
        string itemTokusyoCd = "", itemCd = "88888888";
        var tenant = TenantProvider.GetNoTrackingDataContext();

        var byomeiMsts = new List<ByomeiMst>()
        {
            new ByomeiMst
            {
                HpId = 1,
                ByomeiCd = "10000",
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1
            },
            new ByomeiMst
            {
                HpId = 1,
                ByomeiCd = "11111",
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1
            }
        };
        tenant.ByomeiMsts.AddRange(byomeiMsts);

        var tekiouByomeiMst = new List<TekiouByomeiMst>() { new TekiouByomeiMst {
            HpId = 1,
            ItemCd = itemCd,
            ByomeiCd = "10000",
            SystemData = 1,
            IsInvalidTokusyo = 2
        } };
        tenant.TekiouByomeiMsts.AddRange(tekiouByomeiMst);
        var mockConfiguration = new Mock<IConfiguration>();
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);

        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                5,
                10,
                21000101,
                1,
                21010101,
                1,
                "10000"
            ),
            new PtDiseaseModel(
                5,
                0,
                21000101,
                0,
                1,
                1,
                "10000"
            )
        };
        var itemCds = new List<string>()
        {
            ItemCdConst.TouyakuTokuSyoSyoho,
            ItemCdConst.TouyakuTokuSyoSyohosen,
            ItemCdConst.TouyakuTokuSyo1Syoho,
            ItemCdConst.TouyakuTokuSyo2Syoho,
            ItemCdConst.TouyakuTokuSyo1Syohosen,
            ItemCdConst.TouyakuTokuSyo2Syohosen
        };
        var tenMsts = tenant.TenMsts.Where(i => itemCds.Contains(i.ItemCd) && i.StartDate <= sinDate && i.EndDate >= sinDate && i.HpId == hpId && i.IsDeleted == 0).ToList();
        try
        {
            tenant.SaveChanges();
            // Act
            var checkModel1 = medicalExaminationRepository.CheckByoMei(hpId, sinDate, hokenId, isCheckShuByomeiOnly1, isCheckTeikyoByomei1, itemTokusyoCd, itemCd, inoutKbn, byomeiModelList, tenMsts);
            var checkModel2 = medicalExaminationRepository.CheckByoMei(hpId, sinDate, hokenId, isCheckShuByomeiOnly2, isCheckTeikyoByomei2, itemTokusyoCd, itemCd, inoutKbn, byomeiModelList, tenMsts);

            //Assert
            Assert.True(checkModel1.CheckingType > 0 && checkModel2.CheckingType > 0);
        }
        finally
        {
            tenant.ByomeiMsts.RemoveRange(byomeiMsts);
            tenant.TekiouByomeiMsts.RemoveRange(tekiouByomeiMst);
            tenant.SaveChanges();
        }
    }

    [Test]
    public void CheckByoMei_081_Other()
    {
        //Arrange
        int hpId = 1, sinDate = 21100101, hokenId = 10, inoutKbn = 0;
        bool isCheckShuByomeiOnly1 = true, isCheckTeikyoByomei1 = true, isCheckTeikyoByomei2 = false, isCheckShuByomeiOnly2 = false;
        string itemTokusyoCd = "", itemCd = "88888888";
        var tenant = TenantProvider.GetNoTrackingDataContext();

        var byomeiMsts = new List<ByomeiMst>()
        {
            new ByomeiMst
            {
                HpId = 1,
                ByomeiCd = "10000",
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1
            },
            new ByomeiMst
            {
                HpId = 1,
                ByomeiCd = "11111",
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1
            }
        };
        tenant.ByomeiMsts.AddRange(byomeiMsts);

        var tekiouByomeiMst = new List<TekiouByomeiMst>() { new TekiouByomeiMst {
            HpId = 1,
            ItemCd = itemCd,
            ByomeiCd = "10000",
            SystemData = 1,
            IsInvalidTokusyo = 2
        } };
        tenant.TekiouByomeiMsts.AddRange(tekiouByomeiMst);
        var mockConfiguration = new Mock<IConfiguration>();
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);

        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                8,
                10,
                21000101,
                1,
                21010101,
                1,
                "10000"
            ),
            new PtDiseaseModel(
                8,
                0,
                21000101,
                0,
                1,
                1,
                "10000"
            )
        };
        var itemCds = new List<string>()
        {
            ItemCdConst.TouyakuTokuSyoSyoho,
            ItemCdConst.TouyakuTokuSyoSyohosen,
            ItemCdConst.TouyakuTokuSyo1Syoho,
            ItemCdConst.TouyakuTokuSyo2Syoho,
            ItemCdConst.TouyakuTokuSyo1Syohosen,
            ItemCdConst.TouyakuTokuSyo2Syohosen
        };
        var tenMsts = tenant.TenMsts.Where(i => itemCds.Contains(i.ItemCd) && i.StartDate <= sinDate && i.EndDate >= sinDate && i.HpId == hpId && i.IsDeleted == 0).ToList();
        try
        {
            tenant.SaveChanges();
            // Act
            var checkModel1 = medicalExaminationRepository.CheckByoMei(hpId, sinDate, hokenId, isCheckShuByomeiOnly1, isCheckTeikyoByomei1, itemTokusyoCd, itemCd, inoutKbn, byomeiModelList, tenMsts);
            var checkModel2 = medicalExaminationRepository.CheckByoMei(hpId, sinDate, hokenId, isCheckShuByomeiOnly2, isCheckTeikyoByomei2, itemTokusyoCd, itemCd, inoutKbn, byomeiModelList, tenMsts);

            //Assert
            Assert.True(checkModel1.CheckingType > 0 && checkModel2.CheckingType > 0 && !checkModel1.Santei && !checkModel2.Santei);

        }
        finally
        {
            tenant.ByomeiMsts.RemoveRange(byomeiMsts);
            tenant.TekiouByomeiMsts.RemoveRange(tekiouByomeiMst);
            tenant.SaveChanges();
        }
    }

    [Test]
    public void CheckByoMei_082_True()
    {
        //Arrange
        int hpId = 1, sinDate = 21100101, hokenId = 10, inoutKbn = 0;
        bool isCheckShuByomeiOnly1 = true, isCheckTeikyoByomei1 = true, isCheckTeikyoByomei2 = false, isCheckShuByomeiOnly2 = false;
        string itemTokusyoCd = "", itemCd = "88888888";
        var tenant = TenantProvider.GetNoTrackingDataContext();

        var byomeiMsts = new List<ByomeiMst>()
        {
            new ByomeiMst
            {
                HpId = 1,
                ByomeiCd = "10000",
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1
            },
            new ByomeiMst
            {
                HpId = 1,
                ByomeiCd = "11111",
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1
            }
        };
        tenant.ByomeiMsts.AddRange(byomeiMsts);

        var tekiouByomeiMst = new List<TekiouByomeiMst>() { new TekiouByomeiMst {
            HpId = 1,
            ItemCd = itemCd,
            ByomeiCd = "10000",
            SystemData = 1,
            IsInvalidTokusyo = 2
        } };
        tenant.TekiouByomeiMsts.AddRange(tekiouByomeiMst);
        var mockConfiguration = new Mock<IConfiguration>();
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);

        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                6,
                10,
                21000101,
                1,
                21010101,
                1,
                "10000"
            ),
            new PtDiseaseModel(
                6,
                0,
                21000101,
                0,
                1,
                1,
                "10000"
            )
        };
        var itemCds = new List<string>()
        {
            ItemCdConst.TouyakuTokuSyoSyoho,
            ItemCdConst.TouyakuTokuSyoSyohosen,
            ItemCdConst.TouyakuTokuSyo1Syoho,
            ItemCdConst.TouyakuTokuSyo2Syoho,
            ItemCdConst.TouyakuTokuSyo1Syohosen,
            ItemCdConst.TouyakuTokuSyo2Syohosen
        };
        var tenMsts = tenant.TenMsts.Where(i => itemCds.Contains(i.ItemCd) && i.StartDate <= sinDate && i.EndDate >= sinDate && i.HpId == hpId && i.IsDeleted == 0).ToList();
        try
        {
            tenant.SaveChanges();
            // Act
            var checkModel1 = medicalExaminationRepository.CheckByoMei(hpId, sinDate, hokenId, isCheckShuByomeiOnly1, isCheckTeikyoByomei1, itemTokusyoCd, itemCd, inoutKbn, byomeiModelList, tenMsts);
            var checkModel2 = medicalExaminationRepository.CheckByoMei(hpId, sinDate, hokenId, isCheckShuByomeiOnly2, isCheckTeikyoByomei2, itemTokusyoCd, itemCd, inoutKbn, byomeiModelList, tenMsts);

            //Assert
            Assert.True(checkModel1.CheckingType == 0 && checkModel2.CheckingType == 0);
        }
        finally
        {
            tenant.ByomeiMsts.RemoveRange(byomeiMsts);
            tenant.TekiouByomeiMsts.RemoveRange(tekiouByomeiMst);
            tenant.SaveChanges();
        }
    }

    #region TouyakuTokusyoSyoho Special
    // /// <summary>
    // /// Check  day < 28
    // /// </summary>
    // [Test]
    // public void TouyakuTokusyoSyoho_083_SystemSetting1_OutHospistal_LessThan28()
    // {
    //     //Arrange
    //     int hpId = 1, sinDate = 21000101, hokenId = 10;
    //     var mockConfiguration = new Mock<IConfiguration>();
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);

    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(
    //             "1221",
    //             20,
    //             1
    //         )
    //     };
    //     var ordInfs = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             1,
    //             21,
    //             ordInfDetailModels
    //             ),

    //     };

    //     var byomeiModelList = new List<PtDiseaseModel>()
    //     {
    //         new PtDiseaseModel(
    //             5,
    //             10,
    //             0,
    //             1,
    //             1
    //         )
    //     };

    //     var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
    //     var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //         && p.GrpCd == 2002
    //         && p.GrpEdaNo == 0);
    //     var systemConf2 = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //       && p.GrpCd == 2002
    //       && p.GrpEdaNo == 1);
    //     var temp = systemConf?.Val ?? 0;
    //     if (systemConf != null) systemConf.Val = 0;
    //     else
    //     {
    //         systemConf = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 0,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 0
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     if (systemConf2 != null) systemConf2.Val = 0;
    //     else
    //     {
    //         systemConf2 = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 1,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 0
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     try
    //     {
    //         tenantTracking.SaveChanges();
    //         // Act
    //         var checkModels = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModels, ordInfs);
    //         //Assert
    //         Assert.True(checkModels.Count == 1 && checkModels.First().InOutKbn == 1 && checkModels.First().CheckingType == CheckingType.MissingCalculate && checkModels.First().ItemName == "特定疾患処方管理加算１（処方箋料）");

    //     }
    //     finally
    //     {
    //         systemConf.Val = temp;
    //         systemConf2.Val = temp;
    //         tenantTracking.SaveChanges();
    //     }

    // }

    // [Test]
    // public void TouyakuTokusyoSyoho_084_SystemSetting1_InOutHospistal_LessThan28()
    // {
    //     //Arrange
    //     int hpId = 1, sinDate = 21000101, hokenId = 10;
    //     var mockConfiguration = new Mock<IConfiguration>();
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);

    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(
    //            "1221",
    //             20,
    //             1
    //         )
    //     };

    //     var ordInf1s = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             1,
    //             21,
    //             ordInfDetailModels
    //             ),

    //     };
    //     var ordInf2s = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             0,
    //             21,
    //             ordInfDetailModels
    //             )
    //     };

    //     var byomeiModelList = new List<PtDiseaseModel>()
    //     {
    //         new PtDiseaseModel(
    //             5,
    //             10,
    //             0,
    //             1,
    //             1
    //         )
    //     };

    //     var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
    //     var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //         && p.GrpCd == 2002
    //         && p.GrpEdaNo == 0);
    //     var systemConf2 = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //       && p.GrpCd == 2002
    //       && p.GrpEdaNo == 1);
    //     var temp = systemConf?.Val ?? 0;
    //     if (systemConf != null) systemConf.Val = 0;
    //     else
    //     {
    //         systemConf = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 0,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 0
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     if (systemConf2 != null) systemConf2.Val = 0;
    //     else
    //     {
    //         systemConf2 = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 1,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 0
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     try
    //     {
    //         tenantTracking.SaveChanges();
    //         // Act
    //         var checkModels = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModels, ordInf1s.Union(ordInf2s).ToList());
    //         //Assert
    //         Assert.True(checkModels.Count == 1 && checkModels.First().InOutKbn == 1 && checkModels.First().CheckingType == CheckingType.MissingCalculate && checkModels.First().ItemName == "特定疾患処方管理加算１（処方箋料）");

    //     }
    //     finally
    //     {
    //         systemConf.Val = temp;
    //         systemConf2.Val = temp;
    //         tenantTracking.SaveChanges();
    //     }

    // }

    // [Test]
    // public void TouyakuTokusyoSyoho_085_SystemSetting1_InHospistal_LessThan28()
    // {
    //     //Arrange
    //     int hpId = 1, sinDate = 21000101, hokenId = 10;
    //     var mockConfiguration = new Mock<IConfiguration>();
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);

    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(
    //             "1221",
    //             20,
    //             1
    //         )
    //     };

    //     var ordInf1s = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             0,
    //             21,
    //             ordInfDetailModels
    //             ),

    //     };
    //     var ordInf2s = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             0,
    //             21,
    //             ordInfDetailModels
    //             )
    //     };

    //     var byomeiModelList = new List<PtDiseaseModel>()
    //     {
    //         new PtDiseaseModel(
    //             5,
    //             10,
    //             0,
    //             1,
    //             1
    //         )
    //     };

    //     var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
    //     var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //         && p.GrpCd == 2002
    //         && p.GrpEdaNo == 0);
    //     var systemConf2 = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //       && p.GrpCd == 2002
    //       && p.GrpEdaNo == 1);
    //     var temp = systemConf?.Val ?? 0;
    //     if (systemConf != null) systemConf.Val = 0;
    //     else
    //     {
    //         systemConf = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 0,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 0
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     if (systemConf2 != null) systemConf2.Val = 0;
    //     else
    //     {
    //         systemConf2 = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 1,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 0
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     try
    //     {
    //         tenantTracking.SaveChanges();
    //         // Act
    //         var checkModels = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModels, ordInf1s.Union(ordInf2s).ToList());
    //         //Assert
    //         Assert.True(checkModels.Count == 1 && checkModels.First().InOutKbn == 0 && checkModels.First().CheckingType == CheckingType.MissingCalculate && checkModels.First().ItemName == "特定疾患処方管理加算１（処方料）");
    //     }
    //     finally
    //     {
    //         systemConf.Val = temp;
    //         systemConf2.Val = temp;
    //         tenantTracking.SaveChanges();
    //     }

    // }

    [Test]
    public void TouyakuTokusyoSyoho_086_SystemSetting1_NoMainDisease_OutHospistal_LessThan28()
    {
        //Arrange
        int hpId = 1, sinDate = 21000101, hokenId = 10;
        var mockConfiguration = new Mock<IConfiguration>();
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);

        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "1221",
                20,
                1
            )
        };

        var ordInfs = new List<OrdInfModel>() {
            new OrdInfModel(
                1,
                21,
                ordInfDetailModels
                ),

        };

        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                5,
                10,
                0,
                1,
                0
            )
        };

        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
        var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
            && p.GrpCd == 2002
            && p.GrpEdaNo == 0);
        var systemConf2 = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
          && p.GrpCd == 2002
          && p.GrpEdaNo == 1);
        var temp = systemConf?.Val ?? 0;
        if (systemConf != null) systemConf.Val = 1;
        else
        {
            systemConf = new SystemConf
            {
                HpId = 1,
                GrpCd = 2002,
                GrpEdaNo = 0,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenantTracking.SystemConfs.Add(systemConf);
        }
        if (systemConf2 != null) systemConf2.Val = 0;
        else
        {
            systemConf2 = new SystemConf
            {
                HpId = 1,
                GrpCd = 2002,
                GrpEdaNo = 1,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 0
            };
            tenantTracking.SystemConfs.Add(systemConf);
        }
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(hpId,2002,0,0,"",""),
            new SystemConfModel(hpId,2002,1,0,"",""),
            new SystemConfModel(hpId,2002,2,0,"",""),
            new SystemConfModel(hpId,2002,3,0,"",""),
            new SystemConfModel(hpId,2002,5,0,"",""),
            new SystemConfModel(hpId,2002,6,0,"","")
        };
        try
        {
            tenantTracking.SaveChanges();
            // Act
            var checkModels = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModels, ordInfs, systemConfModels);
            //Assert
            Assert.True(checkModels.Count == 0);
        }
        finally
        {
            systemConf.Val = temp;
            systemConf2.Val = temp;
            tenantTracking.SaveChanges();
        }


    }

    // [Test]
    // public void TouyakuTokusyoSyoho_087_SystemSetting1_MainDisease_OutHospistal_LessThan28()
    // {
    //     //Arrange
    //     int hpId = 1, sinDate = 21000101, hokenId = 10;
    //     var mockConfiguration = new Mock<IConfiguration>();
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);

    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(
    //             "1221",
    //             20,
    //             1
    //         )
    //     };

    //     var ordInfs = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             1,
    //             21,
    //             ordInfDetailModels
    //             ),

    //     };

    //     var byomeiModelList = new List<PtDiseaseModel>()
    //     {
    //         new PtDiseaseModel(
    //             5,
    //             10,
    //             0,
    //             1,
    //             1
    //         )
    //     };

    //     var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
    //     var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //         && p.GrpCd == 2002
    //         && p.GrpEdaNo == 0);
    //     var systemConf2 = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //       && p.GrpCd == 2002
    //       && p.GrpEdaNo == 1);
    //     var temp = systemConf?.Val ?? 0;
    //     if (systemConf != null) systemConf.Val = 1;
    //     else
    //     {
    //         systemConf = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 0,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 1
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     if (systemConf2 != null) systemConf2.Val = 0;
    //     else
    //     {
    //         systemConf2 = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 1,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 0
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     try
    //     {
    //         tenantTracking.SaveChanges();

    //         // Act
    //         var checkModels = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModels, ordInfs);

    //         //Assert
    //         Assert.True(checkModels.Count == 1 && checkModels.First().InOutKbn == 1 && checkModels.First().CheckingType == CheckingType.MissingCalculate && checkModels.First().ItemName == "特定疾患処方管理加算１（処方箋料）");

    //     }
    //     finally
    //     {
    //         systemConf.Val = temp;
    //         systemConf2.Val = temp;
    //         tenantTracking.SaveChanges();
    //     }

    // }

    [Test]
    public void TouyakuTokusyoSyoho_088_SystemSetting1_MainDisease_NoMapDrug_OutHospistal_LessThan28()
    {
        //Arrange
        int hpId = 1, sinDate = 20000101, hokenId = 10;
        var mockConfiguration = new Mock<IConfiguration>();
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);

        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                21,
                21
            )
        };

        var ordInfs = new List<OrdInfModel>() {
            new OrdInfModel(
                1,
                21,
                ordInfDetailModels
                ),

        };

        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                5,
                10,
                1,
                1,
                1
            )
        };

        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
        var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
            && p.GrpCd == 2002
            && p.GrpEdaNo == 0);
        var systemConf2 = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
          && p.GrpCd == 2002
          && p.GrpEdaNo == 1);
        var temp = systemConf?.Val ?? 0;
        if (systemConf != null) systemConf.Val = 1;
        else
        {
            systemConf = new SystemConf
            {
                HpId = 1,
                GrpCd = 2002,
                GrpEdaNo = 0,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenantTracking.SystemConfs.Add(systemConf);
        }
        if (systemConf2 != null) systemConf2.Val = 1;
        else
        {
            systemConf2 = new SystemConf
            {
                HpId = 1,
                GrpCd = 2002,
                GrpEdaNo = 1,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenantTracking.SystemConfs.Add(systemConf);
        }
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(hpId,2002,0,0,"",""),
            new SystemConfModel(hpId,2002,1,0,"",""),
            new SystemConfModel(hpId,2002,2,0,"",""),
            new SystemConfModel(hpId,2002,3,0,"",""),
            new SystemConfModel(hpId,2002,5,0,"",""),
            new SystemConfModel(hpId,2002,6,0,"","")
        };
        try
        {
            tenantTracking.SaveChanges();

            // Act
            var checkModels = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModels, ordInfs, systemConfModels);

            //Assert
            Assert.True(checkModels.Count == 0);
        }
        finally
        {
            systemConf.Val = temp;
            systemConf2.Val = temp;
            tenantTracking.SaveChanges();
        }


    }

    // [Test]
    // public void TouyakuTokusyoSyoho_089_SystemSetting1_MainDisease_MapDrug_OutHospistal_LessThan28()
    // {
    //     //Arrange
    //     int hpId = 1, sinDate = 21000101, hokenId = 10;
    //     string byomeiCd = "0670670", itemCd = "**********";
    //     var mockConfiguration = new Mock<IConfiguration>();
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);

    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(
    //             itemCd,
    //             20,
    //             1
    //         )
    //     };

    //     var ordInfs = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             1,
    //             21,
    //             ordInfDetailModels
    //             ),

    //     };

    //     var byomeiModelList = new List<PtDiseaseModel>()
    //     {
    //         new PtDiseaseModel(
    //             5,
    //             10,
    //             20220101,
    //             0,
    //             1,
    //             1,
    //             byomeiCd
    //         )
    //     };

    //     var tenant = TenantProvider.GetNoTrackingDataContext();
    //     var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
    //     var byomeiMsts = CheckedOrderData.ReadByomeiMst(byomeiCd);
    //     var tekiouByomeiMsts = CheckedOrderData.ReadTekiouByomeiMst(byomeiCd, itemCd);

    //     tenant.ByomeiMsts.AddRange(byomeiMsts);
    //     tenant.TekiouByomeiMsts.AddRange(tekiouByomeiMsts);

    //     var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //         && p.GrpCd == 2002
    //         && p.GrpEdaNo == 0);
    //     var systemConf2 = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //       && p.GrpCd == 2002
    //       && p.GrpEdaNo == 1);
    //     var temp = systemConf?.Val ?? 0;
    //     if (systemConf != null) systemConf.Val = 1;
    //     else
    //     {
    //         systemConf = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 0,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 1
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     if (systemConf2 != null) systemConf2.Val = 1;
    //     else
    //     {
    //         systemConf2 = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 1,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 1
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     try
    //     {
    //         tenant.SaveChanges();
    //         tenantTracking.SaveChanges();

    //         // Act
    //         var checkModels = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModels, ordInfs);

    //         //Assert
    //         Assert.True(checkModels.Count == 1 && checkModels.First().InOutKbn == 1 && checkModels.First().CheckingType == CheckingType.MissingCalculate && checkModels.First().ItemName == "特定疾患処方管理加算１（処方箋料）");

    //     }
    //     finally
    //     {
    //         systemConf.Val = temp;
    //         systemConf2.Val = temp;
    //         tenantTracking.ByomeiMsts.RemoveRange(byomeiMsts);
    //         tenantTracking.TekiouByomeiMsts.RemoveRange(tekiouByomeiMsts);
    //         tenantTracking.SaveChanges();
    //     }

    // }

    // /// <summary>
    // /// Check day > 28
    // /// </summary>
    // [Test]
    // public void TouyakuTokusyoSyoho_090_SystemSetting2_OutHospistal_MoreThan28()
    // {
    //     //Arrange
    //     int hpId = 1, sinDate = 21000101, hokenId = 10;
    //     var mockConfiguration = new Mock<IConfiguration>();
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);

    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(
    //             "1221",
    //             20,
    //             1
    //         ),
    //         new OrdInfDetailModel(
    //             21,
    //             30
    //         )
    //     };
    //     var ordInfs = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             1,
    //             21,
    //             ordInfDetailModels
    //             ),

    //     };

    //     var byomeiModelList = new List<PtDiseaseModel>()
    //     {
    //         new PtDiseaseModel(
    //             5,
    //             10,
    //             0,
    //             1,
    //             1
    //         )
    //     };

    //     var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
    //     var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //         && p.GrpCd == 2002
    //         && p.GrpEdaNo == 2);
    //     var systemConf2 = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //       && p.GrpCd == 2002
    //       && p.GrpEdaNo == 3);
    //     var temp = systemConf?.Val ?? 0;
    //     if (systemConf != null) systemConf.Val = 0;
    //     else
    //     {
    //         systemConf = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 2,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 0
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     if (systemConf2 != null) systemConf2.Val = 0;
    //     else
    //     {
    //         systemConf2 = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 3,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 0
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     try
    //     {
    //         tenantTracking.SaveChanges();

    //         // Act
    //         var checkModels = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModels, ordInfs);

    //         //Assert
    //         Assert.True(checkModels.Count == 1 && checkModels.First().InOutKbn == 1 && checkModels.First().CheckingType == CheckingType.MissingCalculate && checkModels.First().ItemName == "特定疾患処方管理加算２（処方箋料）");

    //     }
    //     finally
    //     {
    //         systemConf.Val = temp;
    //         systemConf2.Val = temp;
    //         tenantTracking.SaveChanges();
    //     }

    // }

    // [Test]
    // public void TouyakuTokusyoSyoho_091_SystemSetting2_InOutHospistal_MoreThan28()
    // {
    //     //Arrange
    //     int hpId = 1, sinDate = 21000101, hokenId = 10;
    //     var mockConfiguration = new Mock<IConfiguration>();
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);

    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(
    //             "1221",
    //             20,
    //             1
    //         ),
    //         new OrdInfDetailModel(
    //             21,
    //             30
    //         )
    //     };

    //     var ordInf1s = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             1,
    //             21,
    //             ordInfDetailModels
    //             ),

    //     };
    //     var ordInf2s = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             0,
    //             21,
    //             ordInfDetailModels
    //             )
    //     };

    //     var byomeiModelList = new List<PtDiseaseModel>()
    //     {
    //         new PtDiseaseModel(
    //             5,
    //             10,
    //             1,
    //             22000101,
    //             1
    //         )
    //     };

    //     var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
    //     var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //         && p.GrpCd == 2002
    //         && p.GrpEdaNo == 2);
    //     var systemConf2 = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //       && p.GrpCd == 2002
    //       && p.GrpEdaNo == 3);
    //     var temp = systemConf?.Val ?? 0;
    //     if (systemConf != null) systemConf.Val = 0;
    //     else
    //     {
    //         systemConf = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 0,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 0
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     if (systemConf2 != null) systemConf2.Val = 0;
    //     else
    //     {
    //         systemConf2 = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 1,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 0
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     try
    //     {
    //         tenantTracking.SaveChanges();

    //         // Act
    //         var checkModels = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModels, ordInf1s.Union(ordInf2s).ToList());

    //         //Assert
    //         Assert.True(checkModels.Count == 1 && checkModels.First().InOutKbn == 1 && checkModels.First().CheckingType == CheckingType.MissingCalculate && checkModels.First().ItemName == "特定疾患処方管理加算２（処方箋料）");

    //     }
    //     finally
    //     {
    //         systemConf.Val = temp;
    //         systemConf2.Val = temp;
    //         tenantTracking.SaveChanges();
    //     }
    // }

    // [Test]
    // public void TouyakuTokusyoSyoho_092_SystemSetting2_InHospistal_MoreThan28()
    // {
    //     //Arrange
    //     int hpId = 1, sinDate = 21000101, hokenId = 10;
    //     var mockConfiguration = new Mock<IConfiguration>();
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);

    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(
    //             "1221",
    //             20,
    //             1
    //         ),
    //         new OrdInfDetailModel(
    //             21,
    //             30
    //         )
    //     };

    //     var ordInf1s = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             0,
    //             21,
    //             ordInfDetailModels
    //             ),

    //     };
    //     var ordInf2s = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             0,
    //             21,
    //             ordInfDetailModels
    //             )
    //     };

    //     var byomeiModelList = new List<PtDiseaseModel>()
    //     {
    //         new PtDiseaseModel(
    //             5,
    //             10,
    //             0,
    //             1,
    //             1
    //         )
    //     };

    //     var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
    //     var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //         && p.GrpCd == 2002
    //         && p.GrpEdaNo == 2);
    //     var systemConf2 = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //       && p.GrpCd == 2002
    //       && p.GrpEdaNo == 3);
    //     var temp = systemConf?.Val ?? 0;
    //     if (systemConf != null) systemConf.Val = 0;
    //     else
    //     {
    //         systemConf = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 0,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 0
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     if (systemConf2 != null) systemConf2.Val = 0;
    //     else
    //     {
    //         systemConf2 = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 1,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 0
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     try
    //     {
    //         tenantTracking.SaveChanges();

    //         // Act
    //         var checkModels = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModels, ordInf1s.Union(ordInf2s).ToList());

    //         //Assert
    //         Assert.True(checkModels.Count == 1 && checkModels.First().InOutKbn == 0 && checkModels.First().CheckingType == CheckingType.MissingCalculate && checkModels.First().ItemName == "特定疾患処方管理加算２（処方料）");


    //     }
    //     finally
    //     {
    //         systemConf.Val = temp;
    //         systemConf2.Val = temp;
    //         tenantTracking.SaveChanges();
    //     }
    // }

    // [Test]
    // public void TouyakuTokusyoSyoho_093_SystemSetting2_NoMainDisease_OutHospistal_MoreThan28()
    // {
    //     //Arrange
    //     int hpId = 1, sinDate = 21000101, hokenId = 10;
    //     var mockConfiguration = new Mock<IConfiguration>();
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);

    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //        new OrdInfDetailModel(
    //             "1221",
    //             20,
    //             1
    //         ),
    //         new OrdInfDetailModel(
    //             21,
    //             30
    //         )
    //     };

    //     var ordInfs = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             1,
    //             21,
    //             ordInfDetailModels
    //             ),

    //     };

    //     var byomeiModelList = new List<PtDiseaseModel>()
    //     {
    //         new PtDiseaseModel(
    //             5,
    //             10,
    //             0,
    //             1,
    //             0
    //         )
    //     };

    //     var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
    //     var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //         && p.GrpCd == 2002
    //         && p.GrpEdaNo == 2);
    //     var systemConf2 = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //       && p.GrpCd == 2002
    //       && p.GrpEdaNo == 3);
    //     var temp = systemConf?.Val ?? 0;
    //     if (systemConf != null) systemConf.Val = 1;
    //     else
    //     {
    //         systemConf = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 0,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 1
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     if (systemConf2 != null) systemConf2.Val = 0;
    //     else
    //     {
    //         systemConf2 = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 1,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 0
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     try
    //     {
    //         tenantTracking.SaveChanges();

    //         // Act
    //         var checkModels = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModels, ordInfs);

    //         //Assert
    //         Assert.True(checkModels.Count == 0);

    //     }
    //     finally
    //     {
    //         systemConf.Val = temp;
    //         systemConf2.Val = temp;
    //         tenantTracking.SaveChanges();
    //     }
    // }

    // [Test]
    // public void TouyakuTokusyoSyoho_094_SystemSetting2_MainDisease_OutHospistal_MoreThan28()
    // {
    //     //Arrange
    //     int hpId = 1, sinDate = 21000101, hokenId = 10;
    //     var mockConfiguration = new Mock<IConfiguration>();
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);

    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //        new OrdInfDetailModel(
    //             "1221",
    //             20,
    //             1
    //         ),
    //         new OrdInfDetailModel(
    //             21,
    //             30
    //         )
    //     };

    //     var ordInfs = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             1,
    //             21,
    //             ordInfDetailModels
    //             ),

    //     };

    //     var byomeiModelList = new List<PtDiseaseModel>()
    //     {
    //         new PtDiseaseModel(
    //             5,
    //             10,
    //             0,
    //             1,
    //             1
    //         )
    //     };

    //     var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
    //     var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //         && p.GrpCd == 2002
    //         && p.GrpEdaNo == 2);
    //     var systemConf2 = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //       && p.GrpCd == 2002
    //       && p.GrpEdaNo == 3);
    //     var temp = systemConf?.Val ?? 0;
    //     if (systemConf != null) systemConf.Val = 1;
    //     else
    //     {
    //         systemConf = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 0,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 1
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     if (systemConf2 != null) systemConf2.Val = 0;
    //     else
    //     {
    //         systemConf2 = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 1,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 0
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     try
    //     {
    //         tenantTracking.SaveChanges();

    //         // Act
    //         var checkModels = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModels, ordInfs);

    //         //Assert
    //         Assert.True(checkModels.Count == 1 && checkModels.First().InOutKbn == 1 && checkModels.First().CheckingType == CheckingType.MissingCalculate && checkModels.First().ItemName == "特定疾患処方管理加算２（処方箋料）"); systemConf.Val = temp;

    //     }
    //     finally
    //     {
    //         systemConf2.Val = temp;
    //         tenantTracking.SaveChanges();
    //     }

    // }

    // [Test]
    // public void TouyakuTokusyoSyoho_095_SystemSetting2_MainDisease_NoMapDrug_OutHospistal_MoreThan28()
    // {
    //     //Arrange
    //     int hpId = 1, sinDate = 20000101, hokenId = 10;
    //     var mockConfiguration = new Mock<IConfiguration>();
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);

    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //          new OrdInfDetailModel(
    //             "1221",
    //             20,
    //             1
    //         ),
    //         new OrdInfDetailModel(
    //             21,
    //             30
    //         )
    //     };

    //     var ordInfs = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             1,
    //             21,
    //             ordInfDetailModels
    //             ),

    //     };

    //     var byomeiModelList = new List<PtDiseaseModel>()
    //     {
    //         new PtDiseaseModel(
    //             5,
    //             10,
    //             0,
    //             1,
    //             1
    //         )
    //     };

    //     var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
    //     var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //         && p.GrpCd == 2002
    //         && p.GrpEdaNo == 2);
    //     var systemConf2 = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //       && p.GrpCd == 2002
    //       && p.GrpEdaNo == 3);
    //     var temp = systemConf?.Val ?? 0;
    //     if (systemConf != null) systemConf.Val = 1;
    //     else
    //     {
    //         systemConf = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 0,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 1
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     if (systemConf2 != null) systemConf2.Val = 1;
    //     else
    //     {
    //         systemConf2 = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 1,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 1
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     try
    //     {
    //         tenantTracking.SaveChanges();

    //         // Act
    //         var checkModels = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModels, ordInfs);

    //         //Assert
    //         Assert.True(checkModels.Count == 0);
    //     }
    //     finally
    //     {
    //         systemConf.Val = temp;
    //         systemConf2.Val = temp;
    //         tenantTracking.SaveChanges();
    //     }
    // }

    // [Test]
    // public void TouyakuTokusyoSyoho_096_SystemSetting2_MainDisease_MapDrug_OutHospistal_MoreThan28()
    // {
    //     //Arrange
    //     int hpId = 1, sinDate = 21000101, hokenId = 10;
    //     var mockConfiguration = new Mock<IConfiguration>();
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);
    //     string byomeiCd = "0740740", itemCd = "**********";

    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(
    //             itemCd,
    //             20,
    //             1
    //         ),
    //         new OrdInfDetailModel(
    //             21,
    //             30
    //         )
    //     };

    //     var ordInfs = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             1,
    //             21,
    //             ordInfDetailModels
    //             ),

    //     };

    //     var byomeiModelList = new List<PtDiseaseModel>()
    //     {
    //         new PtDiseaseModel(
    //             5,
    //             10,
    //             1,
    //             1,
    //             22000101,
    //             1,
    //             byomeiCd
    //         )
    //     };

    //     var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
    //     var byomeiMsts = CheckedOrderData.ReadByomeiMst(byomeiCd);
    //     var tekiouByomeiMsts = CheckedOrderData.ReadTekiouByomeiMst(byomeiCd, itemCd);
    //     tenantTracking.ByomeiMsts.AddRange(byomeiMsts);
    //     tenantTracking.TekiouByomeiMsts.AddRange(tekiouByomeiMsts);
    //     var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //         && p.GrpCd == 2002
    //         && p.GrpEdaNo == 2);
    //     var systemConf2 = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //       && p.GrpCd == 2002
    //       && p.GrpEdaNo == 3);
    //     var temp = systemConf?.Val ?? 0;
    //     if (systemConf != null) systemConf.Val = 1;
    //     else
    //     {
    //         systemConf = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 0,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 1
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     if (systemConf2 != null) systemConf2.Val = 1;
    //     else
    //     {
    //         systemConf2 = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 1,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 1
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     try
    //     {
    //         tenantTracking.SaveChanges();

    //         // Act
    //         var checkModels = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModels, ordInfs);

    //         //Assert
    //         Assert.True(checkModels.Count == 1 && checkModels.First().InOutKbn == 1 && checkModels.First().CheckingType == CheckingType.MissingCalculate && checkModels.First().ItemName == "特定疾患処方管理加算２（処方箋料）");

    //     }
    //     finally
    //     {
    //         systemConf.Val = temp;
    //         systemConf2.Val = temp;
    //         tenantTracking.ByomeiMsts.RemoveRange(byomeiMsts);
    //         tenantTracking.TekiouByomeiMsts.RemoveRange(tekiouByomeiMsts);
    //         tenantTracking.SaveChanges();
    //     }

    // }

    // #endregion

    // #region TouyakuTokusyoSyoho Other
    // /// <summary>
    // /// Check  day < 28
    // /// </summary>
    // [Test]
    // public void TouyakuTokusyoSyoho_097_SystemSetting1_OutHospistal_LessThan28()
    // {
    //     //Arrange
    //     int hpId = 1, sinDate = 21000101, hokenId = 10;
    //     var mockConfiguration = new Mock<IConfiguration>();
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);

    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(
    //             "123",
    //             20,
    //             1
    //         )
    //     };
    //     var ordInfs = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             1,
    //             21,
    //             ordInfDetailModels
    //             ),

    //     };

    //     var byomeiModelList = new List<PtDiseaseModel>()
    //     {
    //         new PtDiseaseModel(
    //             8,
    //             10,
    //             0,
    //             1,
    //             1
    //         )
    //     };

    //     var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
    //     var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //         && p.GrpCd == 2002
    //         && p.GrpEdaNo == 0);
    //     var systemConf2 = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //       && p.GrpCd == 2002
    //       && p.GrpEdaNo == 1);
    //     var temp = systemConf?.Val ?? 0;
    //     if (systemConf != null) systemConf.Val = 0;
    //     else
    //     {
    //         systemConf = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 0,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 0
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     if (systemConf2 != null) systemConf2.Val = 0;
    //     else
    //     {
    //         systemConf2 = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 1,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 0
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     try
    //     {
    //         tenantTracking.SaveChanges();

    //         // Act
    //         var checkModels = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModels, ordInfs);

    //         //Assert
    //         Assert.True(checkModels.Count == 1 && checkModels.First().InOutKbn == 1 && checkModels.First().CheckingType == CheckingType.MissingCalculate && checkModels.First().ItemName == "特定疾患処方管理加算１（処方箋料）" && !checkModels.First().Santei);

    //     }
    //     finally
    //     {
    //         systemConf.Val = temp;
    //         systemConf2.Val = temp;
    //         tenantTracking.SaveChanges();
    //     }

    // }

    // [Test]
    // public void TouyakuTokusyoSyoho_098_SystemSetting1_InOutHospistal_LessThan28()
    // {
    //     //Arrange
    //     int hpId = 1, sinDate = 21000101, hokenId = 10;
    //     var mockConfiguration = new Mock<IConfiguration>();
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);

    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(
    //             "123",
    //             20,
    //             1
    //         )
    //     };

    //     var ordInf1s = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             1,
    //             21,
    //             ordInfDetailModels
    //             ),

    //     };
    //     var ordInf2s = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             0,
    //             21,
    //             ordInfDetailModels
    //             )
    //     };

    //     var byomeiModelList = new List<PtDiseaseModel>()
    //     {
    //         new PtDiseaseModel(
    //             8,
    //             10,
    //             0,
    //             1,
    //             1
    //         )
    //     };

    //     var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
    //     var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //         && p.GrpCd == 2002
    //         && p.GrpEdaNo == 0);
    //     var systemConf2 = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //       && p.GrpCd == 2002
    //       && p.GrpEdaNo == 1);
    //     var temp = systemConf?.Val ?? 0;
    //     if (systemConf != null) systemConf.Val = 0;
    //     else
    //     {
    //         systemConf = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 0,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 0
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     if (systemConf2 != null) systemConf2.Val = 0;
    //     else
    //     {
    //         systemConf2 = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 1,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 0
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     try
    //     {
    //         tenantTracking.SaveChanges();

    //         // Act
    //         var checkModels = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModels, ordInf1s.Union(ordInf2s).ToList());

    //         //Assert
    //         Assert.True(checkModels.Count == 1 && checkModels.First().InOutKbn == 1 && checkModels.First().CheckingType == CheckingType.MissingCalculate && checkModels.First().ItemName == "特定疾患処方管理加算１（処方箋料）" && !checkModels.First().Santei);

    //     }
    //     finally
    //     {
    //         systemConf.Val = temp;
    //         systemConf2.Val = temp;
    //         tenantTracking.SaveChanges();
    //     }

    // }

    // [Test]
    // public void TouyakuTokusyoSyoho_099_SystemSetting1_InHospistal_LessThan28()
    // {
    //     //Arrange
    //     int hpId = 1, sinDate = 21000101, hokenId = 10;
    //     var mockConfiguration = new Mock<IConfiguration>();
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);

    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(
    //             "123",
    //             20,
    //             1
    //         )
    //     };

    //     var ordInf1s = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             0,
    //             21,
    //             ordInfDetailModels
    //             ),

    //     };

    //     var byomeiModelList = new List<PtDiseaseModel>()
    //     {
    //         new PtDiseaseModel(
    //             8,
    //             10,
    //             0,
    //             1,
    //             1
    //         )
    //     };

    //     var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
    //     var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //         && p.GrpCd == 2002
    //         && p.GrpEdaNo == 0);
    //     var systemConf2 = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //       && p.GrpCd == 2002
    //       && p.GrpEdaNo == 1);
    //     var temp = systemConf?.Val ?? 0;
    //     if (systemConf != null) systemConf.Val = 0;
    //     else
    //     {
    //         systemConf = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 0,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 0
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     if (systemConf2 != null) systemConf2.Val = 0;
    //     else
    //     {
    //         systemConf2 = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 1,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 0
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     try
    //     {
    //         tenantTracking.SaveChanges();

    //         // Act
    //         var checkModels = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModels, ordInf1s);

    //         //Assert
    //         Assert.True(checkModels.Count == 1 && checkModels.First().InOutKbn == 0 && checkModels.First().CheckingType == CheckingType.MissingCalculate && checkModels.First().ItemName == "特定疾患処方管理加算１（処方料）");

    //     }
    //     finally
    //     {
    //         systemConf.Val = temp;
    //         systemConf2.Val = temp;
    //         tenantTracking.SaveChanges();
    //     }

    // }

    [Test]
    public void TouyakuTokusyoSyoho_100_SystemSetting1_NoMainDisease_OutHospistal_LessThan28()
    {
        //Arrange
        int hpId = 1, sinDate = 21000101, hokenId = 10;
        var mockConfiguration = new Mock<IConfiguration>();
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);

        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "123",
                20,
                1
            )
        };

        var ordInfs = new List<OrdInfModel>() {
            new OrdInfModel(
                1,
                21,
                ordInfDetailModels
                ),

        };

        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                8,
                10,
                0,
                1,
                0
            )
        };

        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
        var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
            && p.GrpCd == 2002
            && p.GrpEdaNo == 0);
        var systemConf2 = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
          && p.GrpCd == 2002
          && p.GrpEdaNo == 1);
        var temp = systemConf?.Val ?? 0;
        if (systemConf != null) systemConf.Val = 1;
        else
        {
            systemConf = new SystemConf
            {
                HpId = 1,
                GrpCd = 2002,
                GrpEdaNo = 0,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenantTracking.SystemConfs.Add(systemConf);
        }
        if (systemConf2 != null) systemConf2.Val = 0;
        else
        {
            systemConf2 = new SystemConf
            {
                HpId = 1,
                GrpCd = 2002,
                GrpEdaNo = 1,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 0
            };
            tenantTracking.SystemConfs.Add(systemConf);
        }
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(hpId,2002,0,0,"",""),
            new SystemConfModel(hpId,2002,1,0,"",""),
            new SystemConfModel(hpId,2002,2,0,"",""),
            new SystemConfModel(hpId,2002,3,0,"",""),
            new SystemConfModel(hpId,2002,5,0,"",""),
            new SystemConfModel(hpId,2002,6,0,"","")
        };
        try
        {
            tenantTracking.SaveChanges();

            // Act
            var checkModels = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModels, ordInfs, systemConfModels);

            //Assert
            Assert.True(checkModels.Count == 0);

        }
        finally
        {
            systemConf.Val = temp;
            systemConf2.Val = temp;
            tenantTracking.SaveChanges();
        }

    }

    // [Test]
    // public void TouyakuTokusyoSyoho_101_SystemSetting1_MainDisease_OutHospistal_LessThan28()
    // {
    //     //Arrange
    //     int hpId = 1, sinDate = 21000101, hokenId = 10;
    //     var mockConfiguration = new Mock<IConfiguration>();
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);

    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(
    //             "123",
    //             20,
    //             1
    //         )
    //     };

    //     var ordInfs = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             1,
    //             21,
    //             ordInfDetailModels
    //             ),

    //     };

    //     var byomeiModelList = new List<PtDiseaseModel>()
    //     {
    //         new PtDiseaseModel(
    //             8,
    //             10,
    //             0,
    //             1,
    //             1
    //         )
    //     };

    //     var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
    //     var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //         && p.GrpCd == 2002
    //         && p.GrpEdaNo == 0);
    //     var systemConf2 = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //       && p.GrpCd == 2002
    //       && p.GrpEdaNo == 1);
    //     var temp = systemConf?.Val ?? 0;
    //     if (systemConf != null) systemConf.Val = 1;
    //     else
    //     {
    //         systemConf = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 0,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 1
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     if (systemConf2 != null) systemConf2.Val = 0;
    //     else
    //     {
    //         systemConf2 = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 1,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 0
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     try
    //     {
    //         tenantTracking.SaveChanges();

    //         // Act
    //         var checkModels = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModels, ordInfs);

    //         //Assert
    //         Assert.True(checkModels.Count == 1 && checkModels.First().InOutKbn == 1 && checkModels.First().CheckingType == CheckingType.MissingCalculate && checkModels.First().ItemName == "特定疾患処方管理加算１（処方箋料）" && !checkModels.First().Santei);

    //     }
    //     finally
    //     {
    //         systemConf.Val = temp;
    //         systemConf2.Val = temp;
    //         tenantTracking.SaveChanges();
    //     }

    // }

    [Test]
    public void TouyakuTokusyoSyoho_102_SystemSetting1_MainDisease_NoMapDrug_OutHospistal_LessThan28()
    {
        //Arrange
        int hpId = 1, sinDate = 20000101, hokenId = 10;
        var mockConfiguration = new Mock<IConfiguration>();
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object, mockCommon.Object);

        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
               "123",
                20,
                1
            )
        };

        var ordInfs = new List<OrdInfModel>() {
            new OrdInfModel(
                1,
                21,
                ordInfDetailModels
                ),

        };

        var byomeiModelList = new List<PtDiseaseModel>()
        {
            new PtDiseaseModel(
                8,
                10,
                0,
                1,
                1
            )
        };

        var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
        var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
            && p.GrpCd == 2002
            && p.GrpEdaNo == 0);
        var systemConf2 = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
          && p.GrpCd == 2002
          && p.GrpEdaNo == 1);
        var temp = systemConf?.Val ?? 0;
        if (systemConf != null) systemConf.Val = 1;
        else
        {
            systemConf = new SystemConf
            {
                HpId = 1,
                GrpCd = 2002,
                GrpEdaNo = 0,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenantTracking.SystemConfs.Add(systemConf);
        }
        if (systemConf2 != null) systemConf2.Val = 1;
        else
        {
            systemConf2 = new SystemConf
            {
                HpId = 1,
                GrpCd = 2002,
                GrpEdaNo = 1,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenantTracking.SystemConfs.Add(systemConf);
        }
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(hpId,2002,0,0,"",""),
            new SystemConfModel(hpId,2002,1,0,"",""),
            new SystemConfModel(hpId,2002,2,0,"",""),
            new SystemConfModel(hpId,2002,3,0,"",""),
            new SystemConfModel(hpId,2002,5,0,"",""),
            new SystemConfModel(hpId,2002,6,0,"","")
        };
        try
        {
            tenantTracking.SaveChanges();

            // Act
            var checkModels = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModels, ordInfs, systemConfModels);

            //Assert
            Assert.True(checkModels.Count == 0);
        }
        finally
        {
            systemConf.Val = temp;
            systemConf2.Val = temp;
            tenantTracking.SaveChanges();
        }
    }

    // [Test]
    // public void TouyakuTokusyoSyoho_103_SystemSetting1_MainDisease_MapDrug_OutHospistal_LessThan28()
    // {
    //     //Arrange
    //     int hpId = 1, sinDate = 21000101, hokenId = 10;
    //     var mockConfiguration = new Mock<IConfiguration>();
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);
    //     string byomeiCd = "0810810", itemCd = "**********";

    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(
    //             itemCd,
    //             20,
    //             1
    //         )
    //     };

    //     var ordInfs = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             1,
    //             21,
    //             ordInfDetailModels
    //             ),

    //     };

    //     var byomeiModelList = new List<PtDiseaseModel>()
    //     {
    //         new PtDiseaseModel(
    //             8,
    //             10,
    //             1,
    //             0,
    //             22000101,
    //             1,
    //             byomeiCd
    //         )
    //     };

    //     var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
    //     var byomeiMsts = CheckedOrderData.ReadByomeiMst(byomeiCd);
    //     var tekiouByomeiMsts = CheckedOrderData.ReadTekiouByomeiMst(byomeiCd, itemCd);
    //     tenantTracking.ByomeiMsts.AddRange(byomeiMsts);
    //     tenantTracking.TekiouByomeiMsts.AddRange(tekiouByomeiMsts);
    //     var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //         && p.GrpCd == 2002
    //         && p.GrpEdaNo == 0);
    //     var systemConf2 = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //       && p.GrpCd == 2002
    //       && p.GrpEdaNo == 1);
    //     var temp = systemConf?.Val ?? 0;
    //     if (systemConf != null) systemConf.Val = 1;
    //     else
    //     {
    //         systemConf = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 0,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 1
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     if (systemConf2 != null) systemConf2.Val = 1;
    //     else
    //     {
    //         systemConf2 = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 1,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 1
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     try
    //     {
    //         tenantTracking.SaveChanges();

    //         // Act
    //         var checkModels = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModels, ordInfs);

    //         //Assert
    //         Assert.True(checkModels.Count == 1 && checkModels.First().InOutKbn == 1 && checkModels.First().CheckingType == CheckingType.MissingCalculate && checkModels.First().ItemName == "特定疾患処方管理加算１（処方箋料）");

    //     }
    //     finally
    //     {
    //         systemConf.Val = temp;
    //         systemConf2.Val = temp;
    //         tenantTracking.ByomeiMsts.RemoveRange(byomeiMsts);
    //         tenantTracking.TekiouByomeiMsts.RemoveRange(tekiouByomeiMsts);
    //         tenantTracking.SaveChanges();
    //     }

    // }

    // /// <summary>
    // /// Check day > 28
    // /// </summary>
    // [Test]
    // public void TouyakuTokusyoSyoho_104_SystemSetting2_OutHospistal_MoreThan28()
    // {
    //     //Arrange
    //     int hpId = 1, sinDate = 21000101, hokenId = 10;
    //     var mockConfiguration = new Mock<IConfiguration>();
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);

    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(
    //             "1221",
    //             20,
    //             1
    //         ),
    //         new OrdInfDetailModel(
    //             21,
    //             30
    //         )
    //     };
    //     var ordInfs = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             1,
    //             21,
    //             ordInfDetailModels
    //             ),

    //     };

    //     var byomeiModelList = new List<PtDiseaseModel>()
    //     {
    //         new PtDiseaseModel(
    //             8,
    //             10,
    //             1,
    //             0,
    //             1,
    //             1,
    //             "8846347"
    //         )
    //     };

    //     var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
    //     var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //         && p.GrpCd == 2002
    //         && p.GrpEdaNo == 2);
    //     var systemConf2 = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //       && p.GrpCd == 2002
    //       && p.GrpEdaNo == 3);
    //     var temp = systemConf?.Val ?? 0;
    //     if (systemConf != null) systemConf.Val = 0;
    //     else
    //     {
    //         systemConf = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 2,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 0
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     if (systemConf2 != null) systemConf2.Val = 0;
    //     else
    //     {
    //         systemConf2 = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 3,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 0
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     try
    //     {
    //         tenantTracking.SaveChanges();

    //         // Act
    //         var checkModels = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModels, ordInfs);

    //         //Assert
    //         Assert.True(checkModels.Count == 1 && checkModels.First().InOutKbn == 1 && checkModels.First().CheckingType == CheckingType.MissingCalculate && checkModels.First().ItemName == "特定疾患処方管理加算２（処方箋料）" && !checkModels.First().Santei);

    //     }
    //     finally
    //     {
    //         systemConf.Val = temp;
    //         systemConf2.Val = temp;
    //         tenantTracking.SaveChanges();
    //     }
    // }

    // [Test]
    // public void TouyakuTokusyoSyoho_105_SystemSetting2_InOutHospistal_MoreThan28()
    // {
    //     //Arrange
    //     int hpId = 1, sinDate = 21000101, hokenId = 10;
    //     var mockConfiguration = new Mock<IConfiguration>();
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);

    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //          new OrdInfDetailModel(
    //             "1221",
    //             20,
    //             1
    //         ),
    //         new OrdInfDetailModel(
    //             21,
    //             30
    //         )
    //     };

    //     var ordInf1s = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             1,
    //             21,
    //             ordInfDetailModels
    //             ),

    //     };
    //     var ordInf2s = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             0,
    //             21,
    //             ordInfDetailModels
    //             )
    //     };

    //     var byomeiModelList = new List<PtDiseaseModel>()
    //     {
    //         new PtDiseaseModel(
    //             8,
    //             10,
    //             0,
    //             1,
    //             1
    //         )
    //     };

    //     var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
    //     var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //         && p.GrpCd == 2002
    //         && p.GrpEdaNo == 2);
    //     var systemConf2 = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //       && p.GrpCd == 2002
    //       && p.GrpEdaNo == 3);
    //     var temp = systemConf?.Val ?? 0;
    //     if (systemConf != null) systemConf.Val = 0;
    //     else
    //     {
    //         systemConf = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 0,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 0
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     if (systemConf2 != null) systemConf2.Val = 0;
    //     else
    //     {
    //         systemConf2 = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 1,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 0
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     try
    //     {
    //         tenantTracking.SaveChanges();

    //         // Act
    //         var checkModels = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModels, ordInf1s.Union(ordInf2s).ToList());

    //         //Assert
    //         Assert.True(checkModels.Count == 1 && checkModels.First().InOutKbn == 1 && checkModels.First().CheckingType == CheckingType.MissingCalculate && checkModels.First().ItemName == "特定疾患処方管理加算２（処方箋料）" && !checkModels.First().Santei);

    //     }
    //     finally
    //     {
    //         systemConf.Val = temp;
    //         systemConf2.Val = temp;
    //         tenantTracking.SaveChanges();
    //     }

    // }

    // [Test]
    // public void TouyakuTokusyoSyoho_106_SystemSetting2_InHospistal_MoreThan28()
    // {
    //     //Arrange
    //     int hpId = 1, sinDate = 21000101, hokenId = 10;
    //     var mockConfiguration = new Mock<IConfiguration>();
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);

    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(
    //             "1221",
    //             20,
    //             1
    //         ),
    //         new OrdInfDetailModel(
    //             21,
    //             30
    //         )
    //     };

    //     var ordInf1s = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             0,
    //             21,
    //             ordInfDetailModels
    //             ),

    //     };
    //     var ordInf2s = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             0,
    //             21,
    //             ordInfDetailModels
    //             )
    //     };

    //     var byomeiModelList = new List<PtDiseaseModel>()
    //     {
    //         new PtDiseaseModel(
    //             8,
    //             10,
    //             0,
    //             1,
    //             1
    //         )
    //     };

    //     var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
    //     var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //         && p.GrpCd == 2002
    //         && p.GrpEdaNo == 2);
    //     var systemConf2 = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //       && p.GrpCd == 2002
    //       && p.GrpEdaNo == 3);
    //     var temp = systemConf?.Val ?? 0;
    //     if (systemConf != null) systemConf.Val = 0;
    //     else
    //     {
    //         systemConf = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 0,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 0
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     if (systemConf2 != null) systemConf2.Val = 0;
    //     else
    //     {
    //         systemConf2 = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 1,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 0
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     try
    //     {
    //         tenantTracking.SaveChanges();

    //         // Act
    //         var checkModels = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModels, ordInf1s.Union(ordInf2s).ToList());

    //         //Assert
    //         Assert.True(checkModels.Count == 1 && checkModels.First().InOutKbn == 0 && checkModels.First().CheckingType == CheckingType.MissingCalculate && checkModels.First().ItemName == "特定疾患処方管理加算２（処方料）" && !checkModels.First().Santei);

    //     }
    //     finally
    //     {
    //         systemConf.Val = temp;
    //         systemConf2.Val = temp;
    //         tenantTracking.SaveChanges();
    //     }
    // }

    // [Test]
    // public void TouyakuTokusyoSyoho_107_SystemSetting2_NoMainDisease_OutHospistal_MoreThan28()
    // {
    //     //Arrange
    //     int hpId = 1, sinDate = 21000101, hokenId = 10;
    //     var mockConfiguration = new Mock<IConfiguration>();
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);

    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(
    //             "1221",
    //             20,
    //             1
    //         ),
    //         new OrdInfDetailModel(
    //             21,
    //             30
    //         )
    //     };

    //     var ordInfs = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             1,
    //             21,
    //             ordInfDetailModels
    //             ),

    //     };

    //     var byomeiModelList = new List<PtDiseaseModel>()
    //     {
    //         new PtDiseaseModel(
    //             8,
    //             10,
    //             0,
    //             1,
    //             0
    //         )
    //     };

    //     var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
    //     var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //         && p.GrpCd == 2002
    //         && p.GrpEdaNo == 2);
    //     var systemConf2 = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //       && p.GrpCd == 2002
    //       && p.GrpEdaNo == 3);
    //     var temp = systemConf?.Val ?? 0;
    //     if (systemConf != null) systemConf.Val = 1;
    //     else
    //     {
    //         systemConf = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 0,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 1
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     if (systemConf2 != null) systemConf2.Val = 0;
    //     else
    //     {
    //         systemConf2 = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 1,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 0
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     try
    //     {
    //         tenantTracking.SaveChanges();

    //         // Act
    //         var checkModels = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModels, ordInfs);

    //         //Assert
    //         Assert.True(checkModels.Count == 0);
    //     }
    //     finally
    //     {
    //         systemConf.Val = temp;
    //         systemConf2.Val = temp;
    //         tenantTracking.SaveChanges();
    //     }
    // }

    // [Test]
    // public void TouyakuTokusyoSyoho_108_SystemSetting2_MainDisease_OutHospistal_MoreThan28()
    // {
    //     //Arrange
    //     int hpId = 1, sinDate = 21000101, hokenId = 10;
    //     var mockConfiguration = new Mock<IConfiguration>();
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);

    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(
    //             "1221",
    //             20,
    //             1
    //         ),
    //         new OrdInfDetailModel(
    //             21,
    //             30
    //         )
    //     };

    //     var ordInfs = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             1,
    //             21,
    //             ordInfDetailModels
    //             ),

    //     };

    //     var byomeiModelList = new List<PtDiseaseModel>()
    //     {
    //         new PtDiseaseModel(
    //             8,
    //             10,
    //             1,
    //             1,
    //             22000101,
    //             1,
    //             "8846347"
    //         )
    //     };

    //     var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
    //     var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //         && p.GrpCd == 2002
    //         && p.GrpEdaNo == 2);
    //     var systemConf2 = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //       && p.GrpCd == 2002
    //       && p.GrpEdaNo == 3);
    //     var temp = systemConf?.Val ?? 0;
    //     if (systemConf != null) systemConf.Val = 1;
    //     else
    //     {
    //         systemConf = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 0,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 1
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     if (systemConf2 != null) systemConf2.Val = 0;
    //     else
    //     {
    //         systemConf2 = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 1,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 0
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     try
    //     {
    //         tenantTracking.SaveChanges();

    //         // Act
    //         var checkModels = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModels, ordInfs);

    //         //Assert
    //         Assert.True(checkModels.Count == 1 && checkModels.First().InOutKbn == 1 && checkModels.First().CheckingType == CheckingType.MissingCalculate && checkModels.First().ItemName == "特定疾患処方管理加算２（処方箋料）" && !checkModels.First().Santei);
    //     }
    //     finally
    //     {
    //         systemConf.Val = temp;
    //         systemConf2.Val = temp;
    //         tenantTracking.SaveChanges();
    //     }
    // }

    // [Test]
    // public void TouyakuTokusyoSyoho_109_SystemSetting2_MainDisease_NoMapDrug_OutHospistal_MoreThan28()
    // {
    //     //Arrange
    //     int hpId = 1, sinDate = 20000101, hokenId = 10;
    //     var mockConfiguration = new Mock<IConfiguration>();
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);

    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(
    //             "1221",
    //             20,
    //             1
    //         ),
    //         new OrdInfDetailModel(
    //             21,
    //             30
    //         )
    //     };

    //     var ordInfs = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             1,
    //             21,
    //             ordInfDetailModels
    //             ),
    //     };

    //     var byomeiModelList = new List<PtDiseaseModel>()
    //     {
    //         new PtDiseaseModel(
    //             8,
    //             10,
    //             0,
    //             1,
    //             1
    //         )
    //     };

    //     var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
    //     var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //         && p.GrpCd == 2002
    //         && p.GrpEdaNo == 2);
    //     var systemConf2 = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //       && p.GrpCd == 2002
    //       && p.GrpEdaNo == 3);
    //     var temp = systemConf?.Val ?? 0;
    //     if (systemConf != null) systemConf.Val = 1;
    //     else
    //     {
    //         systemConf = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 0,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 1
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     if (systemConf2 != null) systemConf2.Val = 1;
    //     else
    //     {
    //         systemConf2 = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 1,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 1
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     try
    //     {
    //         tenantTracking.SaveChanges();

    //         // Act
    //         var checkModels = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModels, ordInfs);

    //         //Assert
    //         Assert.True(checkModels.Count == 0);

    //     }
    //     finally
    //     {
    //         systemConf.Val = temp;
    //         systemConf2.Val = temp;
    //         tenantTracking.SaveChanges();
    //     }

    // }

    // [Test]
    // public void TouyakuTokusyoSyoho_110_SystemSetting2_MainDisease_MapDrug_OutHospistal_MoreThan28()
    // {
    //     //Arrange
    //     int hpId = 1, sinDate = 21000101, hokenId = 10;
    //     var mockConfiguration = new Mock<IConfiguration>();
    //     var mockMstItem = new Mock<IMstItemRepository>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     SystemConfRepository systemConfRepository = new SystemConfRepository(TenantProvider, mockConfiguration.Object);
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, systemConfRepository, mockMstItem.Object);
    //     string byomeiCd = "0880880", itemCd = "**********";

    //     var ordInfDetailModels = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(
    //             itemCd,
    //             20,
    //             1
    //         ),
    //         new OrdInfDetailModel(
    //             21,
    //             30
    //         )
    //     };

    //     var ordInfs = new List<OrdInfModel>() {
    //         new OrdInfModel(
    //             1,
    //             21,
    //             ordInfDetailModels
    //             ),
    //     };

    //     var byomeiModelList = new List<PtDiseaseModel>()
    //     {
    //         new PtDiseaseModel(
    //             8,
    //             10,
    //             1,
    //             0,
    //             22000101,
    //             1,
    //             byomeiCd
    //         )
    //     };

    //     var tenantTracking = TenantProvider.GetTrackingTenantDataContext();
    //     var byomeiMsts = CheckedOrderData.ReadByomeiMst(byomeiCd);
    //     var tekiouByomeiMsts = CheckedOrderData.ReadTekiouByomeiMst(byomeiCd, itemCd);
    //     tenantTracking.ByomeiMsts.AddRange(byomeiMsts);
    //     tenantTracking.TekiouByomeiMsts.AddRange(tekiouByomeiMsts);
    //     var systemConf = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //         && p.GrpCd == 2002
    //         && p.GrpEdaNo == 2);
    //     var systemConf2 = tenantTracking.SystemConfs.FirstOrDefault(p => p.HpId == 1
    //       && p.GrpCd == 2002
    //       && p.GrpEdaNo == 3);
    //     var temp = systemConf?.Val ?? 0;
    //     if (systemConf != null) systemConf.Val = 1;
    //     else
    //     {
    //         systemConf = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 0,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 1
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     if (systemConf2 != null) systemConf2.Val = 1;
    //     else
    //     {
    //         systemConf2 = new SystemConf
    //         {
    //             HpId = 1,
    //             GrpCd = 2002,
    //             GrpEdaNo = 1,
    //             CreateDate = DateTime.UtcNow,
    //             UpdateDate = DateTime.UtcNow,
    //             CreateId = 1,
    //             UpdateId = 1,
    //             Val = 1
    //         };
    //         tenantTracking.SystemConfs.Add(systemConf);
    //     }
    //     try
    //     {
    //         tenantTracking.SaveChanges();

    //         // Act
    //         var checkModels = medicalExaminationRepository.TouyakuTokusyoSyoho(hpId, sinDate, hokenId, byomeiModelList, ordInfDetailModels, ordInfs);

    //         //Assert
    //         Assert.True(checkModels.Count == 1 && checkModels.First().InOutKbn == 1 && checkModels.First().CheckingType == CheckingType.MissingCalculate && checkModels.First().ItemName == "特定疾患処方管理加算２（処方箋料）" && !checkModels.First().Santei);

    //     }
    //     finally
    //     {
    //         systemConf.Val = temp;
    //         systemConf2.Val = temp;
    //         tenantTracking.ByomeiMsts.RemoveRange(byomeiMsts);
    //         tenantTracking.TekiouByomeiMsts.RemoveRange(tekiouByomeiMsts);
    //         tenantTracking.SaveChanges();
    //     }

    // }

    #endregion

    [Test]
    public void Zanyaku_111_Check_ExistOutOrder()
    {
        //Arrange
        int hpId = 1, sinDate = 21000101;
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockSystemConf = new Mock<ISystemConfRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, mockSystemConf.Object, mockMstItem.Object, mockCommon.Object);
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "123421341",
                1
            ),
            new OrdInfDetailModel(
                "123421342",
                1
            )
        };
        var ordInfs = new List<OrdInfModel>() {
            new OrdInfModel(
                1,
                21,
                new()
                )
        };

        var tenItem = new TenItemModel();

        var mock = new Mock<IMstItemRepository>();
        mock.Setup(finder => finder.GetTenMstInfo(It.IsAny<int>(), It.IsAny<string>(), It.IsAny<int>()))
            .Returns((int hpId, string itemCd, int sinDate) => null);

        // Act
        var checkModel1s = medicalExaminationRepository.Zanyaku(hpId, sinDate, new(), new());

        //Assert
        Assert.True(checkModel1s.Count == 0);
    }

    /// <summary>
    /// Check Zanyaku is drug
    /// </summary>
    [Test]
    public void Zanyaku_112_Drug()
    {
        //Arrange
        int hpId = 1, sinDate = 21000101;
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockSystemConf = new Mock<ISystemConfRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, mockSystemConf.Object, mockMstItem.Object, mockCommon.Object);
        var ordInfDetailModels = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(
                "123421341",
                1
            ),
            new OrdInfDetailModel(
                "123421342",
                1
            )
        };
        var ordInfs = new List<OrdInfModel>() {
            new OrdInfModel(
                1,
                21,
                new()
                )
        };

        var tenItem = new TenItemModel();

        var mock = new Mock<IMstItemRepository>();
        mock.Setup(finder => finder.GetTenMstInfo(It.IsAny<int>(), It.IsAny<string>(), It.IsAny<int>()))
            .Returns((int hpId, string itemCd, int sinDate) => null);

        // Act
        var checkModel1s = medicalExaminationRepository.Zanyaku(hpId, sinDate, ordInfDetailModels, ordInfs);

        //Assert
        Assert.True(checkModel1s.Count == 0);
    }

    [Test]
    public void TrialIryoJyohoKibanCalculation_113_AutoSanteiItem()
    {
        int hpId = 1; long ptId = 999; int sinDate = 20240402; long raiinNo = 123;
        List<OrdInfDetailModel> allOdrInfDetail = new List<OrdInfDetailModel>();
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockSystemConf = new Mock<ISystemConfRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, mockSystemConf.Object, mockMstItem.Object, mockCommon.Object);

        //Act
        var result = medicalExaminationRepository.TrialIryoJyohoKibanCalculation(hpId, ptId, sinDate, raiinNo, allOdrInfDetail);
        //Assert
        Assert.True(result.Count == 0);
    }

    [Test]
    public void TrialIryoJyohoKibanCalculation_114_Check_ExistAutoItem()
    {
        var tenant = TenantProvider.GetTrackingTenantDataContext();
        var autoSanteiMsts = CheckedOrderData.ReadAutoSanteiMst();
        tenant.AddRange(autoSanteiMsts);
        int hpId = 1; long ptId = 999; int sinDate = 20240402; long raiinNo = 123;
        List<OrdInfDetailModel> allOdrInfDetail = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel("111015970",1)
        };
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockMstItem = new Mock<IMstItemRepository>();
        var mockSystemConf = new Mock<ISystemConfRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, mockSystemConf.Object, mockMstItem.Object, mockCommon.Object);
        try
        {
            tenant.SaveChanges();
            //Act
            var result = medicalExaminationRepository.TrialIryoJyohoKibanCalculation(hpId, ptId, sinDate, raiinNo, allOdrInfDetail);
            //Assert
            Assert.True(result.Count == 0);
        }
        finally
        {
            tenant.RemoveRange(autoSanteiMsts);
            tenant.SaveChanges();
        }

    }

    // [Test]
    // public void TrialIryoJyohoKibanCalculation_115_Check_isExistFirstVisit()
    // {
    //     var tenant = TenantProvider.GetTrackingTenantDataContext();
    //     var autoSanteiMsts = CheckedOrderData.ReadAutoSanteiMst();
    //     var tenMsts = CheckedOrderData.ReadTenMst();
    //     tenMsts[0].ItemCd = ItemCdConst.SyosinIryoJyohoKiban1;
    //     tenant.AddRange(autoSanteiMsts);
    //     tenant.Add(tenMsts[0]);
    //     int hpId = 1; long ptId = 999; int sinDate = 20240402; long raiinNo = 123;
    //     List<OrdInfDetailModel> allOdrInfDetail = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(hpId,ItemCdConst.SyosaiKihon,sinDate,1)
    //     };
    //     var mockConfiguration = new Mock<IConfiguration>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     var mockOptionsAccessor = new Mock<IOptions<AmazonS3Options>>();
    //     var mstItemRepository = new MstItemRepository(TenantProvider, mockOptionsAccessor.Object);
    //     var mockSystemConf = new Mock<ISystemConfRepository>();
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, mockSystemConf.Object, mstItemRepository);
    //     try
    //     {
    //         tenant.SaveChanges();
    //         //Act
    //         var result = medicalExaminationRepository.TrialIryoJyohoKibanCalculation(hpId, ptId, sinDate, raiinNo, allOdrInfDetail);
    //         //Assert
    //         Assert.True(result.Count == 1 && result.Any(i => i.ItemCd == ItemCdConst.SyosinIryoJyohoKiban1));
    //     }
    //     finally
    //     {
    //         tenant.RemoveRange(autoSanteiMsts);
    //         tenant.Remove(tenMsts[0]);
    //         tenant.SaveChanges();
    //     }

    // }

    // [Test]
    // public void TrialIryoJyohoKibanCalculation_116_Check_isExistFirstVisit()
    // {
    //     var tenant = TenantProvider.GetTrackingTenantDataContext();
    //     var autoSanteiMsts = CheckedOrderData.ReadAutoSanteiMst();
    //     var tenMsts = CheckedOrderData.ReadTenMst();
    //     tenMsts[0].ItemCd = ItemCdConst.IgakuIryoJyohoKiban1;
    //     tenant.AddRange(autoSanteiMsts);
    //     tenant.Add(tenMsts[0]);
    //     int hpId = 1; long ptId = 999; int sinDate = 20240402; long raiinNo = 123;
    //     List<OrdInfDetailModel> allOdrInfDetail = new List<OrdInfDetailModel>()
    //     {
    //         new OrdInfDetailModel(hpId,ItemCdConst.SyosaiKihon,sinDate,1)
    //     };
    //     var mockConfiguration = new Mock<IConfiguration>();
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
    //     mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
    //     var mockOptionsAccessor = new Mock<IOptions<AmazonS3Options>>();
    //     var mstItemRepository = new MstItemRepository(TenantProvider, mockOptionsAccessor.Object);
    //     var mockSystemConf = new Mock<ISystemConfRepository>();
    //     MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, mockSystemConf.Object, mstItemRepository);
    //     try
    //     {
    //         tenant.SaveChanges();
    //         //Act
    //         var result = medicalExaminationRepository.TrialIryoJyohoKibanCalculation(hpId, ptId, sinDate, raiinNo, allOdrInfDetail);
    //         //Assert
    //         Assert.True(result.Count == 1 && result.Any(i => i.ItemCd == ItemCdConst.IgakuIryoJyohoKiban1));
    //     }
    //     finally
    //     {
    //         tenant.RemoveRange(autoSanteiMsts);
    //         tenant.Remove(tenMsts[0]);
    //         tenant.SaveChanges();
    //     }

    // }

    [Test]
    public void TrialIryoJyohoKibanCalculation_117_Check_isExistReturnVisit()
    {
        var tenant = TenantProvider.GetTrackingTenantDataContext();
        var autoSanteiMsts = CheckedOrderData.ReadAutoSanteiMst();
        var tenMsts = CheckedOrderData.ReadTenMst();
        tenMsts[0].ItemCd = ItemCdConst.SaisinIryoJyohoKiban3;
        tenant.AddRange(autoSanteiMsts);
        tenant.Add(tenMsts[0]);
        int hpId = 1; long ptId = 999; int sinDate = 20240402; long raiinNo = 123;

        List<OrdInfDetailModel> allOdrInfDetail = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(hpId,ItemCdConst.SyosaiKihon,sinDate,3)
        };
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockOptionsAccessor = new Mock<IOptions<AmazonS3Options>>();
        var mstItemRepository = new MstItemRepository(TenantProvider, mockOptionsAccessor.Object, TenantProvider, TenantProvider, TenantProvider, TenantProvider, TenantProvider, TenantProvider);
        var mockSystemConf = new Mock<ISystemConfRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, mockSystemConf.Object, mstItemRepository, mockCommon.Object);
        try
        {
            tenant.SaveChanges();
            //Act
            var result = medicalExaminationRepository.TrialIryoJyohoKibanCalculation(hpId, ptId, sinDate, raiinNo, allOdrInfDetail);
            //Assert
            Assert.True(result.Count == 1 && result.Any(i => i.ItemCd == ItemCdConst.SaisinIryoJyohoKiban3));
        }
        finally
        {
            tenant.RemoveRange(autoSanteiMsts);
            tenant.Remove(tenMsts[0]);
            tenant.SaveChanges();
        }

    }

    [Test]
    public void TrialIryoJyohoKibanCalculation_118_Check_isExistReturnVisit()
    {
        var tenant = TenantProvider.GetTrackingTenantDataContext();
        var autoSanteiMsts = CheckedOrderData.ReadAutoSanteiMst();
        var tenMsts = CheckedOrderData.ReadTenMst();
        tenMsts[0].ItemCd = ItemCdConst.IgakuIryoJyohoKiban3;
        tenant.AddRange(autoSanteiMsts);
        tenant.Add(tenMsts[0]);
        int hpId = 1; long ptId = 999; int sinDate = 20240402; long raiinNo = 123;

        List<OrdInfDetailModel> allOdrInfDetail = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel(hpId,ItemCdConst.SyosaiKihon,sinDate,3)
        };
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockOptionsAccessor = new Mock<IOptions<AmazonS3Options>>();
        var mstItemRepository = new MstItemRepository(TenantProvider, mockOptionsAccessor.Object, TenantProvider, TenantProvider, TenantProvider, TenantProvider, TenantProvider, TenantProvider);
        var mockSystemConf = new Mock<ISystemConfRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, mockSystemConf.Object, mstItemRepository, mockCommon.Object);
        try
        {
            tenant.SaveChanges();
            //Act
            var result = medicalExaminationRepository.TrialIryoJyohoKibanCalculation(hpId, ptId, sinDate, raiinNo, allOdrInfDetail);
            //Assert
            Assert.True(result.Count == 1 && result.Any(i => i.ItemCd == ItemCdConst.IgakuIryoJyohoKiban3));
        }
        finally
        {
            tenant.RemoveRange(autoSanteiMsts);
            tenant.Remove(tenMsts[0]);
            tenant.SaveChanges();
        }

    }

    [Test]
    public void IgakuTokusituIsChecked_119()
    {
        int hpId = 1; int sinDate = 20220202; int syosaisinKbn = 1;
        List<CheckedOrderModel> checkedOrders = new List<CheckedOrderModel>();
        List<OrdInfDetailModel> allOdrInfDetail = new List<OrdInfDetailModel>();
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockOptionsAccessor = new Mock<IOptions<AmazonS3Options>>();
        var mstItemRepository = new MstItemRepository(TenantProvider, mockOptionsAccessor.Object, TenantProvider, TenantProvider, TenantProvider, TenantProvider, TenantProvider, TenantProvider);
        var mockSystemConf = new Mock<ISystemConfRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(1,4001,4,0,"","")
        };
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, mockSystemConf.Object, mstItemRepository, mockCommon.Object);
        try
        {
            //Act
            var result = medicalExaminationRepository.IgakuTokusituIsChecked(hpId, sinDate, syosaisinKbn, checkedOrders, allOdrInfDetail, systemConfModels);
            //Assert
            Assert.True(result.Count == 0);
        }
        finally
        {

        }
    }

    [Test]
    public void IgakuTokusituIsChecked_120()
    {
        int hpId = 1; int sinDate = 20220202; int syosaisinKbn = 0;
        List<CheckedOrderModel> checkedOrders = new List<CheckedOrderModel>()
        {
            new CheckedOrderModel(CheckingType.Order, true, "",ItemCdConst.IgakuTokusitu, 1,"", 1)
        };
        List<OrdInfDetailModel> allOdrInfDetail = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel("Y90298",1)
        };
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockOptionsAccessor = new Mock<IOptions<AmazonS3Options>>();
        var mstItemRepository = new MstItemRepository(TenantProvider, mockOptionsAccessor.Object, TenantProvider, TenantProvider, TenantProvider, TenantProvider, TenantProvider, TenantProvider);
        var mockSystemConf = new Mock<ISystemConfRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(1,4001,4,0,"","")
        };
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, mockSystemConf.Object, mstItemRepository, mockCommon.Object);
        try
        {
            //Act
            var result = medicalExaminationRepository.IgakuTokusituIsChecked(hpId, sinDate, syosaisinKbn, checkedOrders, allOdrInfDetail, systemConfModels);
            //Assert
            Assert.True(result.Count == 1 && !result[0].Santei);
        }
        finally
        {

        }
    }

    [Test]
    public void IgakuTokusituIsChecked_121()
    {
        int hpId = 1; int sinDate = 20220202; int syosaisinKbn = 0;
        List<CheckedOrderModel> checkedOrders = new List<CheckedOrderModel>()
        {
            new CheckedOrderModel(CheckingType.Order, true, "",ItemCdConst.IgakuTokusitu, 1,"", 1)
        };
        List<OrdInfDetailModel> allOdrInfDetail = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel("Y90299",1)
        };
        var tenant = TenantProvider.GetTrackingTenantDataContext();
        var tenMsts = CheckedOrderData.ReadTenMst();
        tenant.AddRange(tenMsts);
        var systemConf = tenant.SystemConfs.FirstOrDefault(p => p.HpId == hpId && p.GrpCd == 4001 && p.GrpEdaNo == 0);
        var temp = systemConf?.Val ?? 0;
        if (systemConf != null) systemConf.Val = 1;
        else
        {
            systemConf = new SystemConf
            {
                HpId = hpId,
                GrpCd = 4001,
                GrpEdaNo = 0,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 1
            };
            tenant.SystemConfs.Add(systemConf);
        }
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockOptionsAccessor = new Mock<IOptions<AmazonS3Options>>();
        var mstItemRepository = new MstItemRepository(TenantProvider, mockOptionsAccessor.Object, TenantProvider, TenantProvider, TenantProvider, TenantProvider, TenantProvider, TenantProvider);
        var mockSystemConf = new Mock<ISystemConfRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(1,4001,4,0,"","")
        };
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, mockSystemConf.Object, mstItemRepository, mockCommon.Object);
        try
        {
            tenant.SaveChanges();
            //Act
            var result = medicalExaminationRepository.IgakuTokusituIsChecked(hpId, sinDate, syosaisinKbn, checkedOrders, allOdrInfDetail, systemConfModels);
            //Assert
            Assert.True(result.Count == 1 && result[0].Santei);
        }
        finally
        {
            systemConf.Val = temp;
            tenant.RemoveRange(tenMsts);
            tenant.SaveChanges();
        }
    }

    [Test]
    public void IgakuTokusituIsChecked_122()
    {
        int hpId = 1; int sinDate = 20220202; int syosaisinKbn = 0;
        List<CheckedOrderModel> checkedOrders = new List<CheckedOrderModel>()
        {
            new CheckedOrderModel(CheckingType.Order, true, "",ItemCdConst.IgakuTokusitu, 1,"", 1)
        };
        List<OrdInfDetailModel> allOdrInfDetail = new List<OrdInfDetailModel>()
        {
            new OrdInfDetailModel("Y90299",1)
        };
        var tenant = TenantProvider.GetTrackingTenantDataContext();
        var tenMsts = CheckedOrderData.ReadTenMst();
        tenant.AddRange(tenMsts);
        var systemConf = tenant.SystemConfs.FirstOrDefault(p => p.HpId == hpId && p.GrpCd == 4001 && p.GrpEdaNo == 0);
        var temp = systemConf?.Val ?? 0;
        if (systemConf != null) systemConf.Val = 0;
        else
        {
            systemConf = new SystemConf
            {
                HpId = hpId,
                GrpCd = 4001,
                GrpEdaNo = 0,
                CreateDate = DateTime.UtcNow,
                UpdateDate = DateTime.UtcNow,
                CreateId = 1,
                UpdateId = 1,
                Val = 0
            };
            tenant.SystemConfs.Add(systemConf);
        }
        var mockConfiguration = new Mock<IConfiguration>();
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("**********");
        mockConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");
        var mockOptionsAccessor = new Mock<IOptions<AmazonS3Options>>();
        var mstItemRepository = new MstItemRepository(TenantProvider, mockOptionsAccessor.Object, TenantProvider, TenantProvider, TenantProvider, TenantProvider, TenantProvider, TenantProvider);
        var mockSystemConf = new Mock<ISystemConfRepository>();
        var mockCommon = new Mock<ICommonRepository>();
        var systemConfModels = new List<SystemConfModel>()
        {
            new SystemConfModel(1,4001,4,0,"","")
        };
        MedicalExaminationRepository medicalExaminationRepository = new MedicalExaminationRepository(TenantProvider, mockSystemConf.Object, mstItemRepository, mockCommon.Object);
        try
        {
            tenant.SaveChanges();
            //Act
            var result = medicalExaminationRepository.IgakuTokusituIsChecked(hpId, sinDate, syosaisinKbn, checkedOrders, allOdrInfDetail, systemConfModels);
            //Assert
            Assert.True(result.Count == 1 && !result[0].Santei);
        }
        finally
        {
            systemConf.Val = temp;
            tenant.RemoveRange(tenMsts);
            tenant.SaveChanges();
        }
    }
}