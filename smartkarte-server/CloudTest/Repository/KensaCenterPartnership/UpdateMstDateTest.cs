using Helper.Constants;
using Infrastructure.Repositories;
using Microsoft.EntityFrameworkCore;
using PostgreDataContext;
using Entity.Tenant;
using Npgsql;

namespace CloudUnitTest.Repository.KensaCenterPartnership;

public class UpdateTenMstTest : BaseUT
{
    private const string notSanteiItemCd = "9999999999";

    [Test]
    public void TC_001_UpdateTenMst_Success_WhenOneRecordRegistered()
    {
        // Arrange
        SetupTestEnvironment(out KensaCenterPartnershipRepository repository, out TenantDataContext tenant);

        int hpId = 1;
        string centerCd = "T001";
        int startDate = 20240101;
        int endDate = 20241231;

        // テストデータを準備
        PrepareTestData(tenant, centerCd, startDate, endDate);

        try
        {
            // Act
            var result = repository.RegisterKensaCenterPartnership(hpId, centerCd, startDate, endDate);

            // Assert
            Assert.IsNotNull(result, "正常に登録された場合はnullではない");

            // 新しいコンテキストで削除状態を確認
            using var verifyContext = TenantProvider.GetNoTrackingDataContext();
            var commonCenterItemMst = verifyContext.CommonCenterItemMst.Where(t => t.CenterCd == centerCd).ToList();
            Assert.IsTrue(commonCenterItemMst.Any(), "CommonCenterItemMstにデータが存在する");
            
            foreach (var item in commonCenterItemMst) {
                var tenMst = verifyContext.TenMsts.FirstOrDefault(t => t.HpId == hpId && t.CenterCd == item.CenterCd && t.ItemCd == item.ItemCd && t.StartDate == item.StartDate);
                Assert.IsNotNull(tenMst, "登録されたデータが存在する");
                
                // 指定された項目を比較
                if (item.SanteiItemCd == notSanteiItemCd) {
                    Assert.That(tenMst.KensaItemCd, Is.EqualTo("0"), "kensa_item_cdが一致する");     
                } else {
                    Assert.That(tenMst.KensaItemCd, Is.EqualTo(item.KensaItemCd), "kensa_item_cdが一致する");
                }
                Assert.That(tenMst.HpId, Is.EqualTo(hpId), "hp_idが一致する");
                Assert.That(tenMst.OdrUnitName, Is.EqualTo(""), "odr_unit_nameが一致する");
                Assert.That(tenMst.ItemCd, Is.EqualTo(item.ItemCd), "item_cdが一致する");
                Assert.That(tenMst.StartDate, Is.EqualTo(item.StartDate), "start_dateが一致する");
                Assert.That(tenMst.EndDate, Is.EqualTo(item.EndDate), "end_dateが一致する");
                Assert.That(tenMst.SanteiItemCd, Is.EqualTo(item.SanteiItemCd), "santei_item_cdが一致する");
                Assert.That(tenMst.CenterCd, Is.EqualTo(item.CenterCd), "center_cdが一致する");
                Assert.That(tenMst.IsAdopted, Is.EqualTo(0), "is_adoptedが一致する");
                Assert.That(tenMst.KensaLabel, Is.EqualTo(0), "kensa_labelが一致する");
                Assert.That(tenMst.Name, Is.EqualTo(item.Name), "nameが一致する");
                Assert.That(tenMst.KanaName1, Is.EqualTo(item.KanaName), "kana_nameが一致する");
                Assert.That(tenMst.KensaLabel, Is.EqualTo(0), "kensa_labelが一致する");
            }
        }
        finally
        {
            // テストデータを物理削除
            CleanupTestData(hpId, centerCd, startDate);
            repository.ReleaseResource();
        }
    }


    [Test]
    public void TC_002_UpdateTenMst_Success_WhenSecondRecordRegistered()
    {
        // Arrange
        SetupTestEnvironment(out KensaCenterPartnershipRepository repository, out TenantDataContext tenant);

        int hpId = 1;
        string centerCd = "T002";
        int startDate = 20250709;
        int endDate = 99991231;

        // テストデータを準備
        PrepareTestData(tenant, centerCd, startDate, endDate);

        try
        {

            repository.RegisterKensaCenterPartnership(hpId, centerCd, startDate, endDate);
            repository.UnregisterKensaCenterPartnership(hpId, centerCd, startDate);

            // Act
            var result = repository.RegisterKensaCenterPartnership(hpId, centerCd, startDate, endDate);

            // Assert
            Assert.IsNotNull(result, "正常に登録された場合はnullではない");

            // 新しいコンテキストで削除状態を確認
            using var verifyContext = TenantProvider.GetNoTrackingDataContext();
            var commonCenterItemMst = verifyContext.CommonCenterItemMst.Where(t => t.CenterCd == centerCd).ToList();
            Assert.IsTrue(commonCenterItemMst.Any(), "CommonCenterItemMstにデータが存在する");
            
            foreach (var item in commonCenterItemMst) {
                var tenMst = verifyContext.TenMsts.FirstOrDefault(t => t.HpId == hpId && t.CenterCd == item.CenterCd && t.ItemCd == item.ItemCd && t.StartDate == item.StartDate);
                Assert.IsNotNull(tenMst, "登録されたデータが存在する");
                
                if (item.SanteiItemCd == notSanteiItemCd) {
                    Assert.That(tenMst.KensaItemCd, Is.EqualTo("0"), "kensa_item_cdが一致する");     
                } else {
                    Assert.That(tenMst.KensaItemCd, Is.EqualTo(item.KensaItemCd), "kensa_item_cdが一致する");
                }
                Assert.That(tenMst.HpId, Is.EqualTo(hpId), "hp_idが一致する");
                Assert.That(tenMst.OdrUnitName, Is.EqualTo(""), "odr_unit_nameが一致する");
                Assert.That(tenMst.ItemCd, Is.EqualTo(item.ItemCd), "item_cdが一致する");
                Assert.That(tenMst.StartDate, Is.EqualTo(item.StartDate), "start_dateが一致する");
                Assert.That(tenMst.EndDate, Is.EqualTo(item.EndDate), "end_dateが一致する");
                Assert.That(tenMst.SanteiItemCd, Is.EqualTo(item.SanteiItemCd), "santei_item_cdが一致する");
                Assert.That(tenMst.CenterCd, Is.EqualTo(item.CenterCd), "center_cdが一致する");
                Assert.That(tenMst.IsAdopted, Is.EqualTo(0), "is_adoptedが一致する");
                Assert.That(tenMst.KensaLabel, Is.EqualTo(0), "kensa_labelが一致する");
                Assert.That(tenMst.Name, Is.EqualTo(item.Name), "nameが一致する");
                Assert.That(tenMst.KanaName1, Is.EqualTo(item.KanaName), "kana_nameが一致する");
                Assert.That(tenMst.KensaLabel, Is.EqualTo(0), "kensa_labelが一致する");
            }
        }
        finally
        {
            // テストデータを物理削除
            CleanupTestData(hpId, centerCd, startDate);
            repository.ReleaseResource();
        }
    }

    [Test]
    public void TC_003_UpdateTenMst_Success_WhenLogin()
    {
        // Arrange
        SetupTestEnvironment(out KensaCenterPartnershipRepository repository, out TenantDataContext tenant);

        int hpId = 1;
        string centerCd = "T003";
        int startDate = 20250709;
        int endDate = 99991231;

        // テストデータを準備
        PrepareTestData(tenant, centerCd, startDate, endDate);

        try
        {
            // Act
            var result = repository.RegisterKensaCenterPartnership(hpId, centerCd, startDate, endDate);
            var kensaCenterPartnership = tenant.KensaCenterPartnership.FirstOrDefault(t => t.HpId == hpId && t.CenterCd == centerCd && t.StartDate == startDate && t.IsDeleted == 0);
            kensaCenterPartnership.MasterUpdateDate = DateTime.UtcNow.AddDays(-1);
            tenant.SaveChanges();
            var tracking = tenant.CommonCenterItemMst.FirstOrDefault(t => t.CenterCd == centerCd && t.StartDate == startDate && t.KensaItemCd == $"K{centerCd}");
            tracking.Name = $"変更後{centerCd}";
            tracking.KanaName = $"テストケンサクモク変更後";
            tracking.UpdateDate = DateTime.UtcNow;
            tenant.SaveChanges();
            repository.UpdateKensaCenterPartnershipMstUpdateDate(hpId);

            // Assert
            Assert.IsNotNull(result, "正常に登録された場合はnullではない");

            // 新しいコンテキストで削除状態を確認
            using var verifyContext = TenantProvider.GetNoTrackingDataContext();
            var commonCenterItemMst = verifyContext.CommonCenterItemMst.Where(t => t.CenterCd == centerCd).ToList();
            Assert.IsTrue(commonCenterItemMst.Any(), "CommonCenterItemMstにデータが存在する");
            
            foreach (var item in commonCenterItemMst) {
                var tenMst = verifyContext.TenMsts.FirstOrDefault(t => t.HpId == hpId && t.CenterCd == item.CenterCd && t.ItemCd == item.ItemCd && t.StartDate == item.StartDate);
                Assert.IsNotNull(tenMst, "登録されたデータが存在する");
                
                // 指定された項目を比較
                if (item.SanteiItemCd == notSanteiItemCd) {
                    Assert.That(tenMst.KensaItemCd, Is.EqualTo("0"), "kensa_item_cdが一致する");     
                } else {
                    Assert.That(tenMst.KensaItemCd, Is.EqualTo(item.KensaItemCd), "kensa_item_cdが一致する");
                }
                Assert.That(tenMst.HpId, Is.EqualTo(hpId), "hp_idが一致する");
                Assert.That(tenMst.OdrUnitName, Is.EqualTo(""), "odr_unit_nameが一致する");
                Assert.That(tenMst.ItemCd, Is.EqualTo(item.ItemCd), "item_cdが一致する");
                Assert.That(tenMst.StartDate, Is.EqualTo(item.StartDate), "start_dateが一致する");
                Assert.That(tenMst.EndDate, Is.EqualTo(item.EndDate), "end_dateが一致する");
                Assert.That(tenMst.SanteiItemCd, Is.EqualTo(item.SanteiItemCd), "santei_item_cdが一致する");
                Assert.That(tenMst.CenterCd, Is.EqualTo(item.CenterCd), "center_cdが一致する");
                Assert.That(tenMst.IsAdopted, Is.EqualTo(0), "is_adoptedが一致する");
                Assert.That(tenMst.KensaLabel, Is.EqualTo(0), "kensa_labelが一致する");
                Assert.That(tenMst.Name, Is.EqualTo(item.Name), "nameが一致する");
                Assert.That(tenMst.KanaName1, Is.EqualTo(item.KanaName), "kana_nameが一致する");
                Assert.That(tenMst.KensaLabel, Is.EqualTo(0), "kensa_labelが一致する");
                Assert.That(tenMst.CreateDate, Is.Not.EqualTo(tenMst.UpdateDate), "create_dateとupdate_dateが一致しない");
            }
        }
        finally
        {
            // テストデータを物理削除
            CleanupTestData(hpId, centerCd, startDate);
            repository.ReleaseResource();
        }
    }

    private void SetupTestEnvironment(out KensaCenterPartnershipRepository repository, out TenantDataContext tenant)
    {
        repository = new KensaCenterPartnershipRepository(TenantProvider);
        tenant = TenantProvider.GetTrackingTenantDataContext();
    }

    private void PrepareTestData(TenantDataContext tenant, string centerCd, int startDate, int endDate)
    {
        // CommonCenterItemMstにテストデータを追加
        var commonCenterItemMst = new CommonCenterItemMst
        {
            CenterCd = centerCd,
            ItemCd = $"T{centerCd}", // 10文字制限内に修正
            KensaItemCd = $"K{centerCd}", // 20文字制限内
            StartDate = startDate,
            EndDate = endDate,
            Name = $"テスト検査項目{centerCd}",
            KanaName = $"テストケンサクモク{centerCd}",
            SanteiItemCd = null,
            UpdateDate = DateTime.UtcNow
        };
        
        tenant.CommonCenterItemMst.Add(commonCenterItemMst);
        tenant.SaveChanges();
    }

    private void CleanupTestData(int hpId, string centerCd, int startDate)
    {
        var verifyContext = TenantProvider.GetNoTrackingDataContext();
        
        // TenMstデータを削除
        var tenMst = verifyContext.TenMsts.Where(t => t.HpId == hpId && t.CenterCd == centerCd && t.StartDate == startDate).ToList();
        foreach (var item in tenMst) verifyContext.TenMsts.Remove(item);
        
        // KensaCenterPartnershipデータを削除
        var kensaCenterPartnership = verifyContext.KensaCenterPartnership.Where(t => t.HpId == hpId && t.CenterCd == centerCd && t.StartDate == startDate).ToList();
        foreach (var item in kensaCenterPartnership) verifyContext.KensaCenterPartnership.Remove(item);
        
        // CommonCenterItemMstデータを削除
        var commonCenterItemMst = verifyContext.CommonCenterItemMst.Where(t => t.CenterCd == centerCd).ToList();
        foreach (var item in commonCenterItemMst) verifyContext.CommonCenterItemMst.Remove(item);
        
        verifyContext.SaveChanges();
    }
}
