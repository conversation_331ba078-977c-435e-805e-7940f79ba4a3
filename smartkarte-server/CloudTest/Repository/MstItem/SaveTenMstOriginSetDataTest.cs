using Domain.CustomException;
using Domain.Models.MstItem;
using Domain.Models.OrdInf;
using Domain.Models.TodayOdr;
using Entity.Tenant;
using Helper.Enum;
using Infrastructure.Interfaces;
using Infrastructure.Options;
using Infrastructure.Repositories;
using Microsoft.Extensions.Options;
using Moq;
using PostgreDataContext;

namespace CloudUnitTest.Repository.MstItem;

public class SaveTenMstOriginSetDataTest : BaseUT
{
    [Test]
    public void Test_SaveTenMstOriginSetData_DuplicateEntryException()
    {
        // Arrange
        SetupTestEnvironment(out MstItemRepository mstItemRepository, out TenantDataContext tenant);

        int hpId = 1;
        string itemCd = "TEST001";
        int userId = 1;

        // 既存データを作成
        TenMst existingTenMst = new()
        {
            HpId = hpId,
            ItemCd = itemCd,
            StartDate = 20230101,
            EndDate = 99999999,
            SinKouiKbn = 1,
            TenId = 1,
            IsDeleted = 0,
            Name = "テスト項目",
            CreateDate = DateTime.UtcNow,
            CreateId = userId,
        };

        // テストパラメータ
        IEnumerable<CategoryItemEnums> tabActs = new List<CategoryItemEnums> { CategoryItemEnums.BasicSetting };
        List<TenMstOriginModel> tenMstOrigins = new();
        SetDataTenMstOriginModel setDataTen = CreateMockSetDataTenMstOriginModel();

        try
        {
            // データベースに既存データを追加
            tenant.TenMsts.Add(existingTenMst);
            tenant.SaveChanges();

            // Act & Assert
            Assert.Throws<DuplicateEntryException>(() =>
                mstItemRepository.SaveTenMstOriginSetData(tabActs, itemCd, tenMstOrigins, setDataTen, userId, hpId));
        }
        finally
        {
            // クリーンアップ
            mstItemRepository.ReleaseResource();
            tenant.TenMsts.Remove(existingTenMst);
            tenant.SaveChanges();
        }
    }

    [Test]
    public void Test_SaveTenMstOriginSetData_Success_IsDeleted()
    {
        // Arrange
        SetupTestEnvironment(out MstItemRepository mstItemRepository, out TenantDataContext tenant);

        int hpId = 1;
        string itemCd = "TEST002";
        int userId = 1;

        // 異なる条件のデータを作成（IsDeleted = 1で重複条件を満たさない）
        TenMst existingTenMst = new()
        {
            HpId = hpId,
            ItemCd = itemCd,
            StartDate = 20230101,
            EndDate = 99999999,
            SinKouiKbn = 1,
            TenId = 1,
            IsDeleted = 1, // DeleteTypes.Deleted - 重複条件を満たさない
            Name = "テスト項目",
            CreateDate = DateTime.UtcNow,
            CreateId = userId,
        };

        // テストパラメータ
        IEnumerable<CategoryItemEnums> tabActs = new List<CategoryItemEnums> { CategoryItemEnums.BasicSetting };
        List<TenMstOriginModel> tenMstOrigins = new();
        SetDataTenMstOriginModel setDataTen = CreateMockSetDataTenMstOriginModel();

        try
        {
            // データベースに既存データを追加
            tenant.TenMsts.Add(existingTenMst);
            tenant.SaveChanges();

            // Act & Assert - 例外がスローされないことを確認
            Assert.DoesNotThrow(() =>
                mstItemRepository.SaveTenMstOriginSetData(tabActs, itemCd, tenMstOrigins, setDataTen, userId, hpId));
        }
        finally
        {
            // クリーンアップ
            mstItemRepository.ReleaseResource();
            tenant.TenMsts.Remove(existingTenMst);
            tenant.SaveChanges();
        }
    }

    [Test]
    public void Test_SaveTenMstOriginSetData_Success_DifferentHpId()
    {
        // Arrange
        SetupTestEnvironment(out MstItemRepository mstItemRepository, out TenantDataContext tenant);

        int hpId = 1;
        int differentHpId = 2; // 異なるHpId
        string itemCd = "TEST003";
        int userId = 1;

        // 異なるHpIdのデータを作成
        TenMst existingTenMst = new()
        {
            HpId = differentHpId, // 異なるHpId
            ItemCd = itemCd,
            StartDate = 20230101,
            EndDate = 99999999,
            SinKouiKbn = 1,
            TenId = 1,
            IsDeleted = 0,
            Name = "テスト項目",
            CreateDate = DateTime.UtcNow,
            CreateId = userId,
        };

        // テストパラメータ
        IEnumerable<CategoryItemEnums> tabActs = new List<CategoryItemEnums> { CategoryItemEnums.BasicSetting };
        List<TenMstOriginModel> tenMstOrigins = new();
        SetDataTenMstOriginModel setDataTen = CreateMockSetDataTenMstOriginModel();

        try
        {
            // データベースに既存データを追加
            tenant.TenMsts.Add(existingTenMst);
            tenant.SaveChanges();

            // Act & Assert - 例外がスローされないことを確認
            Assert.DoesNotThrow(() =>
                mstItemRepository.SaveTenMstOriginSetData(tabActs, itemCd, tenMstOrigins, setDataTen, userId, hpId));
        }
        finally
        {
            // クリーンアップ
            mstItemRepository.ReleaseResource();
            tenant.TenMsts.Remove(existingTenMst);
            tenant.SaveChanges();
        }
    }

    [Test]
    public void Test_SaveTenMstOriginSetData_Success_DifferentItemCd()
    {
        // Arrange
        SetupTestEnvironment(out MstItemRepository mstItemRepository, out TenantDataContext tenant);

        int hpId = 1;
        string itemCd = "TEST004";
        string differentItemCd = "DIFF001"; // 異なるItemCd
        int userId = 1;

        // 異なるItemCdのデータを作成
        TenMst existingTenMst = new()
        {
            HpId = hpId,
            ItemCd = differentItemCd, // 異なるItemCd
            StartDate = 20230101,
            EndDate = 99999999,
            SinKouiKbn = 1,
            TenId = 1,
            IsDeleted = 0,
            Name = "テスト項目",
            CreateDate = DateTime.UtcNow,
            CreateId = userId,
        };

        // テストパラメータ
        IEnumerable<CategoryItemEnums> tabActs = new List<CategoryItemEnums> { CategoryItemEnums.BasicSetting };
        List<TenMstOriginModel> tenMstOrigins = new();
        SetDataTenMstOriginModel setDataTen = CreateMockSetDataTenMstOriginModel();

        try
        {
            // データベースに既存データを追加
            tenant.TenMsts.Add(existingTenMst);
            tenant.SaveChanges();

            // Act & Assert - 例外がスローされないことを確認
            Assert.DoesNotThrow(() =>
                mstItemRepository.SaveTenMstOriginSetData(tabActs, itemCd, tenMstOrigins, setDataTen, userId, hpId));
        }
        finally
        {
            // クリーンアップ
            mstItemRepository.ReleaseResource();
            tenant.TenMsts.Remove(existingTenMst);
            tenant.SaveChanges();
        }
    }

    private void SetupTestEnvironment(out MstItemRepository mstItemRepository, out TenantDataContext tenant)
    {
        // AmazonS3Optionsのモックを作成
        var mockS3Options = new Mock<IOptions<AmazonS3Options>>();
        mockS3Options.Setup(x => x.Value).Returns(new AmazonS3Options());

        // 複数のITenantProviderのモックを作成
        var mockTenantProvider1 = new Mock<ITenantProvider>();
        var mockTenantProvider2 = new Mock<ITenantProvider>();
        var mockTenantProvider3 = new Mock<ITenantProvider>();
        var mockTenantProvider4 = new Mock<ITenantProvider>();
        var mockTenantProvider5 = new Mock<ITenantProvider>();
        var mockTenantProvider6 = new Mock<ITenantProvider>();

        mstItemRepository = new MstItemRepository(
            TenantProvider,
            mockS3Options.Object,
            mockTenantProvider1.Object,
            mockTenantProvider2.Object,
            mockTenantProvider3.Object,
            mockTenantProvider4.Object,
            mockTenantProvider5.Object,
            mockTenantProvider6.Object
        );

        tenant = TenantProvider.GetTrackingTenantDataContext();
    }

    private SetDataTenMstOriginModel CreateMockSetDataTenMstOriginModel()
    {
        var basicSettingTab = new BasicSettingTabModel(new List<CmtKbnMstModel>());
        var ijiSettingTab = new IjiSettingTabModel();
        var precriptionSettingTab = new PrecriptionSettingTabModel(
            new List<M10DayLimitModel>(),
            new List<IpnMinYakkaMstModel>(),
            new List<DrugDayLimitModel>(),
            new DosageMstModel(),
            new IpnNameMstModel());
        var usageSettingTab = new UsageSettingTabModel();
        var drugInfomationTab = new DrugInfomationTabModel(
            new List<DrugInfModel>(),
            new PiImageModel(),
            new PiImageModel());
        var teikyoByomeiTab = new TeikyoByomeiTabModel(
            new List<TeikyoByomeiModel>(),
            new TekiouByomeiMstExcludedModel());
        var santeiKaishuTab = new SanteiKaishuTabModel(new List<DensiSanteiKaisuModel>());
        var haihanTab = new HaihanTabModel(
            new List<DensiHaihanModel>(),
            new List<DensiHaihanModel>(),
            new List<DensiHaihanModel>());
        var houkatsuTab = new HoukatsuTabModel(
            new List<DensiHoukatuModel>(),
            new List<DensiHoukatuGrpModel>(),
            new List<DensiHoukatuModel>());
        var combinedContraindicationTab = new CombinedContraindicationTabModel(new List<CombinedContraindicationModel>());

        return new SetDataTenMstOriginModel(
            basicSettingTab,
            ijiSettingTab,
            precriptionSettingTab,
            usageSettingTab,
            drugInfomationTab,
            teikyoByomeiTab,
            santeiKaishuTab,
            haihanTab,
            houkatsuTab,
            combinedContraindicationTab);
    }
}
