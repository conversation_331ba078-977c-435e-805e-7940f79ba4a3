﻿using Infrastructure.Repositories;
using Microsoft.Extensions.Configuration;
using Moq;
using PostgreDataContext;
using Domain.Models.FlowSheet;

namespace CloudUnitTest.Repository.FlowSheet;

public class SaveHolidayMstTest : BaseUT
{
    [Test]
    public void Test_SaveHolidayMst_holidayModels_null()
    {
        // Arrange
        SetupTestEnvironment(out FlowSheetRepository flowSheetRepository, out TenantNoTrackingDataContext tenant);

        try
        {
            // Act
            var result = flowSheetRepository.SaveHolidayMst(null, 1, true);

            // Assert
            Assert.IsFalse(result);
        }
        finally
        {
            flowSheetRepository.ReleaseResource();
        }
    }

    // holidayKbn 休日区分  0:平日 1:休日 2:日曜もしくは年末年始
    // kyusinKbn  休診区分  0:診療日 1:休診日
    [TestCase(1, 0, new[] { 20250104 }, 1, 1, "院長の誕生日", 1, false, new[] { 1 }, new[] { 1 }, new[] { "院長の誕生日" })]                                                        // 繰り返し✕ 1:休日 1:休診日
    [TestCase(1, 0, new[] { 20250104 }, 0, 0, "", 1, false, new[] { 0 }, new[] { 0 }, new[] { "" })]                                                                            // 繰り返し✕ 0:平日 0:診療日
    [TestCase(1, 0, new[] { 20250101, 20250102, 20250103, 20250106 }, 0, 0, "", 1, true, new[] { 1, 2, 2, 0 }, new[] { 0, 0, 0, 0 }, new[] { "元日", "休診日", "休診日", "" })]    // 繰り返し◯ 0:平日 0:診療日
    [TestCase(1, 0, new[] { 20250101, 20250102, 20250103, 20250106 }, 1, 1, "", 1, true, new[] { 1, 2, 2, 0 }, new[] { 1, 1, 1, 1 }, new[] { "元日", "休診日", "休診日", "" })]    // 繰り返し◯ 1:休日 1:休診日
    public void Test_SaveHolidayMst_OK(int hpId, long seqNo, IEnumerable<int> holidays, int holidayKbn, int kyusinKbn, string holidayName, int userId, bool hasRecurrence,
        int[] expectedHolidayKbn, int[] expectedKyusinKbn, string[] expectedHolidayName)
    {
        // Arrange
        SetupTestEnvironment(out FlowSheetRepository flowSheetRepository, out TenantNoTrackingDataContext tenant);

        try
        {
            // Act
            var holidayModels = holidays.Select(holiday => new HolidayModel(hpId, seqNo, holiday, holidayKbn, kyusinKbn, holidayName))
                .ToList();
            var result = flowSheetRepository.SaveHolidayMst(holidayModels, userId, hasRecurrence);

            // Determine start and end dates
            var holidayArray = holidays.ToArray();
            var startDate = holidayArray.First();
            var endDate = holidayArray.Last();
            if (holidayArray.Length == 1)
                endDate = startDate;
            var holidayList = flowSheetRepository.GetHolidayMst(hpId, startDate, endDate)
                .Where(h => holidays.Contains(h.SinDate))
                .OrderBy(h => h.SinDate)
                .ToList();

            // Assert
            Assert.IsTrue(result);
            for (int i = 0; i < holidayList.Count; i++)
            {
                Assert.That(holidayList[i].HolidayKbn, Is.EqualTo(expectedHolidayKbn[i]), $"Mismatch in HolidayKbn at index {i}");
                Assert.That(holidayList[i].KyusinKbn, Is.EqualTo(expectedKyusinKbn[i]), $"Mismatch in KyusinKbn at index {i}");
                Assert.That(holidayList[i].HolidayName, Is.EqualTo(expectedHolidayName[i]), $"Mismatch in HolidayName at index {i}");
            }
        }
        finally
        {
            flowSheetRepository.ReleaseResource();
        }
    }

    private void SetupTestEnvironment(out FlowSheetRepository flowSheetRepository, out TenantNoTrackingDataContext tenant)
    {
        var mockIConfiguration = new Mock<IConfiguration>();
        mockIConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisHost")]).Returns("localhost");
        mockIConfiguration.SetupGet(x => x[It.Is<string>(s => s == "Redis:RedisPort")]).Returns("6379");

        flowSheetRepository = new FlowSheetRepository(TenantProvider, TenantProvider, TenantProvider, TenantProvider, TenantProvider, TenantProvider, TenantProvider, mockIConfiguration.Object);
        tenant = TenantProvider.GetNoTrackingDataContext();
    }
}
