{"version": 2, "dgSpecHash": "mwDTiYVsbCk=", "success": true, "projectFilePath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/CloudTest/CloudUnitTest.csproj", "expectedPackageFiles": ["/Users/<USER>/.nuget/packages/awssdk.cloudfront/3.7.300/awssdk.cloudfront.3.7.300.nupkg.sha512", "/Users/<USER>/.nuget/packages/awssdk.cloudwatch/3.7.300/awssdk.cloudwatch.3.7.300.nupkg.sha512", "/Users/<USER>/.nuget/packages/awssdk.core/3.7.300.6/awssdk.core.3.7.300.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/awssdk.rds/3.7.300.1/awssdk.rds.3.7.300.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/awssdk.route53/3.7.300/awssdk.route53.3.7.300.nupkg.sha512", "/Users/<USER>/.nuget/packages/awssdk.s3/3.7.301.1/awssdk.s3.3.7.301.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/castle.core/5.1.1/castle.core.5.1.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/castle.windsor/6.0.0/castle.windsor.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/coverlet.collector/3.1.2/coverlet.collector.3.1.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/csvhelper/30.0.1/csvhelper.30.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/documentformat.openxml/2.19.0/documentformat.openxml.2.19.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/graphql.client/6.0.0/graphql.client.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/graphql.client.abstractions/6.0.0/graphql.client.abstractions.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/graphql.client.abstractions.websocket/6.0.0/graphql.client.abstractions.websocket.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/graphql.client.serializer.newtonsoft/6.0.0/graphql.client.serializer.newtonsoft.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/graphql.primitives/6.0.0/graphql.primitives.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/kana.net/1.0.6/kana.net.1.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/konscious.security.cryptography.argon2/1.3.0/konscious.security.cryptography.argon2.1.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/konscious.security.cryptography.blake2/1.1.0/konscious.security.cryptography.blake2.1.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.authentication.abstractions/2.2.0/microsoft.aspnetcore.authentication.abstractions.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.authentication.core/2.2.0/microsoft.aspnetcore.authentication.core.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.authorization/2.2.0/microsoft.aspnetcore.authorization.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.authorization.policy/2.2.0/microsoft.aspnetcore.authorization.policy.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.connections.abstractions/2.2.0/microsoft.aspnetcore.connections.abstractions.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.hosting.abstractions/2.2.0/microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.hosting.server.abstractions/2.2.0/microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.http/2.2.0/microsoft.aspnetcore.http.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.http.abstractions/2.2.0/microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.http.connections/1.1.0/microsoft.aspnetcore.http.connections.1.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.http.connections.common/1.1.0/microsoft.aspnetcore.http.connections.common.1.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.http.extensions/2.2.0/microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.http.features/2.2.0/microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.mvc.abstractions/2.2.0/microsoft.aspnetcore.mvc.abstractions.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.mvc.core/2.2.5/microsoft.aspnetcore.mvc.core.2.2.5.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.responsecaching.abstractions/2.2.0/microsoft.aspnetcore.responsecaching.abstractions.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.routing/2.2.0/microsoft.aspnetcore.routing.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.routing.abstractions/2.2.0/microsoft.aspnetcore.routing.abstractions.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.signalr/1.1.0/microsoft.aspnetcore.signalr.1.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.signalr.common/1.1.0/microsoft.aspnetcore.signalr.common.1.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.signalr.core/1.1.0/microsoft.aspnetcore.signalr.core.1.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.signalr.protocols.json/1.1.0/microsoft.aspnetcore.signalr.protocols.json.1.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.websockets/2.2.0/microsoft.aspnetcore.websockets.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.webutilities/2.2.0/microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.codecoverage/17.1.0/microsoft.codecoverage.17.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.csharp/4.5.0/microsoft.csharp.4.5.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.diagnostics.netcore.client/0.2.510501/microsoft.diagnostics.netcore.client.0.2.510501.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore/7.0.1/microsoft.entityframeworkcore.7.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.abstractions/7.0.1/microsoft.entityframeworkcore.abstractions.7.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.analyzers/7.0.1/microsoft.entityframeworkcore.analyzers.7.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.dynamiclinq/6.2.20/microsoft.entityframeworkcore.dynamiclinq.6.2.20.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.relational/7.0.0/microsoft.entityframeworkcore.relational.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.caching.abstractions/7.0.0/microsoft.extensions.caching.abstractions.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.caching.memory/7.0.0/microsoft.extensions.caching.memory.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration/7.0.0/microsoft.extensions.configuration.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.abstractions/7.0.0/microsoft.extensions.configuration.abstractions.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.fileextensions/7.0.0/microsoft.extensions.configuration.fileextensions.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.json/7.0.0/microsoft.extensions.configuration.json.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection/8.0.0/microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection.abstractions/8.0.0/microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencymodel/6.0.0/microsoft.extensions.dependencymodel.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.fileproviders.abstractions/7.0.0/microsoft.extensions.fileproviders.abstractions.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.fileproviders.physical/7.0.0/microsoft.extensions.fileproviders.physical.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.filesystemglobbing/7.0.0/microsoft.extensions.filesystemglobbing.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.hosting.abstractions/2.2.0/microsoft.extensions.hosting.abstractions.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging/8.0.0/microsoft.extensions.logging.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.abstractions/8.0.0/microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.objectpool/9.0.0/microsoft.extensions.objectpool.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.options/8.0.0/microsoft.extensions.options.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.primitives/8.0.0/microsoft.extensions.primitives.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identitymodel.abstractions/6.34.0/microsoft.identitymodel.abstractions.6.34.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identitymodel.jsonwebtokens/6.34.0/microsoft.identitymodel.jsonwebtokens.6.34.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identitymodel.logging/6.34.0/microsoft.identitymodel.logging.6.34.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identitymodel.tokens/6.34.0/microsoft.identitymodel.tokens.6.34.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.net.http.headers/2.2.0/microsoft.net.http.headers.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.net.test.sdk/17.1.0/microsoft.net.test.sdk.17.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.platforms/3.1.0/microsoft.netcore.platforms.3.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.targets/1.1.0/microsoft.netcore.targets.1.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.testplatform.objectmodel/17.1.0/microsoft.testplatform.objectmodel.17.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.testplatform.testhost/17.1.0/microsoft.testplatform.testhost.17.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.win32.registry/4.7.0/microsoft.win32.registry.4.7.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.win32.systemevents/6.0.0/microsoft.win32.systemevents.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/moq/4.18.4/moq.4.18.4.nupkg.sha512", "/Users/<USER>/.nuget/packages/mynihongo.kanaconverter/1.0.3/mynihongo.kanaconverter.1.0.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/netstandard.library/2.0.0/netstandard.library.2.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/newtonsoft.json/13.0.2/newtonsoft.json.13.0.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/npgsql/7.0.7/npgsql.7.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/npgsql.entityframeworkcore.postgresql/7.0.0/npgsql.entityframeworkcore.postgresql.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/nuget.frameworks/5.11.0/nuget.frameworks.5.11.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/nunit/3.13.3/nunit.3.13.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/nunit.analyzers/3.3.0/nunit.analyzers.3.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/nunit3testadapter/4.2.1/nunit3testadapter.4.2.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/pipelines.sockets.unofficial/2.2.2/pipelines.sockets.unofficial.2.2.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.native.system.data.sqlclient.sni/4.7.0/runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0/runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0/runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0/runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/sentry/4.13.0/sentry.4.13.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/sentry.profiling/4.13.0/sentry.profiling.4.13.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/sharpcompress/0.34.2/sharpcompress.0.34.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/spire.pdf/11.3.0/spire.pdf.11.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/stackexchange.redis/2.6.111/stackexchange.redis.2.6.111.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.buffers/4.5.1/system.buffers.4.5.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.data.sqlclient/4.8.6/system.data.sqlclient.4.8.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.diagnosticsource/4.5.0/system.diagnostics.diagnosticsource.4.5.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.eventlog/6.0.0/system.diagnostics.eventlog.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.drawing.common/6.0.0/system.drawing.common.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.identitymodel.tokens.jwt/6.34.0/system.identitymodel.tokens.jwt.6.34.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io/4.3.0/system.io.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.packaging/4.7.0/system.io.packaging.4.7.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.pipelines/5.0.1/system.io.pipelines.5.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.linq.dynamic.core/1.2.20/system.linq.dynamic.core.1.2.20.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.memory/4.5.4/system.memory.4.5.4.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.net.websockets.websocketprotocol/4.5.1/system.net.websockets.websocketprotocol.4.5.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reactive/5.0.0/system.reactive.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection/4.3.0/system.reflection.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.emit/4.3.0/system.reflection.emit.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.emit.ilgeneration/4.3.0/system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.metadata/1.6.0/system.reflection.metadata.1.6.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.primitives/4.3.0/system.reflection.primitives.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime/4.3.0/system.runtime.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.compilerservices.unsafe/6.0.0/system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.accesscontrol/4.7.0/system.security.accesscontrol.4.7.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.cng/4.5.0/system.security.cryptography.cng.4.5.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.principal.windows/4.7.0/system.security.principal.windows.4.7.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.encoding/4.3.0/system.text.encoding.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.encoding.codepages/7.0.0/system.text.encoding.codepages.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.encodings.web/7.0.0/system.text.encodings.web.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.json/7.0.0/system.text.json.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.channels/4.5.0/system.threading.channels.4.5.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.tasks/4.3.0/system.threading.tasks.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.tasks.extensions/4.5.1/system.threading.tasks.extensions.4.5.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/zstdsharp.port/0.7.2/zstdsharp.port.0.7.2.nupkg.sha512"], "logs": []}