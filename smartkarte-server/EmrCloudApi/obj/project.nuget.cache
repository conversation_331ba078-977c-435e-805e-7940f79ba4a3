{"version": 2, "dgSpecHash": "JJPdXUmWoeI=", "success": true, "projectFilePath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EmrCloudApi/EmrCloudApi.csproj", "expectedPackageFiles": ["/Users/<USER>/.nuget/packages/awssdk.cloudfront/3.7.300/awssdk.cloudfront.3.7.300.nupkg.sha512", "/Users/<USER>/.nuget/packages/awssdk.cloudwatch/3.7.300/awssdk.cloudwatch.3.7.300.nupkg.sha512", "/Users/<USER>/.nuget/packages/awssdk.core/3.7.300.6/awssdk.core.3.7.300.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/awssdk.rds/3.7.300.1/awssdk.rds.3.7.300.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/awssdk.route53/3.7.300/awssdk.route53.3.7.300.nupkg.sha512", "/Users/<USER>/.nuget/packages/awssdk.s3/3.7.301.1/awssdk.s3.3.7.301.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/castle.core/5.1.0/castle.core.5.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/castle.windsor/6.0.0/castle.windsor.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/closedxml/0.102.0/closedxml.0.102.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/csvhelper/30.0.1/csvhelper.30.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/documentformat.openxml/2.18.0/documentformat.openxml.2.18.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/excelnumberformat/1.1.0/excelnumberformat.1.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/fakeiteasy/7.3.1/fakeiteasy.7.3.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/graphql.client/6.0.0/graphql.client.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/graphql.client.abstractions/6.0.0/graphql.client.abstractions.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/graphql.client.abstractions.websocket/6.0.0/graphql.client.abstractions.websocket.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/graphql.client.serializer.newtonsoft/6.0.0/graphql.client.serializer.newtonsoft.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/graphql.primitives/6.0.0/graphql.primitives.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/humanizer.core/2.14.1/humanizer.core.2.14.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/irony.netcore/1.0.11/irony.netcore.1.0.11.nupkg.sha512", "/Users/<USER>/.nuget/packages/itext7/8.0.0/itext7.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/itext7.bouncy-castle-adapter/8.0.0/itext7.bouncy-castle-adapter.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/itext7.commons/8.0.0/itext7.commons.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/kana.net/1.0.6/kana.net.1.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/konscious.security.cryptography.argon2/1.3.0/konscious.security.cryptography.argon2.1.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/konscious.security.cryptography.blake2/1.1.0/konscious.security.cryptography.blake2.1.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/messagepack/2.1.90/messagepack.2.1.90.nupkg.sha512", "/Users/<USER>/.nuget/packages/messagepack.annotations/2.1.90/messagepack.annotations.2.1.90.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.authentication.abstractions/2.2.0/microsoft.aspnetcore.authentication.abstractions.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.authentication.core/2.2.0/microsoft.aspnetcore.authentication.core.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.authentication.jwtbearer/6.0.8/microsoft.aspnetcore.authentication.jwtbearer.6.0.8.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.authorization/2.2.0/microsoft.aspnetcore.authorization.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.authorization.policy/2.2.0/microsoft.aspnetcore.authorization.policy.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.connections.abstractions/7.0.11/microsoft.aspnetcore.connections.abstractions.7.0.11.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.hosting.abstractions/2.2.0/microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.hosting.server.abstractions/2.2.0/microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.http/2.2.0/microsoft.aspnetcore.http.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.http.abstractions/2.2.0/microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.http.connections/1.1.0/microsoft.aspnetcore.http.connections.1.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.http.connections.client/7.0.9/microsoft.aspnetcore.http.connections.client.7.0.9.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.http.connections.common/7.0.9/microsoft.aspnetcore.http.connections.common.7.0.9.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.http.extensions/2.2.0/microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.http.features/2.2.0/microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.mvc.abstractions/2.2.0/microsoft.aspnetcore.mvc.abstractions.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.mvc.core/2.2.5/microsoft.aspnetcore.mvc.core.2.2.5.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.responsecaching.abstractions/2.2.0/microsoft.aspnetcore.responsecaching.abstractions.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.routing/2.2.0/microsoft.aspnetcore.routing.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.routing.abstractions/2.2.0/microsoft.aspnetcore.routing.abstractions.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.signalr/1.1.0/microsoft.aspnetcore.signalr.1.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.signalr.client/7.0.9/microsoft.aspnetcore.signalr.client.7.0.9.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.signalr.client.core/7.0.9/microsoft.aspnetcore.signalr.client.core.7.0.9.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.signalr.common/7.0.11/microsoft.aspnetcore.signalr.common.7.0.11.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.signalr.core/1.1.0/microsoft.aspnetcore.signalr.core.1.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.signalr.protocols.json/7.0.9/microsoft.aspnetcore.signalr.protocols.json.7.0.9.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.signalr.protocols.messagepack/7.0.11/microsoft.aspnetcore.signalr.protocols.messagepack.7.0.11.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.signalr.stackexchangeredis/6.0.26/microsoft.aspnetcore.signalr.stackexchangeredis.6.0.26.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.websockets/2.2.0/microsoft.aspnetcore.websockets.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.webutilities/2.2.0/microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.bcl.asyncinterfaces/1.0.0/microsoft.bcl.asyncinterfaces.1.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.csharp/4.5.0/microsoft.csharp.4.5.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.diagnostics.netcore.client/0.2.510501/microsoft.diagnostics.netcore.client.0.2.510501.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.dotnet.platformabstractions/1.1.0/microsoft.dotnet.platformabstractions.1.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore/7.0.1/microsoft.entityframeworkcore.7.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.abstractions/7.0.1/microsoft.entityframeworkcore.abstractions.7.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.analyzers/7.0.1/microsoft.entityframeworkcore.analyzers.7.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.design/7.0.1/microsoft.entityframeworkcore.design.7.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.dynamiclinq/6.2.20/microsoft.entityframeworkcore.dynamiclinq.6.2.20.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.relational/7.0.1/microsoft.entityframeworkcore.relational.7.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.apidescription.server/3.0.0/microsoft.extensions.apidescription.server.3.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.caching.abstractions/7.0.0/microsoft.extensions.caching.abstractions.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.caching.memory/7.0.0/microsoft.extensions.caching.memory.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration/7.0.0/microsoft.extensions.configuration.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.abstractions/7.0.0/microsoft.extensions.configuration.abstractions.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.binder/2.0.0/microsoft.extensions.configuration.binder.2.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.fileextensions/7.0.0/microsoft.extensions.configuration.fileextensions.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.json/7.0.0/microsoft.extensions.configuration.json.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection/8.0.0/microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection.abstractions/8.0.0/microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencymodel/7.0.0/microsoft.extensions.dependencymodel.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.features/7.0.11/microsoft.extensions.features.7.0.11.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.fileproviders.abstractions/7.0.0/microsoft.extensions.fileproviders.abstractions.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.fileproviders.physical/7.0.0/microsoft.extensions.fileproviders.physical.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.filesystemglobbing/7.0.0/microsoft.extensions.filesystemglobbing.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.hosting.abstractions/3.1.8/microsoft.extensions.hosting.abstractions.3.1.8.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging/8.0.0/microsoft.extensions.logging.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.abstractions/8.0.0/microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.objectpool/9.0.0/microsoft.extensions.objectpool.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.options/8.0.0/microsoft.extensions.options.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.options.configurationextensions/2.0.0/microsoft.extensions.options.configurationextensions.2.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.primitives/8.0.0/microsoft.extensions.primitives.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identitymodel.abstractions/6.34.0/microsoft.identitymodel.abstractions.6.34.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identitymodel.jsonwebtokens/6.34.0/microsoft.identitymodel.jsonwebtokens.6.34.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identitymodel.logging/6.34.0/microsoft.identitymodel.logging.6.34.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identitymodel.protocols/6.10.0/microsoft.identitymodel.protocols.6.10.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identitymodel.protocols.openidconnect/6.10.0/microsoft.identitymodel.protocols.openidconnect.6.10.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.identitymodel.tokens/6.34.0/microsoft.identitymodel.tokens.6.34.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.net.http.headers/2.2.0/microsoft.net.http.headers.2.2.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.platforms/3.1.0/microsoft.netcore.platforms.3.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.targets/1.1.3/microsoft.netcore.targets.1.1.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.openapi/1.2.3/microsoft.openapi.1.2.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.win32.primitives/4.3.0/microsoft.win32.primitives.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.win32.registry/4.7.0/microsoft.win32.registry.4.7.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.win32.systemevents/6.0.0/microsoft.win32.systemevents.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/mono.texttemplating/2.2.1/mono.texttemplating.2.2.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/mynihongo.kanaconverter/1.0.3/mynihongo.kanaconverter.1.0.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/ncrontab.signed/3.3.3/ncrontab.signed.3.3.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/netstandard.library/1.6.1/netstandard.library.1.6.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/newtonsoft.json/13.0.2/newtonsoft.json.13.0.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/npgsql/7.0.7/npgsql.7.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/npgsql.entityframeworkcore.postgresql/7.0.0/npgsql.entityframeworkcore.postgresql.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/pipelines.sockets.unofficial/2.2.2/pipelines.sockets.unofficial.2.2.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/pomelo.entityframeworkcore.lolita/1.1.0/pomelo.entityframeworkcore.lolita.1.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/pomelo.entityframeworkcore.lolita.postgresql/1.1.0/pomelo.entityframeworkcore.lolita.postgresql.1.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/portable.bouncycastle/1.8.10/portable.bouncycastle.1.8.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.native.system/4.3.0/runtime.native.system.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.native.system.data.sqlclient.sni/4.7.0/runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.native.system.io.compression/4.3.0/runtime.native.system.io.compression.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.native.system.net.http/4.3.0/runtime.native.system.net.http.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.native.system.security.cryptography.apple/4.3.0/runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.native.system.security.cryptography.openssl/4.3.2/runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0/runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0/runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0/runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0/runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/sentry/4.13.0/sentry.4.13.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/sentry.profiling/4.13.0/sentry.profiling.4.13.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/serilog/2.11.0/serilog.2.11.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/serilog.aspnetcore/6.0.1/serilog.aspnetcore.6.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/serilog.extensions.hosting/5.0.1/serilog.extensions.hosting.5.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/serilog.extensions.logging/3.1.0/serilog.extensions.logging.3.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/serilog.formatting.compact/1.1.0/serilog.formatting.compact.1.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/serilog.settings.configuration/3.3.0/serilog.settings.configuration.3.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/serilog.sinks.console/4.0.1/serilog.sinks.console.4.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/serilog.sinks.debug/2.0.0/serilog.sinks.debug.2.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/serilog.sinks.file/5.0.0/serilog.sinks.file.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/sharpcompress/0.34.2/sharpcompress.0.34.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/sixlabors.fonts/1.0.0-beta19/sixlabors.fonts.1.0.0-beta19.nupkg.sha512", "/Users/<USER>/.nuget/packages/sixlabors.imagesharp/3.1.6/sixlabors.imagesharp.3.1.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/spire.pdf/11.3.0/spire.pdf.11.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/stackexchange.redis/2.6.111/stackexchange.redis.2.6.111.nupkg.sha512", "/Users/<USER>/.nuget/packages/swashbuckle.aspnetcore/6.2.3/swashbuckle.aspnetcore.6.2.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/swashbuckle.aspnetcore.swagger/6.2.3/swashbuckle.aspnetcore.swagger.6.2.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/swashbuckle.aspnetcore.swaggergen/6.2.3/swashbuckle.aspnetcore.swaggergen.6.2.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/swashbuckle.aspnetcore.swaggerui/6.2.3/swashbuckle.aspnetcore.swaggerui.6.2.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.appcontext/4.3.0/system.appcontext.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.buffers/4.5.0/system.buffers.4.5.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.codedom/4.4.0/system.codedom.4.4.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.collections/4.3.0/system.collections.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.collections.concurrent/4.3.0/system.collections.concurrent.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.collections.nongeneric/4.3.0/system.collections.nongeneric.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.console/4.3.0/system.console.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.data.sqlclient/4.8.6/system.data.sqlclient.4.8.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.debug/4.3.0/system.diagnostics.debug.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.diagnosticsource/4.5.0/system.diagnostics.diagnosticsource.4.5.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.eventlog/6.0.0/system.diagnostics.eventlog.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.process/4.3.0/system.diagnostics.process.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.tools/4.3.0/system.diagnostics.tools.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.tracing/4.3.0/system.diagnostics.tracing.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.drawing.common/6.0.0/system.drawing.common.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.globalization/4.3.0/system.globalization.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.globalization.calendars/4.3.0/system.globalization.calendars.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.globalization.extensions/4.3.0/system.globalization.extensions.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.identitymodel.tokens.jwt/6.34.0/system.identitymodel.tokens.jwt.6.34.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io/4.3.0/system.io.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.compression/4.3.0/system.io.compression.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.compression.zipfile/4.3.0/system.io.compression.zipfile.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.filesystem/4.3.0/system.io.filesystem.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.filesystem.primitives/4.3.0/system.io.filesystem.primitives.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.packaging/6.0.0/system.io.packaging.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.pipelines/7.0.0/system.io.pipelines.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.linq/4.3.0/system.linq.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.linq.dynamic.core/1.2.20/system.linq.dynamic.core.1.2.20.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.linq.expressions/4.3.0/system.linq.expressions.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.memory/4.5.4/system.memory.4.5.4.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.net.http/4.3.4/system.net.http.4.3.4.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.net.primitives/4.3.0/system.net.primitives.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.net.sockets/4.3.0/system.net.sockets.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.net.websockets.websocketprotocol/4.5.1/system.net.websockets.websocketprotocol.4.5.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.objectmodel/4.3.0/system.objectmodel.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reactive/5.0.0/system.reactive.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection/4.3.0/system.reflection.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.emit/4.6.0/system.reflection.emit.4.6.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.emit.ilgeneration/4.3.0/system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.emit.lightweight/4.6.0/system.reflection.emit.lightweight.4.6.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.extensions/4.3.0/system.reflection.extensions.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.primitives/4.3.0/system.reflection.primitives.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.typeextensions/4.3.0/system.reflection.typeextensions.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.resources.resourcemanager/4.3.0/system.resources.resourcemanager.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime/4.3.1/system.runtime.4.3.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.compilerservices.unsafe/6.0.0/system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.extensions/4.3.0/system.runtime.extensions.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.handles/4.3.0/system.runtime.handles.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.interopservices/4.3.0/system.runtime.interopservices.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.interopservices.runtimeinformation/4.3.0/system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.loader/4.3.0/system.runtime.loader.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.numerics/4.3.0/system.runtime.numerics.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.serialization.formatters/4.3.0/system.runtime.serialization.formatters.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.serialization.primitives/4.3.0/system.runtime.serialization.primitives.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.accesscontrol/4.7.0/system.security.accesscontrol.4.7.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.algorithms/4.3.0/system.security.cryptography.algorithms.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.cng/4.5.0/system.security.cryptography.cng.4.5.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.csp/4.3.0/system.security.cryptography.csp.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.encoding/4.3.0/system.security.cryptography.encoding.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.openssl/4.3.0/system.security.cryptography.openssl.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.primitives/4.3.0/system.security.cryptography.primitives.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.x509certificates/4.3.0/system.security.cryptography.x509certificates.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.principal.windows/4.7.0/system.security.principal.windows.4.7.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.encoding/4.3.0/system.text.encoding.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.encoding.codepages/7.0.0/system.text.encoding.codepages.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.encoding.extensions/4.3.0/system.text.encoding.extensions.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.encodings.web/7.0.0/system.text.encodings.web.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.json/7.0.3/system.text.json.7.0.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.regularexpressions/4.3.1/system.text.regularexpressions.4.3.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading/4.3.0/system.threading.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.channels/7.0.0/system.threading.channels.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.tasks/4.3.0/system.threading.tasks.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.tasks.extensions/4.5.3/system.threading.tasks.extensions.4.5.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.thread/4.3.0/system.threading.thread.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.threadpool/4.3.0/system.threading.threadpool.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.timer/4.3.0/system.threading.timer.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.xml.readerwriter/4.3.0/system.xml.readerwriter.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.xml.xdocument/4.3.0/system.xml.xdocument.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.xml.xmldocument/4.3.0/system.xml.xmldocument.4.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/xlparser/1.5.2/xlparser.1.5.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/zstdsharp.port/0.7.2/zstdsharp.port.0.7.2.nupkg.sha512"], "logs": [{"code": "NU1903", "level": "Warning", "warningLevel": 1, "message": "Package 'SixLabors.ImageSharp' 3.1.6 has a known high severity vulnerability, https://github.com/advisories/GHSA-2cmq-823j-5qj8", "libraryId": "SixLabors.ImageSharp", "targetGraphs": ["net6.0"]}]}