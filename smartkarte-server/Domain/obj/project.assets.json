{"version": 3, "targets": {"net6.0": {"AWSSDK.Core/3.7.12.24": {"type": "package", "compile": {"lib/netcoreapp3.1/AWSSDK.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/AWSSDK.Core.dll": {"related": ".pdb;.xml"}}}, "AWSSDK.S3/3.7.9.42": {"type": "package", "dependencies": {"AWSSDK.Core": "[3.7.12.24, 4.0.0)"}, "compile": {"lib/netcoreapp3.1/AWSSDK.S3.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/AWSSDK.S3.dll": {"related": ".pdb;.xml"}}}, "Kana.NET/1.0.6": {"type": "package", "compile": {"lib/net6.0/Umayadia.Kana.dll": {}}, "runtime": {"lib/net6.0/Umayadia.Kana.dll": {}}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.6": {"type": "package", "compile": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.ObjectPool/9.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Primitives/7.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "MyNihongo.KanaConverter/1.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.ObjectPool": "9.0.0"}, "compile": {"lib/netstandard2.0/MyNihongo.KanaConverter.dll": {}}, "runtime": {"lib/netstandard2.0/MyNihongo.KanaConverter.dll": {}}}, "Newtonsoft.Json/13.0.2": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Pipelines.Sockets.Unofficial/2.2.2": {"type": "package", "dependencies": {"System.IO.Pipelines": "5.0.1"}, "compile": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"related": ".xml"}}}, "StackExchange.Redis/2.6.111": {"type": "package", "dependencies": {"Pipelines.Sockets.Unofficial": "2.2.2"}, "compile": {"lib/net5.0/StackExchange.Redis.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/StackExchange.Redis.dll": {"related": ".xml"}}}, "System.IO.Pipelines/5.0.1": {"type": "package", "compile": {"ref/netcoreapp2.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/System.IO.Pipelines.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.dll": {"assetType": "runtime", "rid": "win"}}}, "Entity/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"Helper": "1.0.0", "Microsoft.EntityFrameworkCore.Abstractions": "6.0.6"}, "compile": {"bin/placeholder/Entity.dll": {}}, "runtime": {"bin/placeholder/Entity.dll": {}}}, "ErrorCodeGenerator/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"Newtonsoft.Json": "13.0.2"}, "compile": {"bin/placeholder/ErrorCodeGenerator.dll": {}}, "runtime": {"bin/placeholder/ErrorCodeGenerator.dll": {}}}, "Helper/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"ErrorCodeGenerator": "1.0.0", "Kana.NET": "1.0.6", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "MyNihongo.KanaConverter": "1.0.3", "StackExchange.Redis": "2.6.111", "System.Text.Encoding.CodePages": "6.0.0"}, "compile": {"bin/placeholder/Helper.dll": {}}, "runtime": {"bin/placeholder/Helper.dll": {}}}}}, "libraries": {"AWSSDK.Core/3.7.12.24": {"sha512": "uPAZ3a5Wu4k4Ml+TBAcOumZPxMpoHUHai0PdS3iLX0XPsDqAJPYviAIc1QXi2rVSDgqC2uVBxiEVSNYAgzoxbA==", "type": "package", "path": "awssdk.core/3.7.12.24", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "awssdk.core.3.7.12.24.nupkg.sha512", "awssdk.core.nuspec", "lib/net35/AWSSDK.Core.dll", "lib/net35/AWSSDK.Core.pdb", "lib/net35/AWSSDK.Core.xml", "lib/net45/AWSSDK.Core.dll", "lib/net45/AWSSDK.Core.pdb", "lib/net45/AWSSDK.Core.xml", "lib/netcoreapp3.1/AWSSDK.Core.dll", "lib/netcoreapp3.1/AWSSDK.Core.pdb", "lib/netcoreapp3.1/AWSSDK.Core.xml", "lib/netstandard2.0/AWSSDK.Core.dll", "lib/netstandard2.0/AWSSDK.Core.pdb", "lib/netstandard2.0/AWSSDK.Core.xml", "tools/account-management.ps1"]}, "AWSSDK.S3/3.7.9.42": {"sha512": "JNpSmndHm0uoqLQ3dypHPaP5p8+Yz3zZrr6MsA9+YhDy17L7I66nUYrmAhaXkgTSHlD/5uTAnvC9D6ZZO9QhUA==", "type": "package", "path": "awssdk.s3/3.7.9.42", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/AWSSDK.S3.CodeAnalysis.dll", "awssdk.s3.3.7.9.42.nupkg.sha512", "awssdk.s3.nuspec", "lib/net35/AWSSDK.S3.dll", "lib/net35/AWSSDK.S3.pdb", "lib/net35/AWSSDK.S3.xml", "lib/net45/AWSSDK.S3.dll", "lib/net45/AWSSDK.S3.pdb", "lib/net45/AWSSDK.S3.xml", "lib/netcoreapp3.1/AWSSDK.S3.dll", "lib/netcoreapp3.1/AWSSDK.S3.pdb", "lib/netcoreapp3.1/AWSSDK.S3.xml", "lib/netstandard2.0/AWSSDK.S3.dll", "lib/netstandard2.0/AWSSDK.S3.pdb", "lib/netstandard2.0/AWSSDK.S3.xml", "tools/install.ps1", "tools/uninstall.ps1"]}, "Kana.NET/1.0.6": {"sha512": "xc9teFuWQJ1h0YddJZxVWNTfvNPK648Wed4k8JYViNZN3txcV/5RXW/13nZsKjY/qndFSzJEta7mVr4jsVyXgg==", "type": "package", "path": "kana.net/1.0.6", "files": [".nupkg.metadata", ".signature.p7s", "kana.net.1.0.6.nupkg.sha512", "kana.net.nuspec", "lib/net5.0/Umayadia.Kana.dll", "lib/net6.0/Umayadia.Kana.dll", "lib/netcoreapp3.1/Umayadia.Kana.dll", "umayadia128.png"]}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"sha512": "ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "type": "package", "path": "microsoft.aspnetcore.http.features/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.xml", "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512", "microsoft.aspnetcore.http.features.nuspec"]}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.6": {"sha512": "Z4Ep2LDUSSNjriin0wKH4jF4vsjQ2ICwC9/5ntDVShQqy1C8AmmE5oK25jfthEVSIosDhJoWCescV3xKa9kcpg==", "type": "package", "path": "microsoft.entityframeworkcore.abstractions/6.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll", "lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.xml", "microsoft.entityframeworkcore.abstractions.6.0.6.nupkg.sha512", "microsoft.entityframeworkcore.abstractions.nuspec"]}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"sha512": "f34u2eaqIjNO9YLHBz8rozVZ+TcFiFs0F3r7nUJd7FRkVSxk8u4OpoK226mi49MwexHOR2ibP9MFvRUaLilcQQ==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.7.0.0.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.ObjectPool/9.0.0": {"sha512": "UbsU/gYe4nv1DeqMXIVzDfNNek7Sk2kKuAOXL/Y+sLcAR0HwFUqzg1EPiU88jeHNe0g81aPvvHbvHarQr3r9IA==", "type": "package", "path": "microsoft.extensions.objectpool/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.Extensions.ObjectPool.dll", "lib/net462/Microsoft.Extensions.ObjectPool.xml", "lib/net9.0/Microsoft.Extensions.ObjectPool.dll", "lib/net9.0/Microsoft.Extensions.ObjectPool.xml", "lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll", "lib/netstandard2.0/Microsoft.Extensions.ObjectPool.xml", "microsoft.extensions.objectpool.9.0.0.nupkg.sha512", "microsoft.extensions.objectpool.nuspec"]}, "Microsoft.Extensions.Primitives/7.0.0": {"sha512": "um1KU5kxcRp3CNuI8o/GrZtD4AIOXDk+RLsytjZ9QPok3ttLUelLKpilVPuaFT3TFjOhSibUAso0odbOaCDj3Q==", "type": "package", "path": "microsoft.extensions.primitives/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net6.0/Microsoft.Extensions.Primitives.dll", "lib/net6.0/Microsoft.Extensions.Primitives.xml", "lib/net7.0/Microsoft.Extensions.Primitives.dll", "lib/net7.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.7.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "MyNihongo.KanaConverter/1.0.3": {"sha512": "/HK2iltRWiYKGhFGKvcfMAyixzwiOkwQ1Ppp6/DZJQxPG0bKBYMYYIJd5oRyb0hDy11nDNea4UG/0xSsVGolRA==", "type": "package", "path": "mynihongo.kanaconverter/1.0.3", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "favico.png", "lib/netstandard2.0/MyNihongo.KanaConverter.dll", "mynihongo.kanaconverter.1.0.3.nupkg.sha512", "mynihongo.kanaconverter.nuspec"]}, "Newtonsoft.Json/13.0.2": {"sha512": "R2pZ3B0UjeyHShm9vG+Tu0EBb2lC8b0dFzV9gVn50ofHXh9Smjk6kTn7A/FdAsC8B5cKib1OnGYOXxRBz5XQDg==", "type": "package", "path": "newtonsoft.json/13.0.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.2.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "Pipelines.Sockets.Unofficial/2.2.2": {"sha512": "Bhk0FWxH1paI+18zr1g5cTL+ebeuDcBCR+rRFO+fKEhretgjs7MF2Mc1P64FGLecWp4zKCUOPzngBNrqVyY7Zg==", "type": "package", "path": "pipelines.sockets.unofficial/2.2.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Pipelines.Sockets.Unofficial.dll", "lib/net461/Pipelines.Sockets.Unofficial.xml", "lib/net472/Pipelines.Sockets.Unofficial.dll", "lib/net472/Pipelines.Sockets.Unofficial.xml", "lib/net5.0/Pipelines.Sockets.Unofficial.dll", "lib/net5.0/Pipelines.Sockets.Unofficial.xml", "lib/netcoreapp3.1/Pipelines.Sockets.Unofficial.dll", "lib/netcoreapp3.1/Pipelines.Sockets.Unofficial.xml", "lib/netstandard2.0/Pipelines.Sockets.Unofficial.dll", "lib/netstandard2.0/Pipelines.Sockets.Unofficial.xml", "lib/netstandard2.1/Pipelines.Sockets.Unofficial.dll", "lib/netstandard2.1/Pipelines.Sockets.Unofficial.xml", "pipelines.sockets.unofficial.2.2.2.nupkg.sha512", "pipelines.sockets.unofficial.nuspec"]}, "StackExchange.Redis/2.6.111": {"sha512": "49NlwihVG9I1YaPqYBx2e2yPqC4ecXMog8zVXMC3rjj2kufGkC3ofqvhOPBKP0c9ZQdJ3hhzduM7ckOQTE+gxg==", "type": "package", "path": "stackexchange.redis/2.6.111", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/StackExchange.Redis.dll", "lib/net461/StackExchange.Redis.xml", "lib/net472/StackExchange.Redis.dll", "lib/net472/StackExchange.Redis.xml", "lib/net5.0/StackExchange.Redis.dll", "lib/net5.0/StackExchange.Redis.xml", "lib/netcoreapp3.1/StackExchange.Redis.dll", "lib/netcoreapp3.1/StackExchange.Redis.xml", "lib/netstandard2.0/StackExchange.Redis.dll", "lib/netstandard2.0/StackExchange.Redis.xml", "stackexchange.redis.2.6.111.nupkg.sha512", "stackexchange.redis.nuspec"]}, "System.IO.Pipelines/5.0.1": {"sha512": "qEePWsaq9LoEEIqhbGe6D5J8c9IqQOUuTzzV6wn1POlfdLkJliZY3OlB0j0f17uMWlqZYjH7txj+2YbyrIA8Yg==", "type": "package", "path": "system.io.pipelines/5.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.IO.Pipelines.dll", "lib/net461/System.IO.Pipelines.xml", "lib/netcoreapp3.0/System.IO.Pipelines.dll", "lib/netcoreapp3.0/System.IO.Pipelines.xml", "lib/netstandard1.3/System.IO.Pipelines.dll", "lib/netstandard1.3/System.IO.Pipelines.xml", "lib/netstandard2.0/System.IO.Pipelines.dll", "lib/netstandard2.0/System.IO.Pipelines.xml", "ref/netcoreapp2.0/System.IO.Pipelines.dll", "ref/netcoreapp2.0/System.IO.Pipelines.xml", "system.io.pipelines.5.0.1.nupkg.sha512", "system.io.pipelines.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"sha512": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Encoding.CodePages/6.0.0": {"sha512": "ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "type": "package", "path": "system.text.encoding.codepages/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Text.Encoding.CodePages.targets", "buildTransitive/netcoreapp3.1/_._", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Text.Encoding.CodePages.dll", "lib/net461/System.Text.Encoding.CodePages.xml", "lib/net6.0/System.Text.Encoding.CodePages.dll", "lib/net6.0/System.Text.Encoding.CodePages.xml", "lib/netcoreapp3.1/System.Text.Encoding.CodePages.dll", "lib/netcoreapp3.1/System.Text.Encoding.CodePages.xml", "lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "lib/netstandard2.0/System.Text.Encoding.CodePages.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net461/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net461/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/netcoreapp3.1/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netcoreapp3.1/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netstandard2.0/System.Text.Encoding.CodePages.xml", "system.text.encoding.codepages.6.0.0.nupkg.sha512", "system.text.encoding.codepages.nuspec", "useSharedDesignerContext.txt"]}, "Entity/1.0.0": {"type": "project", "path": "../Entity/Entity.csproj", "msbuildProject": "../Entity/Entity.csproj"}, "ErrorCodeGenerator/1.0.0": {"type": "project", "path": "../ErrorCodeGenerator/ErrorCodeGenerator.csproj", "msbuildProject": "../ErrorCodeGenerator/ErrorCodeGenerator.csproj"}, "Helper/1.0.0": {"type": "project", "path": "../Helper/Helper.csproj", "msbuildProject": "../Helper/Helper.csproj"}}, "projectFileDependencyGroups": {"net6.0": ["AWSSDK.S3 >= 3.7.9.42", "Entity >= 1.0.0", "Helper >= 1.0.0", "Microsoft.AspNetCore.Http.Features >= 2.2.0"]}, "packageFolders": {"/Users/<USER>/.nuget/packages/": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Domain/Domain.csproj", "projectName": "Domain", "projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Domain/Domain.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Domain/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Entity/Entity.csproj": {"projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Entity/Entity.csproj"}, "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Helper/Helper.csproj": {"projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Helper/Helper.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AWSSDK.S3": {"target": "Package", "version": "[3.7.9.42, )"}, "Microsoft.AspNetCore.Http.Features": {"target": "Package", "version": "[2.2.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.404/RuntimeIdentifierGraph.json"}}}}