﻿namespace Domain.Models.SetMst;

public class OrderTooltipModel
{
    public OrderTooltipModel(
        string itemName, double suryo, string unitName, long rpNo, long rpEdaNo, int rowNo,
        int sinKouiKbn, string? itemCd, int unitSbt, double odrTermVal, int kohatuKbn,
        int syohoKbn, int syohoLimitKbn, int drugKbn, int yohoKbn, string? kokuji1,
        string? kokuji2, int isNodspRece, string? ipnCd, string? ipnName, string? bunkatu,
        string? cmtName, string? cmtOpt, string? fontColor, int commentNewline, int syohoSbt, int buiKbn, int rousaiKbn, string centerName, string centerCd, string masterSbt, int tosekiKbn, int sikyuKbn, int santeiKbn, int inOutKbn)
    {
        ItemName = itemName;
        Suryo = suryo;
        UnitName = unitName;
        RpNo = rpNo;
        RpEdaNo = rpEdaNo;
        RowNo = rowNo;
        SinKouiKbn = sinKouiKbn;
        ItemCd = itemCd;
        UnitSbt = unitSbt;
        OdrTermVal = odrTermVal;
        KohatuKbn = kohatuKbn;
        SyohoKbn = syohoKbn;
        SyohoLimitKbn = syohoLimitKbn;
        DrugKbn = drugKbn;
        YohoKbn = yohoKbn;
        Kokuji1 = kokuji1;
        Kokuji2 = kokuji2;
        IsNodspRece = isNodspRece;
        IpnCd = ipnCd;
        IpnName = ipnName;
        Bunkatu = bunkatu;
        CmtName = cmtName;
        CmtOpt = cmtOpt;
        FontColor = fontColor;
        CommentNewline = commentNewline;
        SyohoSbt = syohoSbt;
        BuiKbn = buiKbn;
        RousaiKbn = rousaiKbn;
        CenterName = centerName;
        CenterCd = centerCd;
        MasterSbt = masterSbt;
        TosekiKbn = tosekiKbn;
        SikyuKbn = sikyuKbn;
        SanteiKbn = santeiKbn;
        InOutKbn = inOutKbn;
    }
    public string ItemName { get; private set; }

    public double Suryo { get; private set; }

    public string UnitName { get; private set; }

    public long RpNo { get; private set; }

    public long RpEdaNo { get; private set; }

    public int RowNo { get; private set; }

    public int SinKouiKbn { get; private set; }

    public string? ItemCd { get; private set; }

    public int UnitSbt { get; private set; }

    public double OdrTermVal { get; private set; }

    public int KohatuKbn { get; private set; }

    public int SyohoKbn { get; set; }

    public int SyohoLimitKbn { get; private set; }

    public int DrugKbn { get; private set; }

    public int YohoKbn { get; private set; }

    public string? Kokuji1 { get; private set; }

    public string? Kokuji2 { get; private set; } 

    public int IsNodspRece { get; private set; }

    public string? IpnCd { get; private set; } 

    public string? IpnName { get; private set; } 

    public string? Bunkatu { get; private set; } 

    public string? CmtName { get; private set; } 

    public string? CmtOpt { get; private set; }

    public string? FontColor { get; private set; }

    public int CommentNewline { get; private set; }

    public int SyohoSbt { get; private set; }

    public int BuiKbn { get; private set; }

    public int RousaiKbn { get; private set; }

    public string CenterName { get; private set; }

    public string CenterCd { get; private set; }

    public string MasterSbt { get; private set; }

    public int TosekiKbn { get; private set; }

    public int SikyuKbn { get; private set; }

    public int SanteiKbn { get; private set; }

    public int InOutKbn { get; private set; }
}
