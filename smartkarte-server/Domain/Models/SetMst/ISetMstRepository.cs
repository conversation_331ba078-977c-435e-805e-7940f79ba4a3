﻿using Domain.Common;
using Helper.Constants;

namespace Domain.Models.SetMst;

public interface ISetMstRepository : IRepositoryBase
{
    List<SetMstModel> GetList(int hpId, int generationId, string textSearch, int userId, bool isPersonal);

    (bool status, List<SetMstModel> setMstModels) ReorderSetMst(int userId, int hpId, int setCdDragItem, int setCdDropItem);

    List<SetMstModel> PasteSetMst(int hpId, int userId, int generationId, int setCdCopyItem, int setCdPasteItem, bool pasteToOtherGroup, int copySetKbnEdaNo, int copySetKbn, int pasteSetKbnEdaNo, int pasteSetKbn);

    List<SetMstModel> SaveSetMstModel(int hpId, int userId, int generationId, SetMstModel setMstModel);

    bool CheckExistSetMstBySetCd(int hpId, int setCd);

    bool CheckExistSetMstBySetCd(int hpId, List<int> setCdList);

    SetMstTooltipModel GetToolTip(int hpId, int setCd);

    IEnumerable<SetMstModel> ReloadCache(int hpId, int generationId, int userId);

    void DeleteKey(int generationId, int hpId, int userId);

    bool CheckMyFolder(int hpId, int userId, SetMstModel setMstModel);

    bool CheckDeleteFolder(int hpId, SetMstModel setMstModel);

    bool CheckParentFolder(int hpId, int setCd);
}
