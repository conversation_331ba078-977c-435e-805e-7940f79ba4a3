﻿using System.Text.Json.Serialization;

namespace Domain.Models.SetMst;

public class SetMstModel
{
    [JsonConstructor]
    public SetMstModel(int hpId, int setCd, int setKbn, int setKbnEdaNo, int generationId, int level1, int level2, int level3, string setName, int weightKbn, int color, int isDeleted, int isGroup, int userId, string setKana)
    {
        HpId = hpId;
        SetCd = setCd;
        SetKbn = setKbn;
        SetKbnEdaNo = setKbnEdaNo;
        GenerationId = generationId;
        Level1 = level1;
        Level2 = level2;
        Level3 = level3;
        SetName = setName;
        WeightKbn = weightKbn;
        Color = color;
        IsDeleted = isDeleted;
        IsGroup = isGroup;
        UserId = userId;
        SetKana = setKana;
    }
    public SetMstModel(int hpId, int setCd, int setKbn, int setKbnEdaNo, int generationId, int level1, int level2, int level3, string setName, int weightKbn, int color, int isDeleted, int isGroup, bool isAddNew = false)
    {
        HpId = hpId;
        SetCd = setCd;
        SetKbn = setKbn;
        SetKbnEdaNo = setKbnEdaNo;
        GenerationId = generationId;
        Level1 = level1;
        Level2 = level2;
        Level3 = level3;
        SetName = setName;
        WeightKbn = weightKbn;
        Color = color;
        IsDeleted = isDeleted;
        IsGroup = isGroup;
        IsAddNew = isAddNew;
    }
    public SetMstModel(int hpId, int setCd, int setKbn, int setKbnEdaNo, int generationId, int level1, int level2, int level3, string setName, int weightKbn, int color, int isDeleted, bool isPersonal, string setKana, int isGroup, bool isAddNew = false)
    {
        HpId = hpId;
        SetCd = setCd;
        SetKbn = setKbn;
        SetKbnEdaNo = setKbnEdaNo;
        GenerationId = generationId;
        Level1 = level1;
        Level2 = level2;
        Level3 = level3;
        SetName = setName;
        WeightKbn = weightKbn;
        Color = color;
        IsDeleted = isDeleted;
        IsGroup = isGroup;
        IsAddNew = isAddNew;
        SetKana = setKana;
        IsPersonal = isPersonal;
    }
    public SetMstModel()
    {
        SetName = string.Empty;
        IsAddNew = false;
    }

    public SetMstModel(int hpId, int setCd)
    {
        SetName = string.Empty;
        IsAddNew = false;
        HpId = hpId;
        SetCd = setCd;
    }

    public int HpId { get; private set; }

    public int SetCd { get; private set; }

    public int SetKbn { get; private set; }

    public int SetKbnEdaNo { get; private set; }

    public int GenerationId { get; private set; }

    public int Level1 { get; private set; }

    public int Level2 { get; private set; }

    public int Level3 { get; private set; }

    public string SetName { get; private set; }

    public int WeightKbn { get; private set; }

    public int Color { get; private set; }

    public int IsDeleted { get; private set; }

    public int IsGroup { get; private set; }

    public bool IsAddNew { get; private set; }

    public int UserId { get; private set; }

    public string SetKana { get; private set; }

    public bool IsPersonal { get; private set; }
}