﻿using Domain.Common;
using Domain.Models.EventProcessor;
namespace Domain.Models.Renkei
{
    public interface IRenkeiRepository : IRepositoryBase
    {
        List<RenkeiModel> GetRenkeiModels(int hpId, string machineName, string eventCd);
        List<Renkei130DataModel> GetRenkei130Data(int hpId, ArgumentModel arg);

        List<Renkei260OdrInfModel> GetRenkei260OdrInf(int hpId, long ptId, int sinDate);

        UserMstModel GetUserMst(int hpId, int userId);

        PtInfModel GetPtInf(long ptId, int hpId);

        /// <summary>
        /// 患者グループ情報を取得する
        /// </summary>
        /// <param name="ptId"></param>
        /// <returns></returns>
        List<PtGrpInfModel> GetPtGrpInf(long ptId, int hpId);

        /// <summary>
        /// 患者メモを取得する
        /// </summary>
        /// <param name="ptId">患者ID</param>
        /// <returns></returns>
        PtMemoModel GetPtMemo(long ptId , int hpId);

        /// <summary>
        /// 来院情報を取得する
        /// </summary>
        /// <param name="ptId">患者ID</param>
        /// <param name="sinDate">診療日</param>
        /// <param name="raiinNo">来院番号</param>
        RaiinInfModel GetRaiinInf(int hpId, long ptId, int sinDate, long raiinNo);

        /// <summary>
        /// 来院区分情報を取得する
        /// </summary>
        /// <param name="ptId"></param>
        /// <param name="sinDate"></param>
        /// <param name="raiinNo"></param>
        /// <returns></returns>
        List<RaiinKbnInfModel> GetRaiinKbnInf(int hpId, long ptId, int sinDate, long raiinNo);

        /// <summary>
        /// 会計情報を取得する
        /// raiinNo, oyaRaiinNoは、いずれか一方のみ指定することを想定しています。
        /// 窓口精算画面からの呼び出しの場合、oyaRaiinNoを指定、収納一覧からの呼び出しの場合、raiinNoを想定
        /// </summary>
        /// <param name="ptId">患者ID</param>
        /// <param name="sinDate">診療日</param>
        /// <param name="raiinNo">来院番号(0-未指定)</param>
        /// <param name="oyaRaiinNo">親来院番号(0-未指定)</param>
        /// <returns></returns>
        List<KaikeiInfModel> GetKaikeiInf(int hpId, long ptId, int sinDate, long raiinNo, long oyaRaiinNo);

        /// <summary>
        /// 保険情報を取得する
        /// </summary>
        /// <param name="ptId">患者ID</param>
        /// <param name="sinDate">診療日</param>
        /// <param name="hokenPid">保険組み合わせID</param>
        /// <returns></returns>
        PtHokenPidModel GetHokenPid(int hpId, long ptId, int sinDate, int hokenPid);
    }
}
