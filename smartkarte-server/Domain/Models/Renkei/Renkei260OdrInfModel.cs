﻿using Domain.Constant;
using Entity.Tenant;
using Helper.Constants;

namespace Domain.Models.Renkei
{
    public class Renkei260OdrInfModel
    {
        public PtInf PtInf { get; } = null;
        public OdrInf OdrInf { get; } = null;

        public OdrInfDetail OdrInfDetail { get; } = null;
        public RaiinInf RaiinInf { get; } = null;
        public TenMst TenMst { get; } = null;
        public Entity.Tenant.KensaMst KensaMst { get; } = null;
        public int RaiinSeq { get; set; } = 0;
        public int OdrUchiNo { get; set; } = 0;
        public string Height { get; set; } = "0";
        public string Weight { get; set; } = "0";

        public Renkei260OdrInfModel(PtInf ptInf, OdrInf odrInf, OdrInfDetail odrDtl, RaiinInf raiinInf, TenMst tenMst, Entity.Tenant.KensaMst kensaMst, int raiinSeq, int odrUchiNo, string height, string weight)
        {
            PtInf = ptInf;
            OdrInf = odrInf;
            OdrInfDetail = odrDtl;
            RaiinInf = raiinInf;
            TenMst = tenMst;
            KensaMst = kensaMst;
            RaiinSeq = raiinSeq;
            OdrUchiNo = odrUchiNo;
            Height = height;
            Weight = weight;
        }

        public int KaId
        {
            get { return RaiinInf != null ? RaiinInf.KaId : 0; }
        }
        public int DoctorId
        {
            get
            {
                int ret = 0;

                if (OdrInf != null)
                {
                    if (OdrInf.UpdateId > 0)
                    {
                        ret = OdrInf.UpdateId;
                    }
                    else
                    {
                        ret = OdrInf.CreateId;
                    }
                }

                return ret;
            }
        }
        public int CreateId
        {
            get { return OdrInf != null ? OdrInf.CreateId : 0; }
        }
        public int SinDate
        {
            get { return OdrInf != null ? OdrInf.SinDate : 0; }
        }
        public long PtNum
        {
            get { return PtInf != null ? PtInf.PtNum : 0; }
        }
        public long RpNo
        {
            get { return OdrInf != null ? OdrInf.RpNo : 0; }
        }
        public long SinDateRpNo
        {
            //診療日単位で重複しない番号を採番
            get { return OdrInf != null ? OdrInf.RpNo * 100 + RaiinSeq : 0; }
        }
        public long RpEdaNo
        {
            get { return OdrInf != null ? OdrInf.RpEdaNo : 0; }
        }
        public long RaiinNo
        {
            get { return OdrInf != null ? OdrInf.RaiinNo : 0; }
        }
        public int HokenPid
        {
            get { return OdrInf != null ? OdrInf.HokenPid : 0; }
        }
        public int SortNo
        {
            get { return OdrInf != null ? OdrInf.SortNo : 0; }
        }
        public int OdrKouiKbn
        {
            get { return OdrInf != null ? OdrInf.OdrKouiKbn : 0; }
        }
        public int KouiKbn
        {
            get
            {
                if (OdrInf != null)
                {
                    return getKouiKbn(OdrInf.OdrKouiKbn);
                }
                return 0;
            }
        }
        public string RpName
        {
            get { return OdrInf != null ? OdrInf.RpName : ""; }
        }
        public int SikyuKbn
        {
            get { return OdrInf != null ? OdrInf.SikyuKbn : 0; }
        }
        public int InOutKbn
        {
            get { return OdrInf != null ? OdrInf.InoutKbn : 0; }
        }
        public int InOutKbnDsp
        {
            get
            {
                int ret = InOutKbn;

                if (ret < 0 || ret > 1)
                {
                    ret = 0;
                }

                return ret;
            }
        }
        public int DayCnt
        {
            get { return OdrInf != null ? OdrInf.DaysCnt : 0; }
        }
        public int SanteiKbn
        {
            get { return OdrInf != null ? OdrInf.SanteiKbn : 0; }
        }
        public string CreateMachine
        {
            get { return OdrInf != null ? OdrInf.CreateMachine : ""; }
        }
        public string CreateDate
        {
            get { return OdrInf != null ? OdrInf.CreateDate.ToString("yyyyMMdd") : ""; }
        }
        public string CreateTime
        {
            get { return OdrInf != null ? OdrInf.CreateDate.AddHours(9).ToString("HHmm") : ""; }
        }
        public string UpdateDate
        {
            get { return OdrInf != null ? OdrInf.UpdateDate.ToString("yyyyMMdd") : ""; }
        }
        public string UpdateTime
        {
            get { return OdrInf != null ? OdrInf.UpdateDate.AddHours(9).ToString("HHmm") : ""; }
        }
        public int IsDeleted
        {
            get { return OdrInf != null ? OdrInf.IsDeleted : 0; }
        }
        public int DeleteId
        {
            get { return IsDeleted == DeleteStatus.DeleteFlag ? OdrInf.UpdateId : 0; }
        }
        public string DeleteDate
        {
            get { return IsDeleted == DeleteStatus.DeleteFlag ? OdrInf.UpdateDate.ToString("yyyyMMdd") : ""; }
        }
        public string DeleteTime
        {
            get { return IsDeleted == DeleteStatus.DeleteFlag ? OdrInf.UpdateDate.ToString("HHmm") : ""; }
        }
        public string DeleteMachine
        {
            get { return IsDeleted == DeleteStatus.DeleteFlag ? OdrInf.UpdateMachine : ""; }
        }
        public int RowNo
        {
            get { return OdrInfDetail != null ? OdrInfDetail.RowNo : 0; }
        }
        public string ItemCd
        {
            get { return OdrInfDetail != null ? OdrInfDetail.ItemCd : ""; }
        }
        public string ItemName
        {
            get { return OdrInfDetail != null ? OdrInfDetail.ItemName : ""; }
        }
        public double Suryo
        {
            get { return OdrInfDetail != null ? OdrInfDetail.Suryo : 0; }
        }
        public int UnitSBT
        {
            get { return OdrInfDetail != null ? OdrInfDetail.UnitSBT : 0; }
        }
        public string UnitName
        {
            get { return OdrInfDetail != null ? OdrInfDetail.UnitName : ""; }
        }
        public int SijiCd1
        {
            get
            {
                int ret = 0;

                if (TenMst != null && OdrInf != null && OdrInfDetail != null)
                {
                    if (OdrInf.OdrKouiKbn >= 21 && OdrInf.OdrKouiKbn <= 23 && TenMst.DrugKbn > 0)
                    {
                        if (TenMst.KohatuKbn != 1)
                        {
                            if (OdrInfDetail.SyohoKbn == 2 && OdrInfDetail.SyohoLimitKbn == 0)
                            {
                                ret = 0;
                            }
                            else if (OdrInfDetail.SyohoKbn == 1 && OdrInfDetail.SyohoLimitKbn == 0)
                            {
                                ret = 3;
                            }
                            else if (OdrInfDetail.SyohoKbn == 0 && OdrInfDetail.SyohoLimitKbn == 0)
                            {
                                ret = 6;
                            }
                            else if (OdrInfDetail.SyohoKbn == 2 && OdrInfDetail.SyohoLimitKbn == 1)
                            {
                                ret = 7;
                            }
                            else if (OdrInfDetail.SyohoKbn == 2 && OdrInfDetail.SyohoLimitKbn == 2)
                            {
                                ret = 8;
                            }
                            else if (OdrInfDetail.SyohoKbn == 2 && OdrInfDetail.SyohoLimitKbn == 3)
                            {
                                ret = 9;
                            }
                            else if (OdrInfDetail.SyohoKbn == 3 && OdrInfDetail.SyohoLimitKbn == 0)
                            {
                                ret = 13;
                            }
                            else if (OdrInfDetail.SyohoKbn == 3 && OdrInfDetail.SyohoLimitKbn == 1)
                            {
                                ret = 14;
                            }
                            else if (OdrInfDetail.SyohoKbn == 3 && OdrInfDetail.SyohoLimitKbn == 2)
                            {
                                ret = 15;
                            }
                            else if (OdrInfDetail.SyohoKbn == 3 && OdrInfDetail.SyohoLimitKbn == 3)
                            {
                                ret = 16;
                            }
                        }
                        else
                        {
                            if (OdrInfDetail.SyohoKbn == 1 && OdrInfDetail.SyohoLimitKbn == 0)
                            {
                                ret = 4;
                            }
                            else if (OdrInfDetail.SyohoKbn == 2 && OdrInfDetail.SyohoLimitKbn == 0)
                            {
                                ret = 5;
                            }
                            else if (OdrInfDetail.SyohoKbn == 2 && OdrInfDetail.SyohoLimitKbn == 1)
                            {
                                ret = 10;
                            }
                            else if (OdrInfDetail.SyohoKbn == 2 && OdrInfDetail.SyohoLimitKbn == 2)
                            {
                                ret = 11;
                            }
                            else if (OdrInfDetail.SyohoKbn == 2 && OdrInfDetail.SyohoLimitKbn == 3)
                            {
                                ret = 12;
                            }
                            else if (OdrInfDetail.SyohoKbn == 3 && OdrInfDetail.SyohoLimitKbn == 0)
                            {
                                ret = 17;
                            }
                            else if (OdrInfDetail.SyohoKbn == 3 && OdrInfDetail.SyohoLimitKbn == 1)
                            {
                                ret = 18;
                            }
                            else if (OdrInfDetail.SyohoKbn == 3 && OdrInfDetail.SyohoLimitKbn == 2)
                            {
                                ret = 19;
                            }
                            else if (OdrInfDetail.SyohoKbn == 3 && OdrInfDetail.SyohoLimitKbn == 3)
                            {
                                ret = 20;
                            }
                        }
                    }
                }

                return ret;
            }
        }
        public int SinKouiKbn
        {
            get { return OdrInfDetail != null ? OdrInfDetail.SinKouiKbn : 0; }
        }

        public int DetailKouiKbn
        {
            get
            {
                if (OdrInfDetail != null)
                {
                    return getKouiKbn(OdrInfDetail.SinKouiKbn);
                }
                return 0;
            }
        }

        private int getKouiKbn(int kouiKbn)
        {
            if (kouiKbn == 0)
            {
                return 0;
            }
            else if (kouiKbn >= 10 && kouiKbn <= 14)
            {
                return kouiKbn * 10;
            }
            else if (kouiKbn >= 20 && kouiKbn <= 23)
            {
                return kouiKbn * 10;
            }
            else if (kouiKbn == 28)
            {
                return 240;
            }
            else if (kouiKbn >= 20 && kouiKbn <= 29)
            {
                return 200;
            }
            else if (kouiKbn >= 30 && kouiKbn <= 34)
            {
                return kouiKbn * 10;
            }
            else if (kouiKbn >= 35 && kouiKbn <= 39)
            {
                return 300;
            }
            else if (kouiKbn >= 40 && kouiKbn <= 89)
            {
                return kouiKbn / 10 * 100;
            }
            else if (kouiKbn == 96)
            {
                return 950;
            }
            else if (kouiKbn >= 100 && kouiKbn <= 101)
            {
                return 290;
            }

            return 0;
        }

        public string SanteiItemCd
        {
            get { return TenMst != null ? TenMst.SanteiItemCd : ""; }
        }
        public string KensaItemCd
        {
            get { return TenMst != null ? TenMst.KensaItemCd : ""; }
        }
        public int UketukeNo
        {
            get { return RaiinInf != null ? RaiinInf.UketukeNo : 0; }
        }
        public string CenterItemCd
        {
            get { return KensaMst != null ? KensaMst.CenterItemCd1 : ""; }
        }

        public int SyohoKbn
        {
            get
            {
                int ret = 0;

                if (OdrInf != null)
                {
                    if (OdrInf.OdrKouiKbn == 101)
                    {
                        ret = 1;
                    }
                }

                return ret;
            }
        }

        /// <summary>
        /// 項目種別
        /// </summary>
        public int ItemSbt
        {
            get
            {
                int ret = 0;

                if (BuiKbn > 0)
                {
                    // 60  部位
                    ret = 60;
                }
                else if (YohoKbn == 1)
                {
                    // 100 用法
                    ret = 100;

                    if (SinKouiKbn == 21)
                    {
                        // 101 内服用法
                        ret = 101;
                    }
                    else if (SinKouiKbn == 22)
                    {
                        // 102 頓服
                        ret = 102;
                    }
                    else if (SinKouiKbn == 23)
                    {
                        // 103 外用
                        ret = 103;
                    }
                }
                else if (YohoKbn == 2)
                {
                    // 110 第２用法
                    ret = 110;
                    if (SinKouiKbn == 21)
                    {
                        // 111 内服
                        ret = 111;
                    }
                    else if (SinKouiKbn == 22)
                    {
                        // 112 頓服
                        ret = 112;
                    }
                    else if (SinKouiKbn == 23)
                    {
                        // 113 外用
                        ret = 113;
                    }
                }
                else if (SinKouiKbn == 77)
                {
                    // 50  フィルム SIN_KOUI_KBN = 77
                    ret = 77;
                }
                else if (ItemCd == "840000100")
                {
                    // 電子媒体保存撮影
                    ret = 77;
                }
                else if (MasterSbt == "S")
                {
                    if (SinKouiKbn == 26)
                    {
                        // 26  麻毒加算
                        ret = 26;
                    }
                    else if (new string[] { "120003170", "120003270" }.Contains(ItemCd))
                    {
                        // 27  長期投薬加算
                        ret = 27;
                    }
                    else if (SinKouiKbn == 83)
                    {
                        // 83  処方箋料
                        ret = 83;
                    }
                    else if (MasterSbt == "S" && new string[] { "7", "9" }.Contains(Kokuji1) == false)
                    {
                        // 10  行為
                        ret = 10;

                        if (SinKouiKbn >= 60 && SinKouiKbn <= 69)
                        {
                            // 40  検査
                            ret = 40;

                            if (HandanGrpKbn == 6)
                            {
                                // 42  細菌
                                ret = 42;
                            }
                            else if (SinKouiKbn == 61)
                            {
                                // 41  検体
                                ret = 41;
                            }
                            else if (SinKouiKbn == 64)
                            {
                                // 43  病理
                                ret = 43;
                            }
                            else if (SinKouiKbn == 62)
                            {
                                // 44  生理
                                ret = 44;
                            }
                        }
                    }
                    else if (MasterSbt == "S" && new string[] { "7", "9" }.Contains(Kokuji1))
                    {
                        // 80  加算
                        ret = 80;
                    }
                }
                else if (MasterSbt == "Y")
                {
                    // 20  薬剤
                    ret = 20;

                    if (DrugKbn == 1)
                    {
                        // 21  内服
                        ret = 21;
                    }
                    else if (DrugKbn == 6)
                    {
                        // 22  外用
                        ret = 22;
                    }
                    else if (DrugKbn == 4)
                    {
                        // 23  注射
                        ret = 23;
                    }
                    else if (new int[] { 1, 2 }.Contains(ZoueiKbn))
                    {
                        // 25  造影剤
                        ret = 25;
                    }
                }
                else if (MasterSbt == "T")
                {
                    // 30  特材
                    ret = 30;
                }
                else if (MasterSbt == "C" || string.IsNullOrEmpty(ItemCd))
                {
                    // 90  コメント
                    ret = 90;
                }


                return ret;
            }
        }

        public string MasterSbt
        {
            get { return TenMst?.MasterSbt ?? ""; }
        }
        public string Kokuji1
        {
            get { return TenMst?.Kokuji1 ?? ""; }
        }

        public int DrugKbn
        {
            get { return TenMst?.DrugKbn ?? 0; }
        }
        public int ZoueiKbn
        {
            get { return TenMst?.ZoueiKbn ?? 0; }
        }
        public int HandanGrpKbn
        {
            get { return TenMst?.HandanGrpKbn ?? 0; }
        }
        public int BuiKbn
        {
            get { return TenMst?.BuiKbn ?? 0; }
        }
        public int YohoKbn
        {
            get { return TenMst?.YohoKbn ?? 0; }
        }

        public int HpId
        {
            get { return OdrInf?.HpId ?? 0; }
        }

        public string GetLineData(string param)
        {
            string ret = "";

            void AddRet(string val)
            {
                ret += "\"" + val + "\",";
            }


            // 1   病院コード ODR_INF.HP_ID
            AddRet($"{HpId}");
            //2   入外区分                                1固定
            AddRet("1");
            //3   診療科コード RAIIN_INF.KA_ID
            AddRet($"{KaId}");
            //4   医師コード CREATE_ID or UPDATE_ID
            AddRet($"{DoctorId}");
            //5   診療日 ODR_INF.SIN_DATE
            AddRet($"{SinDate}");
            //6   同日区分                                0固定 どうしても必要なら、RAIIN_NO等をキーにソートして順番を求めるか
            if (param.Contains("/douone"))
            {
                // 1固定
                AddRet("1");
            }
            else
            {
                AddRet($"{RaiinSeq}");
            }
            //7   患者番号 PT_INF.PT_NUM
            AddRet($"{PtNum}");
            //8   オーダー番号 ODR_INF.RP_NO
            AddRet($"{SinDateRpNo}");
            //9   内訳連番 ODR_INF.RP_EDA_NO
            AddRet($"{OdrUchiNo}");
            //10  予約番号 ODR_INF.RAIIN_NO
            AddRet($"{RaiinNo}");
            //11  ＲＰグループ                              0固定
            AddRet("0");
            //12  属性保険番号 ODR_INF.HOKEN_PID
            AddRet($"{HokenPid}");
            //13  表示順番 ODR_INF.SORT_NO
            AddRet($"{SortNo}");
            //14  属性行為コード ODR_INF.ODR_KOUI_KBN                                                            ※コードの意味が変わる
            AddRet($"{KouiKbn}");
            //15  オーダー名称 ODR_INF.RP_NAME
            AddRet($"{RpName}");
            //16  病棟コード                               0固定
            AddRet("0");
            //17  至急区分 ODR_INF.SIKYU_KBN
            AddRet($"{SikyuKbn}");
            //18  処方箋区分 ODR_INF.INOUT_KBN
            AddRet($"{InOutKbnDsp}");
            //19  一包化指示                               0固定
            AddRet("0");
            //20  日数回数 ODR_INF.DAYS_CNT
            AddRet($"{DayCnt}");
            //21  基本用法 空文字固定
            AddRet("");
            //22  コメントコード１
            AddRet("");
            //23  コメント名称１ 空文字固定
            AddRet("");
            //24  コメントコード２
            AddRet("");
            //25  コメント名称２ 空文字固定
            AddRet("");
            //26  身長
            AddRet($"{Height}");
            //27  体重
            AddRet($"{Weight}");
            //28  一日尿量                                0固定
            AddRet("0");
            //29  実施日                             0固定
            AddRet("0");
            //30  実施時間                                0固定
            AddRet("0");
            //31  実施後入力                               0固定
            AddRet("0");
            //32  実施入力フラグ                             0固定
            AddRet("0");
            //33  医師監査フラグ                             0固定 もしくは、ODR_KOUI_KBNから変換
            AddRet($"{SyohoKbn}");
            //34  薬局監査フラグ ODR_INF.SANTEI_KBN
            AddRet($"{SanteiKbn}");
            //35  指示箋発行フラグ                                0固定
            AddRet("0");
            //36  入力者コード ODR_INF.CREATE_ID
            AddRet($"{CreateId}");
            //37  入力端末 ODR_INF.CREATE_MACHINE
            AddRet($"{CreateMachine}");
            //38  予約区分                                0固定
            AddRet("0");
            //39  登録日 ODR_INF.CREATE_DATEから変換
            AddRet($"{CreateDate}");
            //40  登録時間 ODR_INF.CREATE_DATEから変換
            AddRet($"{CreateTime}");
            //41  更新日 ODR_INF.UPDATE_DATEから変換
            AddRet($"{UpdateDate}");
            //42  更新時間 ODR_INF.UPDATE_DATEから変換
            AddRet($"{UpdateTime}");
            //43  削除フラグ ODR_INF.IS_DELETED
            AddRet($"{IsDeleted}");
            //44  削除者コード ODR_INF.UPDATE_ID                                   ※IS_DELETED = 1のときのみ
            AddRet($"{DeleteId}");
            //45  削除端末 ODR_INF.UPDATE_MACHINE                                  ※IS_DELETED = 1のときのみ
            AddRet($"{DeleteMachine}");
            //46  行番号 ODR_INF_DETAIL.ROW_NO
            AddRet($"{RowNo}");
            //47  項目コード ODR_INF_DETAIL.ITEM_CD
            AddRet($"{ItemCd}");
            //48  漢字名称 ODR_INF_DETAIL.NAME
            AddRet($"{ItemName}");
            //49  数量 ODR_INF_DETAIL.SURYO
            AddRet($"{Suryo}");
            //50  単位区分 ODR_INF_DETAIL.UNIT_SBT
            AddRet($"{UnitSBT}");
            //51  単位名称 ODR_INF_DETAIL.UNIT_NAME
            AddRet($"{UnitName}");
            //52  指示コード１ ODR_INF_DETAIL.SYOHO_KBN,SYOHO_LIMIT_KBN
            AddRet($"{SijiCd1}");
            //53  指示コード２                              0固定
            AddRet("0");
            //54  明細行為コード ODR_INF_DETAIL.SIN_KOUI_KBN                                                         ※コードの意味が変わる
            //AddRet($"{SinKouiKbn}");
            AddRet($"{DetailKouiKbn}");
            //55  項目種別                                0固定
            AddRet($"{ItemSbt}");
            //56  薬効コード                               0固定
            AddRet("0");
            //57  明細保険番号                              0固定
            AddRet("0");
            //58  公費区分                                0固定
            AddRet("0");
            //59  基金コード TEN_MST.SANTEI_ITEM_CD
            AddRet($"{SanteiItemCd}");
            //60  外部コード１ TEN_MST.KENSA_ITEM_CD
            AddRet($"{KensaItemCd}");
            //61  受付順番 RAIIN_INF.UKETUKE_NO
            AddRet($"{UketukeNo}");
            //62  検査外注コード KENSA_MST.CENTER_ITEM_CD
            AddRet($"{CenterItemCd}");
            //63  予備１ 空文字固定
            AddRet("");
            //64  予備２ 空文字固定
            AddRet("");
            //65  予備３ 空文字固定
            AddRet("");

            ret = ret.TrimEnd(',');

            // 改行コードを削除
            ret = ret.Replace("\r", "").Replace("\n", "");

            return ret;
        }
    }
}
