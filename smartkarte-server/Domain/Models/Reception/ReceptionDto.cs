﻿using Domain.Models.Accounting;
using Domain.Models.AccountDue;
using Domain.Models.PortalCustomerPharmacy;
using Domain.Models.ReceptionSameVisit;
using Helper.Common;
using Helper.Constants;
using Helper.Enum;
using Helper.Extension;
using Domain.Models.Online;

namespace Domain.Models.Reception
{
    public class ReceptionDto
    {
        public ReceptionDto(int hpId, long ptId, int sinDate, long raiinNo, long oyaRaiinNo, int hokenPid, int santeiKbn, int status, int isYoyaku, string yoyakuTime, int yoyakuId, int uketukeSbt, string uketukeTime, int uketukeId, int uketukeNo, string sinStartTime, string sinEndTime, string kaikeiTime, int kaikeiId, int kaId, int tantoId, int syosaisinKbn, int jikanKbn, string comment)
        {
            HpId = hpId;
            PtId = ptId;
            SinDate = sinDate;
            RaiinNo = raiinNo;
            OyaRaiinNo = oyaRaiinNo;
            HokenPid = hokenPid;
            SanteiKbn = santeiKbn;
            Status = status;
            IsYoyaku = isYoyaku;
            YoyakuTime = yoyakuTime;
            YoyakuId = yoyakuId;
            UketukeSbt = uketukeSbt;
            UketukeTime = uketukeTime;
            UketukeId = uketukeId;
            UketukeNo = uketukeNo;
            SinStartTime = sinStartTime;
            SinEndTime = sinEndTime;
            KaikeiTime = kaikeiTime;
            KaikeiId = kaikeiId;
            KaId = kaId;
            TantoId = tantoId;
            SyosaisinKbn = syosaisinKbn;
            JikanKbn = jikanKbn;
            Comment = comment;
            DepartmentSName = string.Empty;
            HokenPatternModel = new();
            KaikeiInfModels = new();
            IsOnlineTreatment = false;
            IsFcoWaiting = false;
        }

        public ReceptionDto(long raiinNo, int uketukeNo, string departmentSName, int personNumber, HokenPatternModel hokenPatternModel, List<KaikeiInfModel> kaikeiInfModels, bool isOnlineTreatment, bool isFcoWaiting)
        {
            RaiinNo = raiinNo;
            UketukeNo = uketukeNo;
            DepartmentSName = departmentSName;
            PersonNumber = personNumber;
            HokenPatternModel = hokenPatternModel;
            KaikeiInfModels = kaikeiInfModels;
            YoyakuTime = string.Empty;
            UketukeTime = string.Empty;
            SinStartTime = string.Empty;
            SinEndTime = string.Empty;
            KaikeiTime = string.Empty;
            Comment = string.Empty;
            IsOnlineTreatment = isOnlineTreatment;
            IsFcoWaiting = isFcoWaiting;
        }

        public ReceptionDto(long raiinNo, int uketukeNo, int status , string departmentSName, int personNumber, HokenPatternModel hokenPatternModel, List<KaikeiInfModel> kaikeiInfModels, bool isOnlineTreatment, bool isFcoWaiting)
        {
            RaiinNo = raiinNo;
            UketukeNo = uketukeNo;
            Status = status;
            DepartmentSName = departmentSName;
            PersonNumber = personNumber;
            HokenPatternModel = hokenPatternModel;
            KaikeiInfModels = kaikeiInfModels;
            YoyakuTime = string.Empty;
            UketukeTime = string.Empty;
            SinStartTime = string.Empty;
            SinEndTime = string.Empty;
            KaikeiTime = string.Empty;
            Comment = string.Empty;
            IsOnlineTreatment = isOnlineTreatment;
            IsFcoWaiting = isFcoWaiting;
        }


        public ReceptionDto()
        {
            YoyakuTime = string.Empty;
            UketukeTime = string.Empty;
            SinStartTime = string.Empty;
            SinEndTime = string.Empty;
            KaikeiTime = string.Empty;
            Comment = string.Empty;
            DepartmentSName = string.Empty;
            KaikeiInfModels = new();
            HokenPatternModel = new();
            IsOnlineTreatment = false;
            IsFcoWaiting = false;
        }

        public ReceptionDto(int hpId, long ptId, string ptName, string ptKanaName,
                            int sex, string memo, int printEpsReference, int prescriptionIssueType,
                            int kaId, int tantoId, int treatmentDepartmentId, int sinDate, string yoyakuTime,
                            string yoyakuEndTime, int isYoyaku, int sinryoKbn, int hokenPid,
                            int confirmationType, string infoConsFlg, string prescriptionDeliInfo,
                            int status, string tantoName, string kaSname, string treatmentDepartmentName, string hokenName,
                            CombineStatusEnum canCombined, long raiinNo, bool hasMessage, List<Label> labels, long karteEditionRaiinNo, int reservedetailid,
                            PortalCustomerPharmacyModel pharmacyModel, int prescriptionReceiveMethod, string raiinMemo, int syosaisinKbn)
        {
            HpId = hpId;
            PtId = ptId;
            PtName = ptName;
            PtKanaName = ptKanaName;
            Sex = sex;
            Memo = memo;
            Status = status;
            KaSname = kaSname;
            TreatmentDepartmentName = treatmentDepartmentName;
            TantoName = tantoName;
            PrescriptionIssueType = prescriptionIssueType;
            KaId = kaId;
            TantoId = tantoId;
            TreatmentDepartmentId = treatmentDepartmentId;
            SinDate = sinDate;
            YoyakuTime = yoyakuTime;
            YoyakuEndTime = yoyakuEndTime;
            IsYoyaku = isYoyaku;
            SinryoKbn = sinryoKbn;
            HokenPid = hokenPid;
            ConfirmationType = confirmationType;
            InfoConsFlg = infoConsFlg;
            PrescriptionDeliInfo = prescriptionDeliInfo;
            HokenName = hokenName;
            PrintEpsReference = printEpsReference;
            CanCombine = canCombined;
            RaiinNo = raiinNo;
            HasMessage = hasMessage;
            ReserveTypeName = GetReserveName(sinryoKbn);
            PortalCustomerPharmacy = pharmacyModel;
            PrescriptionReceiveMethod = prescriptionReceiveMethod;
            Labels = labels;
            KarteEditionRaiinNo = karteEditionRaiinNo;
            ReserveDetailId = reservedetailid;
            Comment = raiinMemo;
            SyosaisinKbn = syosaisinKbn;
            IsOnlineTreatment = false;
            IsFcoWaiting = false;
        }

        public ReceptionDto(int hpId, long ptId, string ptName, string ptKanaName,
                            int sex, string memo, int printEpsReference, int prescriptionIssueType,
                            int kaId, int tantoId, int treatmentDepartmentId, int sinDate, string yoyakuTime,
                            string yoyakuEndTime, int isYoyaku, int sinryoKbn, int hokenPid,
                            int confirmationType, string infoConsFlg, string prescriptionDeliInfo,
                            int status, string tantoName, string kaSname, string treatmentDepartmentName, string hokenName,
                            CombineStatusEnum canCombined, long raiinNo, bool hasMessage, List<Label> labels, long karteEditionRaiinNo, int reservedetailid,
                            PortalCustomerPharmacyModel pharmacyModel, int prescriptionReceiveMethod, string raiinMemo, int syosaisinKbn, int jikanKbn, OnlineConfirmationHistoryModel onlineConfirmationHistory, 
                            bool isLinkCard, bool canEditPrescription, int meetingId = 0)
        {
            HpId = hpId;
            PtId = ptId;
            PtName = ptName;
            PtKanaName = ptKanaName;
            Sex = sex;
            Memo = memo;
            Status = status;
            KaSname = kaSname;
            TreatmentDepartmentName = treatmentDepartmentName;
            TantoName = tantoName;
            PrescriptionIssueType = prescriptionIssueType;
            KaId = kaId;
            TantoId = tantoId;
            TreatmentDepartmentId = treatmentDepartmentId;
            SinDate = sinDate;
            YoyakuTime = yoyakuTime;
            YoyakuEndTime = yoyakuEndTime;
            IsYoyaku = isYoyaku;
            SinryoKbn = sinryoKbn;
            HokenPid = hokenPid;
            ConfirmationType = confirmationType;
            InfoConsFlg = infoConsFlg;
            PrescriptionDeliInfo = prescriptionDeliInfo;
            HokenName = hokenName;
            PrintEpsReference = printEpsReference;
            CanCombine = canCombined;
            RaiinNo = raiinNo;
            HasMessage = hasMessage;
            ReserveTypeName = GetReserveName(sinryoKbn);
            PortalCustomerPharmacy = pharmacyModel;
            PrescriptionReceiveMethod = prescriptionReceiveMethod;
            Labels = labels;
            KarteEditionRaiinNo = karteEditionRaiinNo;
            ReserveDetailId = reservedetailid;
            Comment = raiinMemo;
            SyosaisinKbn = syosaisinKbn;
            IsOnlineTreatment = false;
            IsFcoWaiting = false;
            JikanKbn = jikanKbn;
            OnlineConfirmationHistory = onlineConfirmationHistory;
            IsLinkCard = isLinkCard;
            CanEditPrescription = canEditPrescription;
            MeetingId = meetingId;
        }

        public int HpId { get; private set; }

        public long PtId { get; private set; }

        public int SinDate { get; private set; }

        public long RaiinNo { get; private set; }

        public long OyaRaiinNo { get; private set; }

        public int HokenPid { get; private set; }

        public string HokenName { get; private set; } = string.Empty;

        public int SanteiKbn { get; private set; }

        public int Status { get; private set; }

        public int IsYoyaku { get; private set; }

        public string YoyakuTime { get; private set; } = string.Empty;

        public string Memo { get; private set; } = string.Empty;

        public int YoyakuId { get; private set; }

        public int UketukeSbt { get; private set; }

        public string UketukeTime { get; private set; } = string.Empty;

        public int UketukeId { get; private set; }

        public int UketukeNo { get; private set; }

        public string SinStartTime { get; private set; } = string.Empty;

        public string SinEndTime { get; private set; } = string.Empty;

        public string KaikeiTime { get; private set; } = string.Empty;

        public int KaikeiId { get; private set; }

        public int KaId { get; private set; }

        public string KaSname { get; private set; } = string.Empty;

        public int TantoId { get; private set; }

        public string TantoName { get; private set; } = string.Empty;

        public int SyosaisinKbn { get; private set; }

        public int JikanKbn { get; private set; }

        public string Comment { get; private set; } = string.Empty;

        public string DepartmentSName { get; private set; } = string.Empty;

        public int PersonNumber { get; private set; }

        public string PtName { get; private set; } = string.Empty;

        public string PtKanaName { get; private set; } = string.Empty;

        public int Sex { get; private set; }

        public int PaymentMethodCd { get; private set; }

        public string PrescriptionName { get; private set; } = string.Empty;

        public int PrescriptionIssueType { get; private set; }

        public int TreatmentDepartmentId { get; private set; }

        public string TreatmentDepartmentName { get; private set; } = string.Empty;

        public string YoyakuEndTime { get; private set; } = string.Empty;

        public int SinryoKbn { get; private set; }

        public int ConfirmationType { get; private set; }

        public string InfoConsFlg { get; private set; } = string.Empty;

        public string PrescriptionDeliInfo { get; private set; } = string.Empty;

        public int PrintEpsReference { get; private set; }

        public CombineStatusEnum CanCombine { get; private set; }

        public bool HasMessage { get; private set; }

        public List<Label> Labels { get; private set; }

        public long KarteEditionRaiinNo { get; private set; }


        public HokenPatternModel HokenPatternModel { get; private set; } = new();

        public List<KaikeiInfModel> KaikeiInfModels { get; private set; } = new();

        public int ReserveDetailId { get; private set; }

        public string RaiinBinding => RaiinNo > 0 ? GetRaiinName() : "(すべて)";

        public string PatternName => GetPatternName();

        public string ReserveTypeName { get; private set; }

        public PortalCustomerPharmacyModel PortalCustomerPharmacy { get; private set; } = new();

        public int PrescriptionReceiveMethod { get; private set; }

        public bool IsOnlineTreatment { get; private set; }
        public bool IsFcoWaiting { get; private set; }
        public OnlineConfirmationHistoryModel OnlineConfirmationHistory { get; private set; }
        public bool CanEditPrescription { get; private set; }

        public bool IsLinkCard { get; private set; }

        public int MeetingId { get; private set; }

        private string GetRaiinName()
        {
            string name = DepartmentSName;
            string hokenText = PatternName;
            string receptionOder = UketukeNo.ToString("D4");
            if (!string.IsNullOrEmpty(hokenText))
            {
                name = receptionOder + " " + name + "【" + hokenText + "】";
            }

            return name;
        }

        public string GetPatternName()
        {
            int hokenId = HokenPatternModel?.HokenId ?? 0;

            var kaikeInf = KaikeiInfModels?.FirstOrDefault(item => item.HokenId == hokenId);

            if (kaikeInf == null)
                return string.Empty;

            string patternName = string.Empty;

            string hokenKbn = kaikeInf.HokenKbn.AsString().PadLeft(2, '0');
            string hokenSbtCd = kaikeInf.HokenSbtCd.AsString().PadRight(3, '0');
            string receSbt = kaikeInf.ReceSbt.AsString().PadRight(4, '0');
            int firstNum = receSbt[0].AsInteger();
            int secondNum = receSbt[1].AsInteger();
            int thirdNum = hokenSbtCd[2].AsInteger();
            int fourthNum;
            if ((firstNum == 1 && secondNum == 2)
                || (firstNum == 1 && secondNum == 3))
            {
                // In case ReceSbt = 12x2: 公費, fourthNum is always = 0
                // In case ReceSbt = 13x8 or 13x0: 後期, fourthNum is always = 0
                fourthNum = 0;
            }
            else
            {
                fourthNum = receSbt[3].AsInteger();
            }
            string key = hokenKbn + firstNum.AsString() + secondNum.AsString() + thirdNum.AsString() + fourthNum.AsString();

            if (HokenPatternConstant.PatternNameDic.ContainsKey(key))
            {
                patternName = HokenPatternConstant.PatternNameDic[key];
            }
            if (kaikeInf.HokenKbn == 0)
            {
                int kohiCount = GetKohiCount(kaikeInf);
                if (kohiCount > 0)
                {
                    patternName += GetKohiCountName(kohiCount);
                }
            }
            if (string.IsNullOrWhiteSpace(patternName))
            {
                patternName = "自費レセ100％";
            }
            else
            {
                patternName = string.Format($"{patternName} {kaikeInf.DispRate}％");
            }

            return patternName;
        }

        private int GetKohiCount(KaikeiInfModel kaikeInf)
        {
            if (kaikeInf == null)
            {
                return 0;
            }
            int result = 0;
            if (kaikeInf.Kohi1Id > 0 && kaikeInf.Kohi1Houbetu != HokenConstant.HOUBETU_MARUCHO)
            {
                result++;
            }
            if (kaikeInf.Kohi2Id > 0 && kaikeInf.Kohi2Houbetu != HokenConstant.HOUBETU_MARUCHO)
            {
                result++;
            }
            if (kaikeInf.Kohi3Id > 0 && kaikeInf.Kohi3Houbetu != HokenConstant.HOUBETU_MARUCHO)
            {
                result++;
            }
            if (kaikeInf.Kohi4Id > 0 && kaikeInf.Kohi4Houbetu != HokenConstant.HOUBETU_MARUCHO)
            {
                result++;
            }
            if (result > 0)
            {
                return result + 1;
            }
            return result;
        }

        private string GetKohiCountName(int kohicount)
        {
            if (kohicount <= 0)
            {
                return string.Empty;
            }
            if (kohicount == 1)
            {
                return "単独";
            }
            else
            {
                return HenkanJ.ToFullsize(kohicount.AsString()) + "併";
            }
        }

        private string GetReserveName(int reserveType) => reserveType switch
        {
            1 => "オンライン診療",
            0 => "対面診療",
            _ => string.Empty
        };
    }
}
