﻿using Microsoft.AspNetCore.Http;

namespace Domain.Models.Reception
{
    public class AddPatientFileModel
    {
        
        public long PtId { get; set; }

        public string FileName { get; set; }

        public string S3FileName { get; set; } = String.Empty;

        public string DspFileName { get; set; }
        
        public string Memo { get; set; } = String.Empty;

        public int CategoryCd { get; set; }

        public bool OverWrite { get; set; }

        public IFormFile StreamFile { get; set; }
        
    }
}
