﻿using System.Runtime.InteropServices;
using Domain.Common;
using Domain.Models.ConfirmOnline;
using Domain.Models.Family;
using Domain.Models.PatientInfor;
using Helper.Constants;

namespace Domain.Models.Reception
{
    public interface IReceptionRepository : IRepositoryBase
    {
        long Insert(ReceptionSaveDto dto, int hpId, int userId);

        bool Update(ReceptionSaveDto dto, int hpId, int userId);

        ReceptionModel Get(int hpId, long raiinNo, bool flag = false);

        List<ReceptionRowModel> GetList(int hpId, int sinDate, long raiinNo, long ptId, [Optional] bool isGetAccountDue, [Optional] bool isGetFamily, int isDeleted = 2, bool searchSameVisit = false);

        /// <summary>
        ///  get reception paging list
        /// </summary>
        /// <param name="hpId"></param>
        /// <param name="sinDate"></param>
        /// <param name="raiinNo"></param>
        /// <param name="ptId"></param>
        /// <param name="isGetAccountDue"></param>
        /// <param name="isGetFamily"></param>
        /// <param name="isDeleted"></param>
        /// <param name="searchSameVisit"></param>
        /// <param name="limit"></param>
        /// <param name="offset"></param>
        /// <returns></returns>
        (List<ReceptionRowModel> receptionInfos, int totalItems) GetPagingList(int hpId, int sinDate, long raiinNo, long ptId, bool isGetAccountDue, bool isGetFamily, int isDeleted = 2, bool searchSameVisit = false, int limit = 0, int offset = 0);

        IEnumerable<ReceptionModel> GetList(int hpId, long ptId, int karteDeleteHistory);

        ReceptionModel GetYoyakuRaiinInf(int hpId, long ptId, int sinDate);

        ReceptionModel GetReceptionComments(int hpId, long raiinNo);

        ReceptionModel GetReceptionVisiting(int hpId, long raiinNo);

        List<ReceptionModel> GetLastRaiinInfs(int hpId, long ptId, int sinDate, bool isGetSysosaisin = false);

        (bool resultSave, int oldStatus, int typeInvalid) UpdateStatus(int hpId, long raiinNo, int status, int userId, long ptId);

        bool UpdateUketukeNo(int hpId, long raiinNo, int uketukeNo, int userId);

        bool UpdateUketukeTime(int hpId, long raiinNo, string uketukeTime, int userId);

        bool UpdateSinStartTime(int hpId, long raiinNo, string sinStartTime, int userId);

        bool UpdateUketukeSbt(int hpId, long raiinNo, int uketukeSbt, int userId);

        bool UpdateTantoId(int hpId, long raiinNo, int tantoId, int userId);

        bool UpdateKaId(int hpId, long raiinNo, int kaId, int userId);

        bool CheckListNo(int hpId, List<long> raininNos);

        int GetFirstVisitWithSyosin(int hpId, long ptId, int sinDate);

        ReceptionModel GetDataDefaultReception(int hpId, long ptId, int sinDate, int defaultSettingDoctor);

        int GetMaxUketukeNo(int hpId, int sindate);

        long InitDoctorCombobox(int userId, int tantoId, long ptId, int hpId, int sinDate);

        bool CheckExistRaiinNo(int hpId, long ptId, long raiinNo);

        List<ReceptionModel> GetListRaiinInf(int hpId, long ptId, int pageIndex, int pageSize, int isDeleted, bool isAll = false);

        ReceptionModel? GetLastKarute(int hpId, long ptNum);

        List<Tuple<int, long, long>> Delete(bool flag, int hpId, long ptId, int userId, int sinDate, List<Tuple<long, long, int>> receptions);

        bool CheckExistOfRaiinNos(int hpId, List<long> raininNos);

        List<ReceptionModel> GetRaiinListWithKanInf(int hpId, long ptId);

        ReceptionModel GetLastVisit(int hpId, long ptId, int sinDate, bool isGetSysosaisin = false);

        List<SameVisitModel> GetListSameVisit(int hpId, long ptId, int sinDate);

        bool UpdateIsDeleted(int hpId, long raiinNo);

        List<RaiinInfToPrintModel> GetOutDrugOrderList(int hpId, int fromDate, int toDate);

        int GetStatusRaiinInf(int hpId, long raiinNo, long ptId);

        ReceptionModel GetRaiinInfBySinDate(int hpId, long ptId, int sinDate);

        int GetNextUketukeNoBySetting(int hpId, int sindate, int infKbn, int kaId, int uketukeMode, int defaultUkeNo);

        RaiinInfModel? GetRaiinInf(int hpId, long ptId, int sinDate, long raiinNo);

        List<ReceptionForViewDto> GetReceptionRowModels(int hpId, int sinDate, int? filterId, List<int> kaIds, List<int> treatmentDepartmentIds, List<int> userIds, List<int> treatmentStatusIds, int limit, int offset, string sortField, List<int> labels, List<long> listRaiinNo, long ptId = CommonConstants.InvalidId);

        bool UpdateTreatmentDepartment(int hpId, long raiinNo, int treatmentDepartmentId, int userId);
        bool UpdateMonshinStatus(int hpId, long raiinNo, int monshinStatus, int userId);
        List<ReceptionForViewDto> GetRecptionList(int hpId, int sinDate, long raiinNo = CommonConstants.InvalidId, int limit = RaiinInfConst.DefaultLimit, int offset = RaiinInfConst.DefaultOffset, string sortField = RaiinInfConst.DefaultSortField, int? filterId = null, List<int> kaIds = null, List<int> treatmentDepartmentIds = null, List<int> userIds = null, List<int> treatmentStatusIds = null, List<int> labels = null, long ptId = CommonConstants.InvalidId);

        List<ReceptionCombineModel> GetReceptionCombine(int hpId, int sinDate, long raiinNo, int hokenPid, long ptId, bool isCombined);

        int SaveCombineBill(List<ReceptionCombineModel> combineBills, int userId, bool isSplited, long raiinNo);

        List<GetPatientFileModel> GetPatientFiles(int hpId, long ptId, int categoryCd, string fileName);

        bool DeletePatientFile(long ptId, int hpId, int categoryCd, int fileId, int userId);

        bool AddPatientFile(List<AddPatientFileModel> listItem, int hpId, int userId, int getDate);

        List<MappingMemberModel> GetMappingMeber(int hpId, long ptId, long ptNum, int status, int portalCustomerId, int birthday, int sex, string name, string kanaName, int sinDate, int modelNum);

        UpdateMappingModelDto UpdatePatientLinking(int hospitalArrivalStatus, UpdateConfirmOnlineModel updateConfirmOnlineModel);

        long InsertFromCalendarOrPortal(InsertFromCalendarOrPortalDto dto, string raiinComment);

        ReceptionModel GetHeader(int hpId, long raiinNo, bool flag = false);

        bool SubmitConfirmation(int hpId, long ptId, long onlineConfiemId, long raiino, int userId);
        long InsertRaiinInf(ReceptionSaveDto dto, int hpId, int userId);

        List<ReceptionForViewDto> GetOnlinePatientData(int sinDate, int hpid, long onlineConfirmationId);
        bool CheckNo(int hpId, long raininNo, long ptId, int sinDate);

        (long, bool) UpdatePrescription(int hpId, long raiinNo, int userId, int prescriptionIssueType, int printEpsReference, bool? checkStatus = true);

        List<long> UpdateBookingInfo(int hpId, List<int> reserveDetailIds, int sinDate, string yoyakuTime, string yoyakuEndTime, int treatmentDepartmentId, int userId);

        List<ReceptionForViewDto> GetReceptionListByRaiinNoes(int hpId, int sinDate, List<long> listRaiinNo, int limit = RaiinInfConst.DefaultLimit, int offset = RaiinInfConst.DefaultOffset, string sortField = RaiinInfConst.DefaultSortField, int? filterId = null, List<int> kaIds = null, List<int> treatmentDepartmentIds = null, List<int> userIds = null, List<int> treatmentStatusIds = null, List<int> labels = null, long ptId = CommonConstants.InvalidId);

        bool CheckRaiinInf(int hpId, long raiinNo, bool flag = false);

        public (int prescriptionIssueType, int printEpsReference) GetDefaultPrescription(int hpId, long ptId, int sinDate, long raiinNo, int sysConfVal);

        bool CheckLinkCard(int hpId);

        int ValidateReservation(int hpId, int userId, long ptId, long raiinNo, int sinDate);

        (bool Result, int SinDate, List<long> ListRaiinNo) UpdateRaiinCmtByBooking(int hpId, int reserveDetailId, int userId, string raiinCmt);

    }
}
