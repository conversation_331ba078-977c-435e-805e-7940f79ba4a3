﻿
namespace Domain.Models.Reception
{
    public class MappingMemberModel
    {
        public long PtId { get; set; }

        public long PtNum { get; private set; }

        public string Name { get; private set; }

        public string KanaName { get; private set; }

        public int Sex { get; private set; }

        public int Birthday { get; private set; }

        public long? RaiinNo { get; private set; }

        public int? PortalCustomerId { get; private set; }

        public int? OnlineConfirmationHistoryId { get; private set; }

        public bool IsNameEqual { get; private set; }

        public bool IsKanaNameEqual { get; private set; }

        public bool IsSexEqual { get; private set; }

        public bool IsBirthDayEqual { get; private set; }


        public MappingMemberModel(
            long ptId,
            long ptNum, 
            string name, 
            string kanaName, 
            int sex, 
            int birthday, 
            long? raiinNo,
            int? portalCustomerId, 
            int? onlineConfirmationHistoryId,
            bool isNameEqual, 
            bool isKanaNameEqual, 
            bool isSexEqual, 
            bool isBirthDayEqual)
        {
            PtId = ptId;
            PtNum = ptNum;
            Name = name;
            KanaName = kanaName;
            Sex = sex;
            Birthday = birthday;
            RaiinNo = raiinNo;
            PortalCustomerId = portalCustomerId;
            OnlineConfirmationHistoryId = onlineConfirmationHistoryId;
            IsNameEqual = isNameEqual;
            IsKanaNameEqual = isKanaNameEqual;
            IsSexEqual = isSexEqual;
            IsBirthDayEqual = isBirthDayEqual;
        }
    }
}
