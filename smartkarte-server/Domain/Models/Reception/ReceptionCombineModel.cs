﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Domain.Models.Reception
{
    public class ReceptionCombineModel
    {
        public int HpId { get; set; }
        public long RaiinNo { get; set; }
        public long OyaRaiinNo { get; set; }
        public long PtId { get; set; }
        public string PtName { get; set; } = string.Empty;
        public int TreatmentDepartmentId { get; set; }
        public string TreatmentDepartmentName { get;set; } = string.Empty;
        public int KaId { get; set; }
        public string KaName { get; set; } = string.Empty;
        public int TantoId { get; set; }
        public string TantoName { get; set; } = string.Empty;
        public int UketukeNo { get; set; }
        public ReceptionCombineModel(int hpId, long raiinNo, long oyaRaiinNo, long ptId, string ptName,
                                     int treatmentDepartmentId, string treatmentDepartmentName,
                                     int kaId, string kaName, int tantoId, string tantoName, int uketukeNo) 
        {
            HpId = hpId;
            RaiinNo = raiinNo;
            OyaRaiinNo = oyaRaiinNo;
            PtId = ptId;
            PtName = ptName;
            TreatmentDepartmentId = treatmentDepartmentId;
            TreatmentDepartmentName = treatmentDepartmentName;
            KaId = kaId;
            KaName = kaName;
            TantoId = tantoId;
            TantoName = tantoName;
            UketukeNo = uketukeNo;
        }
    }
}
