﻿namespace Domain.Models.Reception
{
    public class InsertFromCalendarOrPortalDto
    {
        public InsertFromCalendarOrPortalDto(int hpId, long ptId, int sinDate, string yoyakuTime, int yoyakuId, int tantoId, int treatmentDepartmentId, string yoyakuEndTime, int reserveDetailId)
        {
            HpId = hpId;
            PtId = ptId;
            SinDate = sinDate;
            YoyakuTime = yoyakuTime;
            YoyakuId = yoyakuId;
            TantoId = tantoId;
            TreatmentDepartmentId = treatmentDepartmentId;
            YoyakuEndTime = yoyakuEndTime;
            ReserveDetailId = reserveDetailId;
        }

        public int HpId { get; private set; }

        public long PtId { get; private set; }

        public int SinDate { get; private set; }

        public string YoyakuTime { get; private set; }

        public int YoyakuId { get; private set; }


        public int TantoId { get; private set; }

        public int TreatmentDepartmentId { get; private set; }

        public string YoyakuEndTime { get; private set; }

        public int ReserveDetailId { get; private set; }
    }
}
