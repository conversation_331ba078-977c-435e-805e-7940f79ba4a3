﻿using System.Text.Json.Serialization;
using Domain.Common;
using Domain.Models.Insurance;
using Domain.Models.Online;
using Domain.Models.PortalCustomerPharmacy;
using Helper.Enum;

namespace Domain.Models.Reception
{
    public class ReceptionModel
    {
        public int HpId { get; private set; }

        public long PtId { get; private set; }

        public int SinDate { get; private set; }

        public int UketukeNo { get; private set; }

        public string SName { get; private set; } = string.Empty;

        public string PtMemo { get; private set; } = string.Empty;

        public string KaSname { get; private set; } = string.Empty;

        public string Houbetu { get; private set; } = string.Empty;

        public string HokensyaNo { get; private set; } = string.Empty;

        public int HokenKbn { get; private set; }

        public long RaiinNo { get; private set; }

        public long OyaRaiinNo { get; private set; }

        public int HokenPid { get; private set; }

        public int SanteiKbn { get; private set; }

        public int Status { get; private set; }

        public int IsYoyaku { get; private set; }

        public string YoyakuTime { get; private set; } = string.Empty;

        public int YoyakuId { get; private set; }

        public int UketukeSbt { get; private set; }

        public string UketukeTime { get; private set; } = string.Empty;

        public int UketukeId { get; private set; }

        public string SinStartTime { get; private set; } = string.Empty;

        public string SinEndTime { get; private set; } = string.Empty;

        public string KaikeiTime { get; private set; } = string.Empty;

        public int KaikeiId { get; private set; }

        public int KaId { get; private set; }

        public int TantoId { get; private set; }

        public string TantoName { get; private set; } = string.Empty;

        public int SyosaisinKbn { get; private set; }

        public int JikanKbn { get; private set; }

        public string Comment { get; private set; } = string.Empty;

        public int HokenId { get; private set; }

        public bool IsDeleted { get; private set; }

        public int Sex { get; private set; }

        public string TreatmentDepartmentName { get; private set; } = string.Empty;

        public string YoyakuEndTime { get; private set; } = string.Empty;

        public int SinryoKbn { get; private set; }

        public string DepartmentSName { get; private set; } = string.Empty;

        public int ConfirmationType { get; private set; }

        public string InfoConsFlg { get; private set; } = string.Empty;

        public string PrescriptionDeliInfo { get; private set; } = string.Empty;

        public string HokenHoubetu { get; private set; } = string.Empty;

        public int HokenSbtCd { get; private set; }

        public CombineStatusEnum CanCombine { get; private set; }
        public string HokenName { get; private set; } = string.Empty;

        public bool HasMessage { get; private set; }

        public List<KohiInfModel> Kohi { get; private set; } = new();

        public string HokenKbnName { get; private set; }

        public int TreatmentDepartmentId { get; private set; }

        public int PrintEpsReference { get; private set; }

        public int PrescriptionIssueType { get; private set; }

        public PortalCustomerPharmacyModel PortalCustomerPharmacy { get; private set; } = new();

        public int PrescriptionReceiveMethod { get; private set; }
        public List<Label> Labels { get; private set; } = new();

        public long KateEditionRaiinNo { get; private set; }

        public int Reservedetailid { get; private set; }

        public int OnlineConfirmationHistoryId { get; private set; }
        public OnlineConfirmationHistoryModel OnlineConfirmationHistory { get; private set; }

        public bool CanEditPrescription { get; private set; }

        public bool IsLinkCard { get; private set; }

        public int MeetingId { get; private set; }

        public DateTime UpdateDate { get; private set; }

        [JsonConstructor]
        public ReceptionModel(int hpId, long ptId, int sinDate, long raiinNo, long oyaRaiinNo, int hokenPid, int santeiKbn, int status, int isYoyaku, string yoyakuTime, int yoyakuId, int uketukeSbt, string uketukeTime, int uketukeId, int uketukeNo, string sinStartTime, string sinEndTime, string kaikeiTime, int kaikeiId, int kaId, int tantoId, int syosaisinKbn, int jikanKbn, string comment, int onlineConfirmationHistoryId = 0)
        {
            HpId = hpId;
            PtId = ptId;
            SinDate = sinDate;
            RaiinNo = raiinNo;
            OyaRaiinNo = oyaRaiinNo;
            HokenPid = hokenPid;
            SanteiKbn = santeiKbn;
            Status = status;
            IsYoyaku = isYoyaku;
            YoyakuTime = yoyakuTime;
            YoyakuId = yoyakuId;
            UketukeSbt = uketukeSbt;
            UketukeTime = uketukeTime;
            UketukeId = uketukeId;
            UketukeNo = uketukeNo;
            SinStartTime = sinStartTime;
            SinEndTime = sinEndTime;
            KaikeiTime = kaikeiTime;
            KaikeiId = kaikeiId;
            KaId = kaId;
            TantoId = tantoId;
            SyosaisinKbn = syosaisinKbn;
            JikanKbn = jikanKbn;
            Comment = comment;
            TantoName = string.Empty;
            SName = string.Empty;
            KaSname = string.Empty;
            Houbetu = string.Empty;
            HokensyaNo = string.Empty;
            HokenKbnName = string.Empty;
            OnlineConfirmationHistoryId = onlineConfirmationHistoryId;
        }

        public ReceptionModel(int hpId, long ptId, int sinDate, long raiinNo, long oyaRaiinNo, int hokenPid, int santeiKbn, int status, int isYoyaku, string yoyakuTime, int yoyakuId, int uketukeSbt, string uketukeTime, int uketukeId, int uketukeNo, string sinStartTime, string sinEndTime, string kaikeiTime, int kaikeiId, int kaId, int tantoId, int syosaisinKbn, int jikanKbn, string comment, string tantoName, string kaSname, int reservedetailid, int treatmentDepartmentId)
        {
            HpId = hpId;
            PtId = ptId;
            SinDate = sinDate;
            RaiinNo = raiinNo;
            OyaRaiinNo = oyaRaiinNo;
            HokenPid = hokenPid;
            SanteiKbn = santeiKbn;
            Status = status;
            IsYoyaku = isYoyaku;
            YoyakuTime = yoyakuTime;
            YoyakuId = yoyakuId;
            UketukeSbt = uketukeSbt;
            UketukeTime = uketukeTime;
            UketukeId = uketukeId;
            UketukeNo = uketukeNo;
            SinStartTime = sinStartTime;
            SinEndTime = sinEndTime;
            KaikeiTime = kaikeiTime;
            KaikeiId = kaikeiId;
            KaId = kaId;
            TantoId = tantoId;
            SyosaisinKbn = syosaisinKbn;
            JikanKbn = jikanKbn;
            Comment = comment;
            TantoName = tantoName;
            SName = string.Empty;
            KaSname = kaSname;
            Houbetu = string.Empty;
            HokensyaNo = string.Empty;
            HokenKbnName = string.Empty;
            Reservedetailid = reservedetailid;
            TreatmentDepartmentId = treatmentDepartmentId;
        }

        public ReceptionModel(int hpId, long ptId, int sinDate, long raiinNo, long oyaRaiinNo, int hokenPid, int santeiKbn, int status, int isYoyaku, string yoyakuTime, int yoyakuId, int uketukeSbt, string uketukeTime, int uketukeId, int uketukeNo, string sinStartTime, string sinEndTime, string kaikeiTime, int kaikeiId, int kaId, int tantoId, int syosaisinKbn, int jikanKbn, string comment, string tantoName, string kaSname, int reservedetailid, int printEpsReference, int prescriptionIssueType, DateTime updateDate)
        {
            HpId = hpId;
            PtId = ptId;
            SinDate = sinDate;
            RaiinNo = raiinNo;
            OyaRaiinNo = oyaRaiinNo;
            HokenPid = hokenPid;
            SanteiKbn = santeiKbn;
            Status = status;
            IsYoyaku = isYoyaku;
            YoyakuTime = yoyakuTime;
            YoyakuId = yoyakuId;
            UketukeSbt = uketukeSbt;
            UketukeTime = uketukeTime;
            UketukeId = uketukeId;
            UketukeNo = uketukeNo;
            SinStartTime = sinStartTime;
            SinEndTime = sinEndTime;
            KaikeiTime = kaikeiTime;
            KaikeiId = kaikeiId;
            KaId = kaId;
            TantoId = tantoId;
            SyosaisinKbn = syosaisinKbn;
            JikanKbn = jikanKbn;
            Comment = comment;
            TantoName = tantoName;
            SName = string.Empty;
            KaSname = kaSname;
            Houbetu = string.Empty;
            HokensyaNo = string.Empty;
            HokenKbnName = string.Empty;
            Reservedetailid = reservedetailid;
            PrintEpsReference = printEpsReference;
            PrescriptionIssueType = prescriptionIssueType;
            UpdateDate = updateDate;
        }

        public ReceptionModel(int hpId, long ptId, long raiinNo, string comment)
        {
            HpId = hpId;
            PtId = ptId;
            RaiinNo = raiinNo;
            Comment = comment;
            YoyakuTime = string.Empty;
            UketukeTime = string.Empty;
            SinStartTime = string.Empty;
            SinEndTime = string.Empty;
            KaikeiTime = string.Empty;
            SName = string.Empty;
            KaSname = string.Empty;
            Houbetu = string.Empty;
            HokensyaNo = string.Empty;
            HokenKbnName = string.Empty;
        }

        public ReceptionModel(int hpId, long ptId, long raiinNo, int sinDate)
        {
            HpId = hpId;
            PtId = ptId;
            RaiinNo = raiinNo;
            SinDate = sinDate;
            YoyakuTime = string.Empty;
            UketukeTime = string.Empty;
            SinStartTime = string.Empty;
            SinEndTime = string.Empty;
            KaikeiTime = string.Empty;
            SName = string.Empty;
            KaSname = string.Empty;
            Houbetu = string.Empty;
            HokensyaNo = string.Empty;
            HokenKbnName = string.Empty;
            Comment = string.Empty;
        }

        public ReceptionModel(long raiinNo, int uketukeId, int kaId, string uketukeTime, string sinStartTime, int status, int yokakuId, int tantoId)
        {
            RaiinNo = raiinNo;
            UketukeId = uketukeId;
            KaId = kaId;
            UketukeTime = uketukeTime;
            SinStartTime = sinStartTime;
            Status = status;
            YoyakuId = yokakuId;
            TantoId = tantoId;
            YoyakuTime = string.Empty;
            SinEndTime = string.Empty;
            KaikeiTime = string.Empty;
            Comment = string.Empty;
            SName = string.Empty;
            KaSname = string.Empty;
            Houbetu = string.Empty;
            HokensyaNo = string.Empty;
            HokenKbnName = string.Empty;
        }

        public ReceptionModel()
        {
            HpId = 0;
            PtId = 0;
            SinDate = 0;
            RaiinNo = 0;
            OyaRaiinNo = 0;
            HokenPid = 0;
            SanteiKbn = 0;
            Status = 0;
            IsYoyaku = 0;
            YoyakuTime = string.Empty;
            YoyakuId = 0;
            UketukeSbt = 0;
            UketukeTime = string.Empty;
            UketukeId = 0;
            UketukeNo = 0;
            SinStartTime = string.Empty;
            SinEndTime = string.Empty;
            KaikeiTime = string.Empty;
            KaikeiId = 0;
            KaId = 0;
            TantoId = 0;
            SyosaisinKbn = 0;
            JikanKbn = 0;
            Comment = string.Empty;
            SName = string.Empty;
            KaSname = string.Empty;
            Houbetu = string.Empty;
            HokensyaNo = string.Empty;
            HokenKbnName = string.Empty;
        }

        public ReceptionModel(int tantoId, int kaId)
        {
            Comment = string.Empty;
            YoyakuTime = string.Empty;
            UketukeTime = string.Empty;
            SinStartTime = string.Empty;
            SinEndTime = string.Empty;
            KaikeiTime = string.Empty;
            KaId = kaId;
            TantoId = tantoId;
            KaSname = string.Empty;
            SName = string.Empty;
            Houbetu = string.Empty;
            HokensyaNo = string.Empty;
            HokenKbnName = string.Empty;
        }

        public ReceptionModel(int hpId, long ptId, int sinDate, int uketukeNo, int status, string kaSname, string sName, string houbetu, string hokensyaNo, int hokenKbn, int hokenId, int hokenPid, long raiinNo, bool isDeleted)
        {
            HpId = hpId;
            PtId = ptId;
            SinDate = sinDate;
            UketukeNo = uketukeNo;
            Status = status;
            KaSname = kaSname;
            SName = sName;
            Houbetu = houbetu;
            HokensyaNo = hokensyaNo;
            HokenKbn = hokenKbn;
            HokenId = hokenId;
            HokenPid = hokenPid;
            RaiinNo = raiinNo;
            IsDeleted = isDeleted;
            Comment = string.Empty;
            YoyakuTime = string.Empty;
            UketukeTime = string.Empty;
            SinStartTime = string.Empty;
            SinEndTime = string.Empty;
            KaikeiTime = string.Empty;
            HokenKbnName = string.Empty;
        }

        public ReceptionModel(ReceptionUpsertItem receptionUpsertItem)
        {
            HpId = receptionUpsertItem.HpId;
            PtId = receptionUpsertItem.PtId;
            SinDate = receptionUpsertItem.SinDate;
            RaiinNo = receptionUpsertItem.RaiinNo;
            OyaRaiinNo = receptionUpsertItem.OyaRaiinNo;
            HokenPid = receptionUpsertItem.HokenPid;
            SanteiKbn = receptionUpsertItem.SanteiKbn;
            Status = receptionUpsertItem.Status;
            IsYoyaku = receptionUpsertItem.IsYoyaku;
            YoyakuTime = receptionUpsertItem.YoyakuTime;
            YoyakuId = receptionUpsertItem.YoyakuId;
            UketukeSbt = receptionUpsertItem.UketukeSbt;
            UketukeTime = receptionUpsertItem.UketukeTime;
            UketukeId = receptionUpsertItem.UketukeId;
            UketukeNo = receptionUpsertItem.UketukeNo;
            SinStartTime = receptionUpsertItem.SinStartTime;
            SinEndTime = receptionUpsertItem.SinEndTime;
            KaikeiTime = receptionUpsertItem.KaikeiTime;
            KaikeiId = receptionUpsertItem.KaikeiId;
            KaId = receptionUpsertItem.KaId;
            TantoId = receptionUpsertItem.TantoId;
            SyosaisinKbn = receptionUpsertItem.SyosaisinKbn;
            JikanKbn = receptionUpsertItem.JikanKbn;
            Comment = receptionUpsertItem.Comment;
            TreatmentDepartmentId = receptionUpsertItem.TreatmentDepartmentId;
            PrintEpsReference = receptionUpsertItem.PrintEpsReference;
            PrescriptionIssueType = receptionUpsertItem.PrescriptionIssueType;
            SName = string.Empty;
            KaSname = string.Empty;
            Houbetu = string.Empty;
            HokensyaNo = string.Empty;
            HokenKbnName = string.Empty;
            OnlineConfirmationHistoryId = receptionUpsertItem.OnlineConfirmationHistoryId;
        }

        public ReceptionModel(long ptId, int sinDate, long raiinNo, int tantoId, int kaId, string sName, string kaSname, string hokenKbnName)
        {
            PtId = ptId;
            SinDate = sinDate;
            RaiinNo = raiinNo;
            SName = sName;
            KaSname = kaSname;
            TantoId = tantoId;
            KaId = kaId;
            HokenKbnName = hokenKbnName;
            Houbetu = string.Empty;
            HokensyaNo = string.Empty;
            YoyakuTime = string.Empty;
            UketukeTime = string.Empty;
            SinStartTime = string.Empty;
            SinEndTime = string.Empty;
            KaikeiTime = string.Empty;
            Comment = string.Empty;
        }

        public ReceptionModel(int hpId, long ptId, string ptName, string ptKanaName,
                            int sex, string memo, int prescriptionIssueType, int printEpsReference,
                            int kaId, int tantoId, int treatmentDepartmentId, int sinDate, string yoyakuTime,
                            string yoyakuEndTime, int isYoyaku, int sinryoKbn, int hokenPid,
                            int confirmationType, string infoConsFlg, string prescriptionDeliInfo,
                            int status, string tantoName, string kaSname, string departmentSName, int hokenSbtCd,
                            string hokenHoubetu, List<KohiInfModel> kohi, CombineStatusEnum canCombine, long raiinNo, 
                            bool hasMessage, List<Label> labels, long karteEditionRaiinNo, int reservedetailid,
                            PortalCustomerPharmacyModel pharmacyModel, int prescriptionReceiveMethod, string raiinMemo, int syosaisinKbn, int jikanKbn, OnlineConfirmationHistoryModel onlineConfirmationHistory,
                            bool isLinkCard, bool canEditPrescription, int hokenKbn = 0, int meetingId = 0)
        {
            HpId = hpId;
            PtId = ptId;
            SName = ptName;
            KaSname = ptKanaName;
            Sex = sex;
            PtMemo = memo;
            Status = status;
            PrescriptionIssueType = prescriptionIssueType;
            PrintEpsReference = printEpsReference;
            KaId = kaId;
            KaSname = kaSname;
            TantoId = tantoId;
            TantoName = tantoName;
            TreatmentDepartmentId = treatmentDepartmentId;
            DepartmentSName = departmentSName;
            SinDate = sinDate;
            YoyakuTime = yoyakuTime;
            YoyakuEndTime = yoyakuEndTime;
            IsYoyaku = isYoyaku;
            SinryoKbn = sinryoKbn;
            HokenPid = hokenPid;
            ConfirmationType = confirmationType;
            InfoConsFlg = infoConsFlg;
            PrescriptionDeliInfo = prescriptionDeliInfo;
            HokenSbtCd = hokenSbtCd;
            HokenHoubetu = hokenHoubetu;
            Kohi = kohi;
            HokenName = GetHokenName.CreateHokenName(HokenHoubetu, HokenSbtCd, Kohi, hokenKbn);
            CanCombine = canCombine;
            RaiinNo = raiinNo;
            HasMessage = hasMessage;
            PortalCustomerPharmacy = pharmacyModel;
            PrescriptionReceiveMethod = prescriptionReceiveMethod;
            Labels = labels;
            KateEditionRaiinNo = karteEditionRaiinNo;
            HokenKbnName = string.Empty;
            Reservedetailid = reservedetailid;
            Comment = raiinMemo;
            SyosaisinKbn = syosaisinKbn;
            JikanKbn = jikanKbn;
            OnlineConfirmationHistory = onlineConfirmationHistory;
            IsLinkCard = isLinkCard;
            CanEditPrescription = canEditPrescription;
            HokenKbn = hokenKbn;
            MeetingId = meetingId;
        }

        public ReceptionModel(int hpId, long ptId, int sinDate, int status, int uketukeId, int onlineConfirmationId, int hokenPid, int confirmationType, string infoConsFlg, int prescriptionIssueType)
        {
            HpId = hpId;
            PtId = ptId;
            SinDate = sinDate;
            Status = status;
            OnlineConfirmationHistoryId = onlineConfirmationId;
            HokenPid = hokenPid;
            InfoConsFlg = infoConsFlg;
            ConfirmationType = confirmationType;
            PrescriptionIssueType = prescriptionIssueType;
        }

        public ReceptionDto ToDto()
        {
            return new ReceptionDto
                (
                    HpId,
                    PtId,
                    SinDate,
                    RaiinNo,
                    OyaRaiinNo,
                    HokenPid,
                    SanteiKbn,
                    Status,
                    IsYoyaku,
                    YoyakuTime,
                    YoyakuId,
                    UketukeSbt,
                    UketukeTime,
                    UketukeId,
                    UketukeNo,
                    SinStartTime,
                    SinEndTime,
                    KaikeiTime,
                    KaikeiId,
                    KaId,
                    TantoId,
                    SyosaisinKbn,
                    JikanKbn,
                    Comment
                );
        }

        public ReceptionModel ChangeUketukeNo(int uketukeNo)
        {
            UketukeNo = uketukeNo;
            return this;
        }
    }
}
