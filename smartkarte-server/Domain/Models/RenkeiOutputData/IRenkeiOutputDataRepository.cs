﻿using Domain.Common;

namespace Domain.Models.RenkeiOutputData
{
    public interface IRenkeiOutputDataRepository : IRepositoryBase
    {
        RenkeiOutputDataModel GetRenkeiOutputDataModel(int hpId, int renkeiType, long ptId, int sinDate, long raiinNo, string centerCd);

        void SaveRenkeiOutputData(int hpId, int renkeiType, long ptId, int sinDate, long raiinNo, string centerCd, string data, int userId);
    }
}
