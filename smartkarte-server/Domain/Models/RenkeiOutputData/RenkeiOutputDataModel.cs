﻿namespace Domain.Models.RenkeiOutputData
{
    public class RenkeiOutputDataModel
    {
        public RenkeiOutputDataModel()
        {
        }

        public RenkeiOutputDataModel(int hpId, int renkeiType, long ptId, int sinDate, long raiinNo, string? data)
        {
            HpId = hpId;
            RenkeiType = renkeiType;
            PtId = ptId;
            SinDate = sinDate;
            RaiinNo = raiinNo;
            Data = data;
        }

        public int HpId { get; private set; }

        public int RenkeiType { get; private set; }

        public long PtId { get; private set; }

        public int SinDate { get; private set; }

        public long RaiinNo { get; private set; }

        public string? Data { get; private set; }

    }
}
