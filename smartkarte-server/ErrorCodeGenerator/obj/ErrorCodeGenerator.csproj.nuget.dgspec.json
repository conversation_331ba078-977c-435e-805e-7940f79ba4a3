{"format": 1, "restore": {"/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/ErrorCodeGenerator/ErrorCodeGenerator.csproj": {}}, "projects": {"/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/ErrorCodeGenerator/ErrorCodeGenerator.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/ErrorCodeGenerator/ErrorCodeGenerator.csproj", "projectName": "ErrorCodeGenerator", "projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/ErrorCodeGenerator/ErrorCodeGenerator.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/ErrorCodeGenerator/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.404/RuntimeIdentifierGraph.json"}}}}}