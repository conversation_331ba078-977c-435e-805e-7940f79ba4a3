{"version": 3, "targets": {"net6.0": {"AWSSDK.Core/3.7.12.24": {"type": "package", "compile": {"lib/netcoreapp3.1/AWSSDK.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/AWSSDK.Core.dll": {"related": ".pdb;.xml"}}}, "AWSSDK.S3/3.7.9.42": {"type": "package", "dependencies": {"AWSSDK.Core": "[3.7.12.24, 4.0.0)"}, "compile": {"lib/netcoreapp3.1/AWSSDK.S3.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/AWSSDK.S3.dll": {"related": ".pdb;.xml"}}}, "GraphQL.Client/6.0.0": {"type": "package", "dependencies": {"GraphQL.Client.Abstractions": "6.0.0", "GraphQL.Client.Abstractions.Websocket": "6.0.0", "System.Reactive": "5.0.0"}, "compile": {"lib/netstandard2.0/GraphQL.Client.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/GraphQL.Client.dll": {"related": ".xml"}}}, "GraphQL.Client.Abstractions/6.0.0": {"type": "package", "dependencies": {"GraphQL.Primitives": "6.0.0"}, "compile": {"lib/netstandard2.0/GraphQL.Client.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/GraphQL.Client.Abstractions.dll": {"related": ".xml"}}}, "GraphQL.Client.Abstractions.Websocket/6.0.0": {"type": "package", "dependencies": {"GraphQL.Client.Abstractions": "6.0.0"}, "compile": {"lib/netstandard2.0/GraphQL.Client.Abstractions.Websocket.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/GraphQL.Client.Abstractions.Websocket.dll": {"related": ".xml"}}}, "GraphQL.Client.Serializer.Newtonsoft/6.0.0": {"type": "package", "dependencies": {"GraphQL.Client.Abstractions.Websocket": "6.0.0", "Newtonsoft.Json": "13.0.2"}, "compile": {"lib/netstandard2.0/GraphQL.Client.Serializer.Newtonsoft.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/GraphQL.Client.Serializer.Newtonsoft.dll": {"related": ".xml"}}}, "GraphQL.Primitives/6.0.0": {"type": "package", "compile": {"lib/netstandard2.0/GraphQL.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/GraphQL.Primitives.dll": {"related": ".xml"}}}, "Kana.NET/1.0.6": {"type": "package", "compile": {"lib/net6.0/Umayadia.Kana.dll": {}}, "runtime": {"lib/net6.0/Umayadia.Kana.dll": {}}}, "Konscious.Security.Cryptography.Argon2/1.3.0": {"type": "package", "dependencies": {"Konscious.Security.Cryptography.Blake2": "1.1.0", "System.Memory": "4.5.4"}, "compile": {"lib/net6.0/Konscious.Security.Cryptography.Argon2.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Konscious.Security.Cryptography.Argon2.dll": {"related": ".xml"}}}, "Konscious.Security.Cryptography.Blake2/1.1.0": {"type": "package", "compile": {"lib/net6.0/Konscious.Security.Cryptography.Blake2.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Konscious.Security.Cryptography.Blake2.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authorization/2.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authorization.Policy/2.2.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Authorization": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.Policy.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authorization.Policy.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http/2.2.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0", "System.Buffers": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Mvc.Abstractions/2.2.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Mvc.Core/2.2.5": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.Authorization.Policy": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Mvc.Abstractions": "2.2.0", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "2.2.0", "Microsoft.AspNetCore.Routing": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection": "2.2.0", "Microsoft.Extensions.DependencyModel": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "System.Diagnostics.DiagnosticSource": "4.5.0", "System.Threading.Tasks.Extensions": "4.5.1"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Core.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Routing/2.2.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}, "compile": {"lib/netcoreapp2.2/Microsoft.AspNetCore.Routing.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.2/Microsoft.AspNetCore.Routing.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Routing.Abstractions/2.2.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"type": "package", "dependencies": {"Microsoft.Net.Http.Headers": "2.2.0", "System.Text.Encodings.Web": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll": {"related": ".xml"}}}, "Microsoft.CSharp/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "Microsoft.Diagnostics.NETCore.Client/0.2.510501": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging": "6.0.0"}, "compile": {"lib/net6.0/Microsoft.Diagnostics.NETCore.Client.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Diagnostics.NETCore.Client.dll": {"related": ".xml"}}}, "Microsoft.DotNet.PlatformAbstractions/2.1.0": {"type": "package", "dependencies": {"System.AppContext": "4.1.0", "System.Collections": "4.0.11", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.InteropServices.RuntimeInformation": "4.0.0"}, "compile": {"lib/netstandard1.3/Microsoft.DotNet.PlatformAbstractions.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.DotNet.PlatformAbstractions.dll": {}}}, "Microsoft.EntityFrameworkCore/7.0.1": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "7.0.1", "Microsoft.EntityFrameworkCore.Analyzers": "7.0.1", "Microsoft.Extensions.Caching.Memory": "7.0.0", "Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.EntityFrameworkCore.props": {}}}, "Microsoft.EntityFrameworkCore.Abstractions/7.0.1": {"type": "package", "compile": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Analyzers/7.0.1": {"type": "package", "compile": {"lib/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/_._": {}}}, "Microsoft.EntityFrameworkCore.DynamicLinq/6.2.20": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "6.0.0", "System.Linq.Dynamic.Core": "1.2.20"}, "compile": {"lib/net6.0/Microsoft.EntityFrameworkCore.DynamicLinq.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.DynamicLinq.dll": {"related": ".pdb;.xml"}}}, "Microsoft.EntityFrameworkCore.Relational/7.0.0": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyModel/2.1.0": {"type": "package", "dependencies": {"Microsoft.DotNet.PlatformAbstractions": "2.1.0", "Newtonsoft.Json": "9.0.1", "System.Diagnostics.Debug": "4.0.11", "System.Dynamic.Runtime": "4.0.11", "System.Linq": "4.1.0"}, "compile": {"lib/netstandard1.6/Microsoft.Extensions.DependencyModel.dll": {}}, "runtime": {"lib/netstandard1.6/Microsoft.Extensions.DependencyModel.dll": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/2.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Hosting.Abstractions/2.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.ObjectPool/9.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Options/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Primitives/7.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.IdentityModel.Abstractions/6.34.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.34.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "6.34.0", "System.Text.Encoding": "4.3.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2"}, "compile": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Logging/6.34.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "6.34.0"}, "compile": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Tokens/6.34.0": {"type": "package", "dependencies": {"Microsoft.CSharp": "4.5.0", "Microsoft.IdentityModel.Logging": "6.34.0", "System.Security.Cryptography.Cng": "4.5.0"}, "compile": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}}, "Microsoft.Net.Http.Headers/2.2.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "2.2.0", "System.Buffers": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {"related": ".xml"}}}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "MyNihongo.KanaConverter/1.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.ObjectPool": "9.0.0"}, "compile": {"lib/netstandard2.0/MyNihongo.KanaConverter.dll": {}}, "runtime": {"lib/netstandard2.0/MyNihongo.KanaConverter.dll": {}}}, "Newtonsoft.Json/13.0.2": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Npgsql/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "6.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/Npgsql.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Npgsql.dll": {"related": ".xml"}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/7.0.0": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "[7.0.0, 8.0.0)", "Microsoft.EntityFrameworkCore.Abstractions": "[7.0.0, 8.0.0)", "Microsoft.EntityFrameworkCore.Relational": "[7.0.0, 8.0.0)", "Npgsql": "7.0.0"}, "compile": {"lib/net6.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"related": ".xml"}}}, "Pipelines.Sockets.Unofficial/2.2.2": {"type": "package", "dependencies": {"System.IO.Pipelines": "5.0.1"}, "compile": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"related": ".xml"}}}, "runtime.native.System/4.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Sentry/4.13.0": {"type": "package", "compile": {"lib/net6.0/Sentry.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Sentry.dll": {"related": ".xml"}}, "build": {"buildTransitive/Sentry.targets": {}}}, "Sentry.Profiling/4.13.0": {"type": "package", "dependencies": {"Microsoft.Diagnostics.NETCore.Client": "0.2.510501", "Sentry": "4.13.0"}, "compile": {"lib/net6.0/Microsoft.Diagnostics.FastSerialization.dll": {"related": ".pdb"}, "lib/net6.0/Microsoft.Diagnostics.Tracing.TraceEvent.dll": {"related": ".pdb"}, "lib/net6.0/Sentry.Profiling.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Diagnostics.FastSerialization.dll": {"related": ".pdb"}, "lib/net6.0/Microsoft.Diagnostics.Tracing.TraceEvent.dll": {"related": ".pdb"}, "lib/net6.0/Sentry.Profiling.dll": {"related": ".xml"}}, "build": {"buildTransitive/Sentry.Profiling.targets": {}}}, "StackExchange.Redis/2.6.111": {"type": "package", "dependencies": {"Pipelines.Sockets.Unofficial": "2.2.2"}, "compile": {"lib/net5.0/StackExchange.Redis.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/StackExchange.Redis.dll": {"related": ".xml"}}}, "System.AppContext/4.1.0": {"type": "package", "dependencies": {"System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.6/System.AppContext.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.AppContext.dll": {}}}, "System.Buffers/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Collections/4.0.11": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Collections.dll": {"related": ".xml"}}}, "System.Diagnostics.Debug/4.0.11": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Diagnostics.Debug.dll": {"related": ".xml"}}}, "System.Diagnostics.DiagnosticSource/4.5.0": {"type": "package", "compile": {"lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}}, "System.Dynamic.Runtime/4.0.11": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Emit": "4.0.1", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Dynamic.Runtime.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Dynamic.Runtime.dll": {}}}, "System.Globalization/4.0.11": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.IdentityModel.Tokens.Jwt/6.34.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.34.0", "Microsoft.IdentityModel.Tokens": "6.34.0"}, "compile": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}}, "System.IO/4.1.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Threading.Tasks": "4.0.11"}, "compile": {"ref/netstandard1.5/System.IO.dll": {"related": ".xml"}}}, "System.IO.FileSystem/4.0.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.IO": "4.1.0", "System.IO.FileSystem.Primitives": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Text.Encoding": "4.0.11", "System.Threading.Tasks": "4.0.11"}, "compile": {"ref/netstandard1.3/System.IO.FileSystem.dll": {"related": ".xml"}}}, "System.IO.FileSystem.Primitives/4.0.1": {"type": "package", "dependencies": {"System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/System.IO.FileSystem.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.IO.FileSystem.Primitives.dll": {}}}, "System.IO.Pipelines/5.0.1": {"type": "package", "compile": {"ref/netcoreapp2.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/System.IO.Pipelines.dll": {"related": ".xml"}}}, "System.Linq/4.1.0": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0"}, "compile": {"ref/netstandard1.6/System.Linq.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.Linq.dll": {}}}, "System.Linq.Dynamic.Core/1.2.20": {"type": "package", "compile": {"lib/net6.0/System.Linq.Dynamic.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/System.Linq.Dynamic.Core.dll": {"related": ".pdb;.xml"}}}, "System.Linq.Expressions/4.1.0": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Linq": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Emit": "4.0.1", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Emit.Lightweight": "4.0.1", "System.Reflection.Extensions": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.6/System.Linq.Expressions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.Linq.Expressions.dll": {}}}, "System.Memory/4.5.4": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.ObjectModel/4.0.12": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.3/System.ObjectModel.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.ObjectModel.dll": {}}}, "System.Reactive/5.0.0": {"type": "package", "compile": {"lib/net5.0/System.Reactive.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/System.Reactive.dll": {"related": ".xml"}}, "build": {"buildTransitive/net5.0/_._": {}}}, "System.Reflection/4.1.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.IO": "4.1.0", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.5/System.Reflection.dll": {"related": ".xml"}}}, "System.Reflection.Emit/4.0.1": {"type": "package", "dependencies": {"System.IO": "4.1.0", "System.Reflection": "4.1.0", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.1/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.dll": {}}}, "System.Reflection.Emit.ILGeneration/4.0.1": {"type": "package", "dependencies": {"System.Reflection": "4.1.0", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.ILGeneration.dll": {}}}, "System.Reflection.Emit.Lightweight/4.0.1": {"type": "package", "dependencies": {"System.Reflection": "4.1.0", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll": {}}}, "System.Reflection.Extensions/4.0.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Reflection": "4.1.0", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}}, "System.Reflection.Primitives/4.0.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Primitives.dll": {"related": ".xml"}}}, "System.Reflection.TypeExtensions/4.1.0": {"type": "package", "dependencies": {"System.Reflection": "4.1.0", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.5/System.Reflection.TypeExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.5/System.Reflection.TypeExtensions.dll": {}}}, "System.Resources.ResourceManager/4.0.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Globalization": "4.0.11", "System.Reflection": "4.1.0", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}}, "System.Runtime/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"ref/netstandard1.5/System.Runtime.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Runtime.Extensions/4.1.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.5/System.Runtime.Extensions.dll": {"related": ".xml"}}}, "System.Runtime.Handles/4.0.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Runtime.Handles.dll": {"related": ".xml"}}}, "System.Runtime.InteropServices/4.1.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Reflection": "4.1.0", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Handles": "4.0.1"}, "compile": {"ref/netstandard1.5/System.Runtime.InteropServices.dll": {"related": ".xml"}}}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Threading": "4.0.11", "runtime.native.System": "4.0.0"}, "compile": {"ref/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Cng/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.1/System.Security.Cryptography.Cng.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll": {}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encoding/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.dll": {"related": ".xml"}}}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encodings.Web/4.7.2": {"type": "package", "compile": {"lib/netstandard2.1/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/System.Text.Encodings.Web.dll": {"related": ".xml"}}}, "System.Text.Json/4.7.2": {"type": "package", "compile": {"lib/netcoreapp3.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/System.Text.Json.dll": {"related": ".xml"}}}, "System.Threading/4.0.11": {"type": "package", "dependencies": {"System.Runtime": "4.1.0", "System.Threading.Tasks": "4.0.11"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Threading.dll": {}}}, "System.Threading.Tasks/4.0.11": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Threading.Tasks.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.1": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "Domain/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"AWSSDK.S3": "3.7.9.42", "Entity": "1.0.0", "Helper": "1.0.0", "Microsoft.AspNetCore.Http.Features": "2.2.0"}, "compile": {"bin/placeholder/Domain.dll": {}}, "runtime": {"bin/placeholder/Domain.dll": {}}}, "Entity/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"Helper": "1.0.0", "Microsoft.EntityFrameworkCore.Abstractions": "6.0.6"}, "compile": {"bin/placeholder/Entity.dll": {}}, "runtime": {"bin/placeholder/Entity.dll": {}}}, "ErrorCodeGenerator/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"Newtonsoft.Json": "13.0.2"}, "compile": {"bin/placeholder/ErrorCodeGenerator.dll": {}}, "runtime": {"bin/placeholder/ErrorCodeGenerator.dll": {}}}, "Helper/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"ErrorCodeGenerator": "1.0.0", "Kana.NET": "1.0.6", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "MyNihongo.KanaConverter": "1.0.3", "StackExchange.Redis": "2.6.111", "System.Text.Encoding.CodePages": "6.0.0"}, "compile": {"bin/placeholder/Helper.dll": {}}, "runtime": {"bin/placeholder/Helper.dll": {}}}, "Infrastructure/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"AWSSDK.S3": "3.7.9.42", "Domain": "1.0.0", "GraphQL.Client": "6.0.0", "GraphQL.Client.Serializer.Newtonsoft": "6.0.0", "Konscious.Security.Cryptography.Argon2": "1.3.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.Mvc.Abstractions": "2.2.0", "Microsoft.AspNetCore.Mvc.Core": "2.2.5", "Microsoft.EntityFrameworkCore.DynamicLinq": "6.2.20", "PostgreDataContext": "1.0.0", "Sentry": "4.13.0", "Sentry.Profiling": "4.13.0", "System.IdentityModel.Tokens.Jwt": "6.34.0"}, "compile": {"bin/placeholder/Infrastructure.dll": {}}, "runtime": {"bin/placeholder/Infrastructure.dll": {}}}, "PostgreDataContext/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"Entity": "1.0.0", "Microsoft.EntityFrameworkCore": "7.0.1", "Npgsql.EntityFrameworkCore.PostgreSQL": "7.0.0"}, "compile": {"bin/placeholder/PostgreDataContext.dll": {}}, "runtime": {"bin/placeholder/PostgreDataContext.dll": {}}}}}, "libraries": {"AWSSDK.Core/3.7.12.24": {"sha512": "uPAZ3a5Wu4k4Ml+TBAcOumZPxMpoHUHai0PdS3iLX0XPsDqAJPYviAIc1QXi2rVSDgqC2uVBxiEVSNYAgzoxbA==", "type": "package", "path": "awssdk.core/3.7.12.24", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "awssdk.core.3.7.12.24.nupkg.sha512", "awssdk.core.nuspec", "lib/net35/AWSSDK.Core.dll", "lib/net35/AWSSDK.Core.pdb", "lib/net35/AWSSDK.Core.xml", "lib/net45/AWSSDK.Core.dll", "lib/net45/AWSSDK.Core.pdb", "lib/net45/AWSSDK.Core.xml", "lib/netcoreapp3.1/AWSSDK.Core.dll", "lib/netcoreapp3.1/AWSSDK.Core.pdb", "lib/netcoreapp3.1/AWSSDK.Core.xml", "lib/netstandard2.0/AWSSDK.Core.dll", "lib/netstandard2.0/AWSSDK.Core.pdb", "lib/netstandard2.0/AWSSDK.Core.xml", "tools/account-management.ps1"]}, "AWSSDK.S3/3.7.9.42": {"sha512": "JNpSmndHm0uoqLQ3dypHPaP5p8+Yz3zZrr6MsA9+YhDy17L7I66nUYrmAhaXkgTSHlD/5uTAnvC9D6ZZO9QhUA==", "type": "package", "path": "awssdk.s3/3.7.9.42", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/AWSSDK.S3.CodeAnalysis.dll", "awssdk.s3.3.7.9.42.nupkg.sha512", "awssdk.s3.nuspec", "lib/net35/AWSSDK.S3.dll", "lib/net35/AWSSDK.S3.pdb", "lib/net35/AWSSDK.S3.xml", "lib/net45/AWSSDK.S3.dll", "lib/net45/AWSSDK.S3.pdb", "lib/net45/AWSSDK.S3.xml", "lib/netcoreapp3.1/AWSSDK.S3.dll", "lib/netcoreapp3.1/AWSSDK.S3.pdb", "lib/netcoreapp3.1/AWSSDK.S3.xml", "lib/netstandard2.0/AWSSDK.S3.dll", "lib/netstandard2.0/AWSSDK.S3.pdb", "lib/netstandard2.0/AWSSDK.S3.xml", "tools/install.ps1", "tools/uninstall.ps1"]}, "GraphQL.Client/6.0.0": {"sha512": "8yPNBbuVBpTptivyAlak4GZvbwbUcjeQTL4vN1HKHRuOykZ4r7l5fcLS6vpyPyLn0x8FsL31xbOIKyxbmR9rbA==", "type": "package", "path": "graphql.client/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "graphql.client.6.0.0.nupkg.sha512", "graphql.client.nuspec", "lib/net461/GraphQL.Client.dll", "lib/net461/GraphQL.Client.xml", "lib/netstandard2.0/GraphQL.Client.dll", "lib/netstandard2.0/GraphQL.Client.xml", "logo.64x64.png"]}, "GraphQL.Client.Abstractions/6.0.0": {"sha512": "h7uzWFORHZ+CCjwr/ThAyXMr0DPpzEANDa4Uo54wqCQ+j7qUKwqYTgOrb1W40sqbvNaZm9v/X7It31SUw0maHA==", "type": "package", "path": "graphql.client.abstractions/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "graphql.client.abstractions.6.0.0.nupkg.sha512", "graphql.client.abstractions.nuspec", "lib/netstandard2.0/GraphQL.Client.Abstractions.dll", "lib/netstandard2.0/GraphQL.Client.Abstractions.xml", "logo.64x64.png"]}, "GraphQL.Client.Abstractions.Websocket/6.0.0": {"sha512": "Nr9bPf8gIOvLuXpqEpqr9z9jslYFJOvd0feHth3/kPqeR3uMbjF5pjiwh4jxyMcxHdr8Pb6QiXkV3hsSyt0v7A==", "type": "package", "path": "graphql.client.abstractions.websocket/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "graphql.client.abstractions.websocket.6.0.0.nupkg.sha512", "graphql.client.abstractions.websocket.nuspec", "lib/netstandard2.0/GraphQL.Client.Abstractions.Websocket.dll", "lib/netstandard2.0/GraphQL.Client.Abstractions.Websocket.xml", "logo.64x64.png"]}, "GraphQL.Client.Serializer.Newtonsoft/6.0.0": {"sha512": "6AFZjq4MZhR2e1ZZOi++NXMTq0y3Q+sT3S/UAIcLRJ7H6lQ8nxoAbzrlToui/m2j5gO9UL4Pmc+CdvM/H+t8Xg==", "type": "package", "path": "graphql.client.serializer.newtonsoft/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "graphql.client.serializer.newtonsoft.6.0.0.nupkg.sha512", "graphql.client.serializer.newtonsoft.nuspec", "lib/netstandard2.0/GraphQL.Client.Serializer.Newtonsoft.dll", "lib/netstandard2.0/GraphQL.Client.Serializer.Newtonsoft.xml", "logo.64x64.png"]}, "GraphQL.Primitives/6.0.0": {"sha512": "yg72rrYDapfsIUrul7aF6wwNnTJBOFvuA9VdDTQpPa8AlAriHbufeXYLBcodKjfUdkCnaiggX1U/nEP08Zb5GA==", "type": "package", "path": "graphql.primitives/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "graphql.primitives.6.0.0.nupkg.sha512", "graphql.primitives.nuspec", "lib/netstandard2.0/GraphQL.Primitives.dll", "lib/netstandard2.0/GraphQL.Primitives.xml", "logo.64x64.png"]}, "Kana.NET/1.0.6": {"sha512": "xc9teFuWQJ1h0YddJZxVWNTfvNPK648Wed4k8JYViNZN3txcV/5RXW/13nZsKjY/qndFSzJEta7mVr4jsVyXgg==", "type": "package", "path": "kana.net/1.0.6", "files": [".nupkg.metadata", ".signature.p7s", "kana.net.1.0.6.nupkg.sha512", "kana.net.nuspec", "lib/net5.0/Umayadia.Kana.dll", "lib/net6.0/Umayadia.Kana.dll", "lib/netcoreapp3.1/Umayadia.Kana.dll", "umayadia128.png"]}, "Konscious.Security.Cryptography.Argon2/1.3.0": {"sha512": "/pNPlNmjx7r7CGvw0WqO8Rk5vHHyUuzAWhB/PRKLZ2cmhQXoM6TYUooJcuES9zE9D+6yTOl+4LmFV2nKtgDyFg==", "type": "package", "path": "konscious.security.cryptography.argon2/1.3.0", "files": [".nupkg.metadata", ".signature.p7s", "konscious.security.cryptography.argon2.1.3.0.nupkg.sha512", "konscious.security.cryptography.argon2.nuspec", "lib/net46/Konscious.Security.Cryptography.Argon2.dll", "lib/net46/Konscious.Security.Cryptography.Argon2.xml", "lib/net6.0/Konscious.Security.Cryptography.Argon2.dll", "lib/net6.0/Konscious.Security.Cryptography.Argon2.xml", "lib/netstandard1.3/Konscious.Security.Cryptography.Argon2.dll", "lib/netstandard1.3/Konscious.Security.Cryptography.Argon2.xml"]}, "Konscious.Security.Cryptography.Blake2/1.1.0": {"sha512": "enWOzNAlBjFcKoiBNVVLsa1ZzOciExznu+sUURE8dx322GI1LzrBxzEuZqjeOoK9oo9RubLtg96/7Y4JtUk9zA==", "type": "package", "path": "konscious.security.cryptography.blake2/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "konscious.security.cryptography.blake2.1.1.0.nupkg.sha512", "konscious.security.cryptography.blake2.nuspec", "lib/net46/Konscious.Security.Cryptography.Blake2.dll", "lib/net46/Konscious.Security.Cryptography.Blake2.xml", "lib/net6.0/Konscious.Security.Cryptography.Blake2.dll", "lib/net6.0/Konscious.Security.Cryptography.Blake2.xml", "lib/netstandard1.3/Konscious.Security.Cryptography.Blake2.dll", "lib/netstandard1.3/Konscious.Security.Cryptography.Blake2.xml"]}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"sha512": "VloMLDJMf3n/9ic5lCBOa42IBYJgyB1JhzLsL68Zqg+2bEPWfGBj/xCJy/LrKTArN0coOcZp3wyVTZlx0y9pHQ==", "type": "package", "path": "microsoft.aspnetcore.authentication.abstractions/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.xml", "microsoft.aspnetcore.authentication.abstractions.2.2.0.nupkg.sha512", "microsoft.aspnetcore.authentication.abstractions.nuspec"]}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"sha512": "XlVJzJ5wPOYW+Y0J6Q/LVTEyfS4ssLXmt60T0SPP+D8abVhBTl+cgw2gDHlyKYIkcJg7btMVh383NDkMVqD/fg==", "type": "package", "path": "microsoft.aspnetcore.authentication.core/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.xml", "microsoft.aspnetcore.authentication.core.2.2.0.nupkg.sha512", "microsoft.aspnetcore.authentication.core.nuspec"]}, "Microsoft.AspNetCore.Authorization/2.2.0": {"sha512": "/L0W8H3jMYWyaeA9gBJqS/tSWBegP9aaTM0mjRhxTttBY9z4RVDRYJ2CwPAmAXIuPr3r1sOw+CS8jFVRGHRezQ==", "type": "package", "path": "microsoft.aspnetcore.authorization/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.xml", "microsoft.aspnetcore.authorization.2.2.0.nupkg.sha512", "microsoft.aspnetcore.authorization.nuspec"]}, "Microsoft.AspNetCore.Authorization.Policy/2.2.0": {"sha512": "aJCo6niDRKuNg2uS2WMEmhJTooQUGARhV2ENQ2tO5443zVHUo19MSgrgGo9FIrfD+4yKPF8Q+FF33WkWfPbyKw==", "type": "package", "path": "microsoft.aspnetcore.authorization.policy/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.Policy.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.Policy.xml", "microsoft.aspnetcore.authorization.policy.2.2.0.nupkg.sha512", "microsoft.aspnetcore.authorization.policy.nuspec"]}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"sha512": "ubycklv+ZY7Kutdwuy1W4upWcZ6VFR8WUXU7l7B2+mvbDBBPAcfpi+E+Y5GFe+Q157YfA3C49D2GCjAZc7Mobw==", "type": "package", "path": "microsoft.aspnetcore.hosting.abstractions/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.xml", "microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512", "microsoft.aspnetcore.hosting.abstractions.nuspec"]}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"sha512": "1PMijw8RMtuQF60SsD/JlKtVfvh4NORAhF4wjysdABhlhTrYmtgssqyncR0Stq5vqtjplZcj6kbT4LRTglt9IQ==", "type": "package", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.xml", "microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512", "microsoft.aspnetcore.hosting.server.abstractions.nuspec"]}, "Microsoft.AspNetCore.Http/2.2.0": {"sha512": "YogBSMotWPAS/X5967pZ+yyWPQkThxhmzAwyCHCSSldzYBkW5W5d6oPfBaPqQOnSHYTpSOSOkpZoAce0vwb6+A==", "type": "package", "path": "microsoft.aspnetcore.http/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.xml", "microsoft.aspnetcore.http.2.2.0.nupkg.sha512", "microsoft.aspnetcore.http.nuspec"]}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"sha512": "Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "type": "package", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.xml", "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512", "microsoft.aspnetcore.http.abstractions.nuspec"]}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"sha512": "2DgZ9rWrJtuR7RYiew01nGRzuQBDaGHGmK56Rk54vsLLsCdzuFUPqbDTJCS1qJQWTbmbIQ9wGIOjpxA1t0l7/w==", "type": "package", "path": "microsoft.aspnetcore.http.extensions/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.xml", "microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512", "microsoft.aspnetcore.http.extensions.nuspec"]}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"sha512": "ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "type": "package", "path": "microsoft.aspnetcore.http.features/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.xml", "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512", "microsoft.aspnetcore.http.features.nuspec"]}, "Microsoft.AspNetCore.Mvc.Abstractions/2.2.0": {"sha512": "ET6uZpfVbGR1NjCuLaLy197cQ3qZUjzl7EG5SL4GfJH/c9KRE89MMBrQegqWsh0w1iRUB/zQaK0anAjxa/pz4g==", "type": "package", "path": "microsoft.aspnetcore.mvc.abstractions/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Abstractions.xml", "microsoft.aspnetcore.mvc.abstractions.2.2.0.nupkg.sha512", "microsoft.aspnetcore.mvc.abstractions.nuspec"]}, "Microsoft.AspNetCore.Mvc.Core/2.2.5": {"sha512": "/8sr8ixIUD57UFwUntha9bOwex7/AkZfdk1f9oNJG1Ek7p/uuKVa7fuHmYZpQOf35Oxrt+2Ku4WPwMSbNxOuWg==", "type": "package", "path": "microsoft.aspnetcore.mvc.core/2.2.5", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Core.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Core.xml", "microsoft.aspnetcore.mvc.core.2.2.5.nupkg.sha512", "microsoft.aspnetcore.mvc.core.nuspec"]}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.2.0": {"sha512": "CIHWEKrHzZfFp7t57UXsueiSA/raku56TgRYauV/W1+KAQq6vevz60zjEKaazt3BI76zwMz3B4jGWnCwd8kwQw==", "type": "package", "path": "microsoft.aspnetcore.responsecaching.abstractions/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.ResponseCaching.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.ResponseCaching.Abstractions.xml", "microsoft.aspnetcore.responsecaching.abstractions.2.2.0.nupkg.sha512", "microsoft.aspnetcore.responsecaching.abstractions.nuspec"]}, "Microsoft.AspNetCore.Routing/2.2.0": {"sha512": "jAhDBy0wryOnMhhZTtT9z63gJbvCzFuLm8yC6pHzuVu9ZD1dzg0ltxIwT4cfwuNkIL/TixdKsm3vpVOpG8euWQ==", "type": "package", "path": "microsoft.aspnetcore.routing/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netcoreapp2.2/Microsoft.AspNetCore.Routing.dll", "lib/netcoreapp2.2/Microsoft.AspNetCore.Routing.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Routing.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Routing.xml", "microsoft.aspnetcore.routing.2.2.0.nupkg.sha512", "microsoft.aspnetcore.routing.nuspec"]}, "Microsoft.AspNetCore.Routing.Abstractions/2.2.0": {"sha512": "lRRaPN7jDlUCVCp9i0W+PB0trFaKB0bgMJD7hEJS9Uo4R9MXaMC8X2tJhPLmeVE3SGDdYI4QNKdVmhNvMJGgPQ==", "type": "package", "path": "microsoft.aspnetcore.routing.abstractions/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.Routing.Abstractions.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Routing.Abstractions.xml", "microsoft.aspnetcore.routing.abstractions.2.2.0.nupkg.sha512", "microsoft.aspnetcore.routing.abstractions.nuspec"]}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"sha512": "9ErxAAKaDzxXASB/b5uLEkLgUWv1QbeVxyJYEHQwMaxXOeFFVkQxiq8RyfVcifLU7NR0QY0p3acqx4ZpYfhHDg==", "type": "package", "path": "microsoft.aspnetcore.webutilities/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll", "lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.xml", "microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512", "microsoft.aspnetcore.webutilities.nuspec"]}, "Microsoft.CSharp/4.5.0": {"sha512": "kaj6Wb4qoMuH3HySFJhxwQfe8R/sJsNJnANrvv8WdFPMoNbKY5htfNscv+LHCu5ipz+49m2e+WQXpLXr9XYemQ==", "type": "package", "path": "microsoft.csharp/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/Microsoft.CSharp.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.3/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "microsoft.csharp.4.5.0.nupkg.sha512", "microsoft.csharp.nuspec", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/Microsoft.CSharp.dll", "ref/netcore50/Microsoft.CSharp.xml", "ref/netcore50/de/Microsoft.CSharp.xml", "ref/netcore50/es/Microsoft.CSharp.xml", "ref/netcore50/fr/Microsoft.CSharp.xml", "ref/netcore50/it/Microsoft.CSharp.xml", "ref/netcore50/ja/Microsoft.CSharp.xml", "ref/netcore50/ko/Microsoft.CSharp.xml", "ref/netcore50/ru/Microsoft.CSharp.xml", "ref/netcore50/zh-hans/Microsoft.CSharp.xml", "ref/netcore50/zh-hant/Microsoft.CSharp.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/Microsoft.CSharp.dll", "ref/netstandard1.0/Microsoft.CSharp.xml", "ref/netstandard1.0/de/Microsoft.CSharp.xml", "ref/netstandard1.0/es/Microsoft.CSharp.xml", "ref/netstandard1.0/fr/Microsoft.CSharp.xml", "ref/netstandard1.0/it/Microsoft.CSharp.xml", "ref/netstandard1.0/ja/Microsoft.CSharp.xml", "ref/netstandard1.0/ko/Microsoft.CSharp.xml", "ref/netstandard1.0/ru/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hans/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hant/Microsoft.CSharp.xml", "ref/netstandard2.0/Microsoft.CSharp.dll", "ref/netstandard2.0/Microsoft.CSharp.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Diagnostics.NETCore.Client/0.2.510501": {"sha512": "juoqJYMDs+lRrrZyOkXXMImJHneCF23cuvO4waFRd2Ds7j+ZuGIPbJm0Y/zz34BdeaGiiwGWraMUlln05W1PCQ==", "type": "package", "path": "microsoft.diagnostics.netcore.client/0.2.510501", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net6.0/Microsoft.Diagnostics.NETCore.Client.dll", "lib/net6.0/Microsoft.Diagnostics.NETCore.Client.xml", "lib/netstandard2.0/Microsoft.Diagnostics.NETCore.Client.dll", "lib/netstandard2.0/Microsoft.Diagnostics.NETCore.Client.xml", "microsoft.diagnostics.netcore.client.0.2.510501.nupkg.sha512", "microsoft.diagnostics.netcore.client.nuspec"]}, "Microsoft.DotNet.PlatformAbstractions/2.1.0": {"sha512": "9KPDwvb/hLEVXYruVHVZ8BkebC8j17DmPb56LnqRF74HqSPLjCkrlFUjOtFpQPA2DeADBRTI/e69aCfRBfrhxw==", "type": "package", "path": "microsoft.dotnet.platformabstractions/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net45/Microsoft.DotNet.PlatformAbstractions.dll", "lib/netstandard1.3/Microsoft.DotNet.PlatformAbstractions.dll", "microsoft.dotnet.platformabstractions.2.1.0.nupkg.sha512", "microsoft.dotnet.platformabstractions.nuspec"]}, "Microsoft.EntityFrameworkCore/7.0.1": {"sha512": "uvzJkdmqbB50COlCiNaBjWc4cl3kHBO9P7glPk6wK8xyLrli4xeVip+pmMyT/kYc2shWm1YdxegxFqKeCvQXAA==", "type": "package", "path": "microsoft.entityframeworkcore/7.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "buildTransitive/net6.0/Microsoft.EntityFrameworkCore.props", "lib/net6.0/Microsoft.EntityFrameworkCore.dll", "lib/net6.0/Microsoft.EntityFrameworkCore.xml", "microsoft.entityframeworkcore.7.0.1.nupkg.sha512", "microsoft.entityframeworkcore.nuspec"]}, "Microsoft.EntityFrameworkCore.Abstractions/7.0.1": {"sha512": "pPaoi2RHAkrsJ2kcGZGBiJFUXSS1LC+qET6HCn4hXP+X/kvIriJLv8lx6ddI6gTluGvm/lDgPAA0zZDXYaKf3A==", "type": "package", "path": "microsoft.entityframeworkcore.abstractions/7.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll", "lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.xml", "microsoft.entityframeworkcore.abstractions.7.0.1.nupkg.sha512", "microsoft.entityframeworkcore.abstractions.nuspec"]}, "Microsoft.EntityFrameworkCore.Analyzers/7.0.1": {"sha512": "4A4NFkPFoRMdry24Hjdb5/sOH3tU3hzFePTGnSg8P+VBrFp/EO8kcA4L0hBzwbXj2HGNL8I/quYyS5GVtb4EOg==", "type": "package", "path": "microsoft.entityframeworkcore.analyzers/7.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "analyzers/dotnet/cs/Microsoft.EntityFrameworkCore.Analyzers.dll", "lib/netstandard2.0/_._", "microsoft.entityframeworkcore.analyzers.7.0.1.nupkg.sha512", "microsoft.entityframeworkcore.analyzers.nuspec"]}, "Microsoft.EntityFrameworkCore.DynamicLinq/6.2.20": {"sha512": "RD72FFL8F5KTDj6vyM/tbSrUICpkYtpr+6CLnNd4USACQ/CxuzsLQBD15cWTtlBlXIheJwKuak2xew70WrBe8g==", "type": "package", "path": "microsoft.entityframeworkcore.dynamiclinq/6.2.20", "files": [".nupkg.metadata", ".signature.p7s", "lib/net6.0/Microsoft.EntityFrameworkCore.DynamicLinq.dll", "lib/net6.0/Microsoft.EntityFrameworkCore.DynamicLinq.pdb", "lib/net6.0/Microsoft.EntityFrameworkCore.DynamicLinq.xml", "microsoft.entityframeworkcore.dynamiclinq.6.2.20.nupkg.sha512", "microsoft.entityframeworkcore.dynamiclinq.nuspec"]}, "Microsoft.EntityFrameworkCore.Relational/7.0.0": {"sha512": "eQiYygtR2xZ0Uy7KtiFRHpoEx/U8xNwbNRgu1pEJgSxbJLtg6tDL1y2YcIbSuIRSNEljXIIHq/apEhGm1QL70g==", "type": "package", "path": "microsoft.entityframeworkcore.relational/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll", "lib/net6.0/Microsoft.EntityFrameworkCore.Relational.xml", "microsoft.entityframeworkcore.relational.7.0.0.nupkg.sha512", "microsoft.entityframeworkcore.relational.nuspec"]}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"sha512": "IeimUd0TNbhB4ded3AbgBLQv2SnsiVugDyGV1MvspQFVlA07nDC7Zul7kcwH5jWN3JiTcp/ySE83AIJo8yfKjg==", "type": "package", "path": "microsoft.extensions.caching.abstractions/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Abstractions.targets", "lib/net462/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net462/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.xml", "microsoft.extensions.caching.abstractions.7.0.0.nupkg.sha512", "microsoft.extensions.caching.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"sha512": "xpidBs2KCE2gw1JrD0quHE72kvCaI3xFql5/Peb2GRtUuZX+dYPoK/NTdVMiM67Svym0M0Df9A3xyU0FbMQhHw==", "type": "package", "path": "microsoft.extensions.caching.memory/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Memory.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Memory.targets", "lib/net462/Microsoft.Extensions.Caching.Memory.dll", "lib/net462/Microsoft.Extensions.Caching.Memory.xml", "lib/net6.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net6.0/Microsoft.Extensions.Caching.Memory.xml", "lib/net7.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net7.0/Microsoft.Extensions.Caching.Memory.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.xml", "microsoft.extensions.caching.memory.7.0.0.nupkg.sha512", "microsoft.extensions.caching.memory.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"sha512": "f34u2eaqIjNO9YLHBz8rozVZ+TcFiFs0F3r7nUJd7FRkVSxk8u4OpoK226mi49MwexHOR2ibP9MFvRUaLilcQQ==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.7.0.0.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"sha512": "elNeOmkeX3eDVG6pYVeV82p29hr+UKDaBhrZyWvWLw/EVZSYEkZlQdkp0V39k/Xehs2Qa0mvoCvkVj3eQxNQ1Q==", "type": "package", "path": "microsoft.extensions.dependencyinjection/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.7.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"sha512": "h3j/QfmFN4S0w4C2A6X7arXij/M/OVw3uQHSOFxnND4DyAzO1F9eMX7Eti7lU/OkSthEE0WzRsfT/Dmx86jzCw==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.7.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyModel/2.1.0": {"sha512": "nS2XKqi+1A1umnYNLX2Fbm/XnzCxs5i+zXVJ3VC6r9t2z0NZr9FLnJN4VQpKigdcWH/iFTbMuX6M6WQJcTjVIg==", "type": "package", "path": "microsoft.extensions.dependencymodel/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net451/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard1.3/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard1.6/Microsoft.Extensions.DependencyModel.dll", "microsoft.extensions.dependencymodel.2.1.0.nupkg.sha512", "microsoft.extensions.dependencymodel.nuspec"]}, "Microsoft.Extensions.FileProviders.Abstractions/2.2.0": {"sha512": "EcnaSsPTqx2MGnHrmWOD0ugbuuqVT8iICqSqPzi45V5/MA1LjUNb0kwgcxBGqizV1R+WeBK7/Gw25Jzkyk9bIw==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.2.2.0.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec"]}, "Microsoft.Extensions.Hosting.Abstractions/2.2.0": {"sha512": "+k4AEn68HOJat5gj1TWa6X28WlirNQO9sPIIeQbia+91n03esEtMSSoekSTpMjUzjqtJWQN3McVx0GvSPFHF/Q==", "type": "package", "path": "microsoft.extensions.hosting.abstractions/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.xml", "microsoft.extensions.hosting.abstractions.2.2.0.nupkg.sha512", "microsoft.extensions.hosting.abstractions.nuspec"]}, "Microsoft.Extensions.Logging/7.0.0": {"sha512": "Nw2muoNrOG5U5qa2ZekXwudUn2BJcD41e65zwmDHb1fQegTX66UokLWZkJRpqSSHXDOWZ5V0iqhbxOEky91atA==", "type": "package", "path": "microsoft.extensions.logging/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net6.0/Microsoft.Extensions.Logging.dll", "lib/net6.0/Microsoft.Extensions.Logging.xml", "lib/net7.0/Microsoft.Extensions.Logging.dll", "lib/net7.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.7.0.0.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"sha512": "kmn78+LPVMOWeITUjIlfxUPDsI0R6G0RkeAMBmQxAJ7vBJn4q2dTva7pWi65ceN5vPGjJ9q/Uae2WKgvfktJAw==", "type": "package", "path": "microsoft.extensions.logging.abstractions/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.7.0.0.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.ObjectPool/9.0.0": {"sha512": "UbsU/gYe4nv1DeqMXIVzDfNNek7Sk2kKuAOXL/Y+sLcAR0HwFUqzg1EPiU88jeHNe0g81aPvvHbvHarQr3r9IA==", "type": "package", "path": "microsoft.extensions.objectpool/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.Extensions.ObjectPool.dll", "lib/net462/Microsoft.Extensions.ObjectPool.xml", "lib/net9.0/Microsoft.Extensions.ObjectPool.dll", "lib/net9.0/Microsoft.Extensions.ObjectPool.xml", "lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll", "lib/netstandard2.0/Microsoft.Extensions.ObjectPool.xml", "microsoft.extensions.objectpool.9.0.0.nupkg.sha512", "microsoft.extensions.objectpool.nuspec"]}, "Microsoft.Extensions.Options/7.0.0": {"sha512": "lP1yBnTTU42cKpMozuafbvNtQ7QcBjr/CcK3bYOGEMH55Fjt+iecXjT6chR7vbgCMqy3PG3aNQSZgo/EuY/9qQ==", "type": "package", "path": "microsoft.extensions.options/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net6.0/Microsoft.Extensions.Options.dll", "lib/net6.0/Microsoft.Extensions.Options.xml", "lib/net7.0/Microsoft.Extensions.Options.dll", "lib/net7.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.7.0.0.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/7.0.0": {"sha512": "um1KU5kxcRp3CNuI8o/GrZtD4AIOXDk+RLsytjZ9QPok3ttLUelLKpilVPuaFT3TFjOhSibUAso0odbOaCDj3Q==", "type": "package", "path": "microsoft.extensions.primitives/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net6.0/Microsoft.Extensions.Primitives.dll", "lib/net6.0/Microsoft.Extensions.Primitives.xml", "lib/net7.0/Microsoft.Extensions.Primitives.dll", "lib/net7.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.7.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.IdentityModel.Abstractions/6.34.0": {"sha512": "5nInt1KKSpKQBlhe6gXz4yKxRzRUQa21vCvSIIKKzAI2e1r9PHQOZc7aRzBA8L/JCvBxLbCxelvUqun6qwWPJg==", "type": "package", "path": "microsoft.identitymodel.abstractions/6.34.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Abstractions.dll", "lib/net45/Microsoft.IdentityModel.Abstractions.xml", "lib/net461/Microsoft.IdentityModel.Abstractions.dll", "lib/net461/Microsoft.IdentityModel.Abstractions.xml", "lib/net462/Microsoft.IdentityModel.Abstractions.dll", "lib/net462/Microsoft.IdentityModel.Abstractions.xml", "lib/net472/Microsoft.IdentityModel.Abstractions.dll", "lib/net472/Microsoft.IdentityModel.Abstractions.xml", "lib/net6.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net6.0/Microsoft.IdentityModel.Abstractions.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.xml", "microsoft.identitymodel.abstractions.6.34.0.nupkg.sha512", "microsoft.identitymodel.abstractions.nuspec"]}, "Microsoft.IdentityModel.JsonWebTokens/6.34.0": {"sha512": "CZMom/ZoWcgjxLMxmCmcEkuoA0OA4swN1CGeMBQyxF/hEZgRbWK9EnWVJ9/oMUq3D1+OGJjnbN+W6gFq9kZcEg==", "type": "package", "path": "microsoft.identitymodel.jsonwebtokens/6.34.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net45/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.xml", "microsoft.identitymodel.jsonwebtokens.6.34.0.nupkg.sha512", "microsoft.identitymodel.jsonwebtokens.nuspec"]}, "Microsoft.IdentityModel.Logging/6.34.0": {"sha512": "E0AbluNkI30/VKa96PxJhhFZDx/NGYIXFrRIRq1N5/V0TToaiuc3hM90QLFszT2BBQefnp/wjm12ilSudmt9bg==", "type": "package", "path": "microsoft.identitymodel.logging/6.34.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Logging.dll", "lib/net45/Microsoft.IdentityModel.Logging.xml", "lib/net461/Microsoft.IdentityModel.Logging.dll", "lib/net461/Microsoft.IdentityModel.Logging.xml", "lib/net462/Microsoft.IdentityModel.Logging.dll", "lib/net462/Microsoft.IdentityModel.Logging.xml", "lib/net472/Microsoft.IdentityModel.Logging.dll", "lib/net472/Microsoft.IdentityModel.Logging.xml", "lib/net6.0/Microsoft.IdentityModel.Logging.dll", "lib/net6.0/Microsoft.IdentityModel.Logging.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.xml", "microsoft.identitymodel.logging.6.34.0.nupkg.sha512", "microsoft.identitymodel.logging.nuspec"]}, "Microsoft.IdentityModel.Tokens/6.34.0": {"sha512": "PEPcGMqbEwEwbpQ6nTld9Nqq6V5BPZSOfk71qXZ7h7DuGuxa13bWvjImhJba5Ko88YvIuZuOBJWFZmjLfwbNXA==", "type": "package", "path": "microsoft.identitymodel.tokens/6.34.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Tokens.dll", "lib/net45/Microsoft.IdentityModel.Tokens.xml", "lib/net461/Microsoft.IdentityModel.Tokens.dll", "lib/net461/Microsoft.IdentityModel.Tokens.xml", "lib/net462/Microsoft.IdentityModel.Tokens.dll", "lib/net462/Microsoft.IdentityModel.Tokens.xml", "lib/net472/Microsoft.IdentityModel.Tokens.dll", "lib/net472/Microsoft.IdentityModel.Tokens.xml", "lib/net6.0/Microsoft.IdentityModel.Tokens.dll", "lib/net6.0/Microsoft.IdentityModel.Tokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.xml", "microsoft.identitymodel.tokens.6.34.0.nupkg.sha512", "microsoft.identitymodel.tokens.nuspec"]}, "Microsoft.Net.Http.Headers/2.2.0": {"sha512": "iZNkjYqlo8sIOI0bQfpsSoMTmB/kyvmV2h225ihyZT33aTp48ZpF6qYnXxzSXmHt8DpBAwBTX+1s1UFLbYfZKg==", "type": "package", "path": "microsoft.net.http.headers/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Net.Http.Headers.dll", "lib/netstandard2.0/Microsoft.Net.Http.Headers.xml", "microsoft.net.http.headers.2.2.0.nupkg.sha512", "microsoft.net.http.headers.nuspec"]}, "Microsoft.NETCore.Platforms/1.1.0": {"sha512": "kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "type": "package", "path": "microsoft.netcore.platforms/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.1.1.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json"]}, "Microsoft.NETCore.Targets/1.1.0": {"sha512": "aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "type": "package", "path": "microsoft.netcore.targets/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.targets.1.1.0.nupkg.sha512", "microsoft.netcore.targets.nuspec", "runtime.json"]}, "MyNihongo.KanaConverter/1.0.3": {"sha512": "/HK2iltRWiYKGhFGKvcfMAyixzwiOkwQ1Ppp6/DZJQxPG0bKBYMYYIJd5oRyb0hDy11nDNea4UG/0xSsVGolRA==", "type": "package", "path": "mynihongo.kanaconverter/1.0.3", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "favico.png", "lib/netstandard2.0/MyNihongo.KanaConverter.dll", "mynihongo.kanaconverter.1.0.3.nupkg.sha512", "mynihongo.kanaconverter.nuspec"]}, "Newtonsoft.Json/13.0.2": {"sha512": "R2pZ3B0UjeyHShm9vG+Tu0EBb2lC8b0dFzV9gVn50ofHXh9Smjk6kTn7A/FdAsC8B5cKib1OnGYOXxRBz5XQDg==", "type": "package", "path": "newtonsoft.json/13.0.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.2.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "Npgsql/7.0.0": {"sha512": "tOBFksJZ2MiEz8xtDUgS5IG19jVO3nSP15QDYWiiGpXHe0PsLoQBts2Sg3hHKrrLTuw+AjsJz9iKvvGNHyKDIg==", "type": "package", "path": "npgsql/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net5.0/Npgsql.dll", "lib/net5.0/Npgsql.xml", "lib/net6.0/Npgsql.dll", "lib/net6.0/Npgsql.xml", "lib/net7.0/Npgsql.dll", "lib/net7.0/Npgsql.xml", "lib/netcoreapp3.1/Npgsql.dll", "lib/netcoreapp3.1/Npgsql.xml", "lib/netstandard2.0/Npgsql.dll", "lib/netstandard2.0/Npgsql.xml", "lib/netstandard2.1/Npgsql.dll", "lib/netstandard2.1/Npgsql.xml", "npgsql.7.0.0.nupkg.sha512", "npgsql.nuspec", "postgresql.png"]}, "Npgsql.EntityFrameworkCore.PostgreSQL/7.0.0": {"sha512": "CyUNlFZmtX2Kmw8XK5Tlx5eVUCzWJ+zJHErxZiMo2Y8zCRuH9+/OMGwG+9Mmp5zD5p3Ifbi5Pp3btsqoDDkSZQ==", "type": "package", "path": "npgsql.entityframeworkcore.postgresql/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net6.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll", "lib/net6.0/Npgsql.EntityFrameworkCore.PostgreSQL.xml", "lib/net7.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll", "lib/net7.0/Npgsql.EntityFrameworkCore.PostgreSQL.xml", "npgsql.entityframeworkcore.postgresql.7.0.0.nupkg.sha512", "npgsql.entityframeworkcore.postgresql.nuspec", "postgresql.png"]}, "Pipelines.Sockets.Unofficial/2.2.2": {"sha512": "Bhk0FWxH1paI+18zr1g5cTL+ebeuDcBCR+rRFO+fKEhretgjs7MF2Mc1P64FGLecWp4zKCUOPzngBNrqVyY7Zg==", "type": "package", "path": "pipelines.sockets.unofficial/2.2.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Pipelines.Sockets.Unofficial.dll", "lib/net461/Pipelines.Sockets.Unofficial.xml", "lib/net472/Pipelines.Sockets.Unofficial.dll", "lib/net472/Pipelines.Sockets.Unofficial.xml", "lib/net5.0/Pipelines.Sockets.Unofficial.dll", "lib/net5.0/Pipelines.Sockets.Unofficial.xml", "lib/netcoreapp3.1/Pipelines.Sockets.Unofficial.dll", "lib/netcoreapp3.1/Pipelines.Sockets.Unofficial.xml", "lib/netstandard2.0/Pipelines.Sockets.Unofficial.dll", "lib/netstandard2.0/Pipelines.Sockets.Unofficial.xml", "lib/netstandard2.1/Pipelines.Sockets.Unofficial.dll", "lib/netstandard2.1/Pipelines.Sockets.Unofficial.xml", "pipelines.sockets.unofficial.2.2.2.nupkg.sha512", "pipelines.sockets.unofficial.nuspec"]}, "runtime.native.System/4.0.0": {"sha512": "QfS/nQI7k/BLgmLrw7qm7YBoULEvgWnPI+cYsbfCVFTW8Aj+i8JhccxcFMu1RWms0YZzF+UHguNBK4Qn89e2Sg==", "type": "package", "path": "runtime.native.system/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.native.system.4.0.0.nupkg.sha512", "runtime.native.system.nuspec"]}, "Sentry/4.13.0": {"sha512": "Wfw3M1WpFcrYaGzPm7QyUTfIOYkVXQ1ry6p4WYjhbLz9fPwV23SGQZTFDpdox67NHM0V0g1aoQ4YKLm4ANtEEg==", "type": "package", "path": "sentry/4.13.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "README.md", "build/Sentry.Native.targets", "build/Sentry.targets", "buildTransitive/Sentry.Native.targets", "buildTransitive/Sentry.targets", "lib/net462/Sentry.dll", "lib/net462/Sentry.xml", "lib/net6.0/Sentry.dll", "lib/net6.0/Sentry.xml", "lib/net7.0-android33.0/Sentry.dll", "lib/net7.0-android33.0/Sentry.xml", "lib/net7.0-ios16.1/Sentry.dll", "lib/net7.0-ios16.1/Sentry.xml", "lib/net7.0-maccatalyst16.1/Sentry.dll", "lib/net7.0-maccatalyst16.1/Sentry.xml", "lib/net8.0-android34.0/Sentry.dll", "lib/net8.0-android34.0/Sentry.xml", "lib/net8.0-ios18.0/Sentry.dll", "lib/net8.0-ios18.0/Sentry.xml", "lib/net8.0-maccatalyst18.0/Sentry.dll", "lib/net8.0-maccatalyst18.0/Sentry.xml", "lib/net8.0/Sentry.dll", "lib/net8.0/Sentry.xml", "lib/netstandard2.0/Sentry.dll", "lib/netstandard2.0/Sentry.xml", "lib/netstandard2.1/Sentry.dll", "lib/netstandard2.1/Sentry.xml", "sentry-native/linux-x64/libsentry-native.a", "sentry-native/osx/libsentry-native.a", "sentry-native/win-x64/sentry-native.lib", "sentry-nuget.png", "sentry.4.13.0.nupkg.sha512", "sentry.nuspec", "tools/sentry-cli-Darwin-arm64", "tools/sentry-cli-Darwin-x86_64", "tools/sentry-cli-Linux-aarch64", "tools/sentry-cli-Linux-i686", "tools/sentry-cli-Linux-x86_64", "tools/sentry-cli-Windows-i686.exe", "tools/sentry-cli-Windows-x86_64.exe"]}, "Sentry.Profiling/4.13.0": {"sha512": "FJM5SDwHGN2+ceVGCeVQLHXNSBQEHSpvPrBVqVdbbduIdf7yWvaCdCn8MZyCJsvOuKMpwbrle6lXtxrKOZ2ESA==", "type": "package", "path": "sentry.profiling/4.13.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "build/Sentry.Profiling.targets", "buildTransitive/Sentry.Profiling.targets", "lib/net6.0/Microsoft.Diagnostics.FastSerialization.dll", "lib/net6.0/Microsoft.Diagnostics.FastSerialization.pdb", "lib/net6.0/Microsoft.Diagnostics.Tracing.TraceEvent.dll", "lib/net6.0/Microsoft.Diagnostics.Tracing.TraceEvent.pdb", "lib/net6.0/Sentry.Profiling.dll", "lib/net6.0/Sentry.Profiling.xml", "lib/net8.0/Microsoft.Diagnostics.FastSerialization.dll", "lib/net8.0/Microsoft.Diagnostics.FastSerialization.pdb", "lib/net8.0/Microsoft.Diagnostics.Tracing.TraceEvent.dll", "lib/net8.0/Microsoft.Diagnostics.Tracing.TraceEvent.pdb", "lib/net8.0/Sentry.Profiling.dll", "lib/net8.0/Sentry.Profiling.xml", "sentry-nuget.png", "sentry.profiling.4.13.0.nupkg.sha512", "sentry.profiling.nuspec"]}, "StackExchange.Redis/2.6.111": {"sha512": "49NlwihVG9I1YaPqYBx2e2yPqC4ecXMog8zVXMC3rjj2kufGkC3ofqvhOPBKP0c9ZQdJ3hhzduM7ckOQTE+gxg==", "type": "package", "path": "stackexchange.redis/2.6.111", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/StackExchange.Redis.dll", "lib/net461/StackExchange.Redis.xml", "lib/net472/StackExchange.Redis.dll", "lib/net472/StackExchange.Redis.xml", "lib/net5.0/StackExchange.Redis.dll", "lib/net5.0/StackExchange.Redis.xml", "lib/netcoreapp3.1/StackExchange.Redis.dll", "lib/netcoreapp3.1/StackExchange.Redis.xml", "lib/netstandard2.0/StackExchange.Redis.dll", "lib/netstandard2.0/StackExchange.Redis.xml", "stackexchange.redis.2.6.111.nupkg.sha512", "stackexchange.redis.nuspec"]}, "System.AppContext/4.1.0": {"sha512": "3QjO4jNV7PdKkmQAVp9atA+usVnKRwI3Kx1nMwJ93T0LcQfx7pKAYk0nKz5wn1oP5iqlhZuy6RXOFdhr7rDwow==", "type": "package", "path": "system.appcontext/4.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.AppContext.dll", "lib/net463/System.AppContext.dll", "lib/netcore50/System.AppContext.dll", "lib/netstandard1.6/System.AppContext.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.AppContext.dll", "ref/net463/System.AppContext.dll", "ref/netstandard/_._", "ref/netstandard1.3/System.AppContext.dll", "ref/netstandard1.3/System.AppContext.xml", "ref/netstandard1.3/de/System.AppContext.xml", "ref/netstandard1.3/es/System.AppContext.xml", "ref/netstandard1.3/fr/System.AppContext.xml", "ref/netstandard1.3/it/System.AppContext.xml", "ref/netstandard1.3/ja/System.AppContext.xml", "ref/netstandard1.3/ko/System.AppContext.xml", "ref/netstandard1.3/ru/System.AppContext.xml", "ref/netstandard1.3/zh-hans/System.AppContext.xml", "ref/netstandard1.3/zh-hant/System.AppContext.xml", "ref/netstandard1.6/System.AppContext.dll", "ref/netstandard1.6/System.AppContext.xml", "ref/netstandard1.6/de/System.AppContext.xml", "ref/netstandard1.6/es/System.AppContext.xml", "ref/netstandard1.6/fr/System.AppContext.xml", "ref/netstandard1.6/it/System.AppContext.xml", "ref/netstandard1.6/ja/System.AppContext.xml", "ref/netstandard1.6/ko/System.AppContext.xml", "ref/netstandard1.6/ru/System.AppContext.xml", "ref/netstandard1.6/zh-hans/System.AppContext.xml", "ref/netstandard1.6/zh-hant/System.AppContext.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.AppContext.dll", "system.appcontext.4.1.0.nupkg.sha512", "system.appcontext.nuspec"]}, "System.Buffers/4.5.0": {"sha512": "pL2ChpaRRWI/p4LXyy4RgeWlYF2sgfj/pnVMvBqwNFr5cXg7CXNnWZWxrOONLg8VGdFB8oB+EG2Qw4MLgTOe+A==", "type": "package", "path": "system.buffers/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.0.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Collections/4.0.11": {"sha512": "YUJGz6eFKqS0V//mLt25vFGrrCvOnsXjlvFQs+KimpwNxug9x0Pzy4PlFMU3Q2IzqAa9G2L4LsK3+9vCBK7oTg==", "type": "package", "path": "system.collections/4.0.11", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Collections.dll", "ref/netcore50/System.Collections.xml", "ref/netcore50/de/System.Collections.xml", "ref/netcore50/es/System.Collections.xml", "ref/netcore50/fr/System.Collections.xml", "ref/netcore50/it/System.Collections.xml", "ref/netcore50/ja/System.Collections.xml", "ref/netcore50/ko/System.Collections.xml", "ref/netcore50/ru/System.Collections.xml", "ref/netcore50/zh-hans/System.Collections.xml", "ref/netcore50/zh-hant/System.Collections.xml", "ref/netstandard1.0/System.Collections.dll", "ref/netstandard1.0/System.Collections.xml", "ref/netstandard1.0/de/System.Collections.xml", "ref/netstandard1.0/es/System.Collections.xml", "ref/netstandard1.0/fr/System.Collections.xml", "ref/netstandard1.0/it/System.Collections.xml", "ref/netstandard1.0/ja/System.Collections.xml", "ref/netstandard1.0/ko/System.Collections.xml", "ref/netstandard1.0/ru/System.Collections.xml", "ref/netstandard1.0/zh-hans/System.Collections.xml", "ref/netstandard1.0/zh-hant/System.Collections.xml", "ref/netstandard1.3/System.Collections.dll", "ref/netstandard1.3/System.Collections.xml", "ref/netstandard1.3/de/System.Collections.xml", "ref/netstandard1.3/es/System.Collections.xml", "ref/netstandard1.3/fr/System.Collections.xml", "ref/netstandard1.3/it/System.Collections.xml", "ref/netstandard1.3/ja/System.Collections.xml", "ref/netstandard1.3/ko/System.Collections.xml", "ref/netstandard1.3/ru/System.Collections.xml", "ref/netstandard1.3/zh-hans/System.Collections.xml", "ref/netstandard1.3/zh-hant/System.Collections.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.4.0.11.nupkg.sha512", "system.collections.nuspec"]}, "System.Diagnostics.Debug/4.0.11": {"sha512": "w5U95fVKHY4G8ASs/K5iK3J5LY+/dLFd4vKejsnI/ZhBsWS9hQakfx3Zr7lRWKg4tAw9r4iktyvsTagWkqYCiw==", "type": "package", "path": "system.diagnostics.debug/4.0.11", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Diagnostics.Debug.dll", "ref/netcore50/System.Diagnostics.Debug.xml", "ref/netcore50/de/System.Diagnostics.Debug.xml", "ref/netcore50/es/System.Diagnostics.Debug.xml", "ref/netcore50/fr/System.Diagnostics.Debug.xml", "ref/netcore50/it/System.Diagnostics.Debug.xml", "ref/netcore50/ja/System.Diagnostics.Debug.xml", "ref/netcore50/ko/System.Diagnostics.Debug.xml", "ref/netcore50/ru/System.Diagnostics.Debug.xml", "ref/netcore50/zh-hans/System.Diagnostics.Debug.xml", "ref/netcore50/zh-hant/System.Diagnostics.Debug.xml", "ref/netstandard1.0/System.Diagnostics.Debug.dll", "ref/netstandard1.0/System.Diagnostics.Debug.xml", "ref/netstandard1.0/de/System.Diagnostics.Debug.xml", "ref/netstandard1.0/es/System.Diagnostics.Debug.xml", "ref/netstandard1.0/fr/System.Diagnostics.Debug.xml", "ref/netstandard1.0/it/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ja/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ko/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ru/System.Diagnostics.Debug.xml", "ref/netstandard1.0/zh-hans/System.Diagnostics.Debug.xml", "ref/netstandard1.0/zh-hant/System.Diagnostics.Debug.xml", "ref/netstandard1.3/System.Diagnostics.Debug.dll", "ref/netstandard1.3/System.Diagnostics.Debug.xml", "ref/netstandard1.3/de/System.Diagnostics.Debug.xml", "ref/netstandard1.3/es/System.Diagnostics.Debug.xml", "ref/netstandard1.3/fr/System.Diagnostics.Debug.xml", "ref/netstandard1.3/it/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ja/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ko/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ru/System.Diagnostics.Debug.xml", "ref/netstandard1.3/zh-hans/System.Diagnostics.Debug.xml", "ref/netstandard1.3/zh-hant/System.Diagnostics.Debug.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.diagnostics.debug.4.0.11.nupkg.sha512", "system.diagnostics.debug.nuspec"]}, "System.Diagnostics.DiagnosticSource/4.5.0": {"sha512": "eIHRELiYDQvsMToML81QFkXEEYXUSUT2F28t1SGrevWqP+epFdw80SyAXIKTXOHrIEXReFOEnEr7XlGiC2GgOg==", "type": "package", "path": "system.diagnostics.diagnosticsource/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net45/System.Diagnostics.DiagnosticSource.dll", "lib/net45/System.Diagnostics.DiagnosticSource.xml", "lib/net46/System.Diagnostics.DiagnosticSource.dll", "lib/net46/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard1.1/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard1.1/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard1.3/System.Diagnostics.DiagnosticSource.xml", "lib/portable-net45+win8+wpa81/System.Diagnostics.DiagnosticSource.dll", "lib/portable-net45+win8+wpa81/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.4.5.0.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Dynamic.Runtime/4.0.11": {"sha512": "db34f6LHYM0U0JpE+sOmjar27BnqTVkbLJhgfwMpTdgTigG/Hna3m2MYVwnFzGGKnEJk2UXFuoVTr8WUbU91/A==", "type": "package", "path": "system.dynamic.runtime/4.0.11", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Dynamic.Runtime.dll", "lib/netstandard1.3/System.Dynamic.Runtime.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Dynamic.Runtime.dll", "ref/netcore50/System.Dynamic.Runtime.xml", "ref/netcore50/de/System.Dynamic.Runtime.xml", "ref/netcore50/es/System.Dynamic.Runtime.xml", "ref/netcore50/fr/System.Dynamic.Runtime.xml", "ref/netcore50/it/System.Dynamic.Runtime.xml", "ref/netcore50/ja/System.Dynamic.Runtime.xml", "ref/netcore50/ko/System.Dynamic.Runtime.xml", "ref/netcore50/ru/System.Dynamic.Runtime.xml", "ref/netcore50/zh-hans/System.Dynamic.Runtime.xml", "ref/netcore50/zh-hant/System.Dynamic.Runtime.xml", "ref/netstandard1.0/System.Dynamic.Runtime.dll", "ref/netstandard1.0/System.Dynamic.Runtime.xml", "ref/netstandard1.0/de/System.Dynamic.Runtime.xml", "ref/netstandard1.0/es/System.Dynamic.Runtime.xml", "ref/netstandard1.0/fr/System.Dynamic.Runtime.xml", "ref/netstandard1.0/it/System.Dynamic.Runtime.xml", "ref/netstandard1.0/ja/System.Dynamic.Runtime.xml", "ref/netstandard1.0/ko/System.Dynamic.Runtime.xml", "ref/netstandard1.0/ru/System.Dynamic.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Dynamic.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Dynamic.Runtime.xml", "ref/netstandard1.3/System.Dynamic.Runtime.dll", "ref/netstandard1.3/System.Dynamic.Runtime.xml", "ref/netstandard1.3/de/System.Dynamic.Runtime.xml", "ref/netstandard1.3/es/System.Dynamic.Runtime.xml", "ref/netstandard1.3/fr/System.Dynamic.Runtime.xml", "ref/netstandard1.3/it/System.Dynamic.Runtime.xml", "ref/netstandard1.3/ja/System.Dynamic.Runtime.xml", "ref/netstandard1.3/ko/System.Dynamic.Runtime.xml", "ref/netstandard1.3/ru/System.Dynamic.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Dynamic.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Dynamic.Runtime.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Dynamic.Runtime.dll", "system.dynamic.runtime.4.0.11.nupkg.sha512", "system.dynamic.runtime.nuspec"]}, "System.Globalization/4.0.11": {"sha512": "B95h0YLEL2oSnwF/XjqSWKnwKOy/01VWkNlsCeMTFJLLabflpGV26nK164eRs5GiaRSBGpOxQ3pKoSnnyZN5pg==", "type": "package", "path": "system.globalization/4.0.11", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Globalization.dll", "ref/netcore50/System.Globalization.xml", "ref/netcore50/de/System.Globalization.xml", "ref/netcore50/es/System.Globalization.xml", "ref/netcore50/fr/System.Globalization.xml", "ref/netcore50/it/System.Globalization.xml", "ref/netcore50/ja/System.Globalization.xml", "ref/netcore50/ko/System.Globalization.xml", "ref/netcore50/ru/System.Globalization.xml", "ref/netcore50/zh-hans/System.Globalization.xml", "ref/netcore50/zh-hant/System.Globalization.xml", "ref/netstandard1.0/System.Globalization.dll", "ref/netstandard1.0/System.Globalization.xml", "ref/netstandard1.0/de/System.Globalization.xml", "ref/netstandard1.0/es/System.Globalization.xml", "ref/netstandard1.0/fr/System.Globalization.xml", "ref/netstandard1.0/it/System.Globalization.xml", "ref/netstandard1.0/ja/System.Globalization.xml", "ref/netstandard1.0/ko/System.Globalization.xml", "ref/netstandard1.0/ru/System.Globalization.xml", "ref/netstandard1.0/zh-hans/System.Globalization.xml", "ref/netstandard1.0/zh-hant/System.Globalization.xml", "ref/netstandard1.3/System.Globalization.dll", "ref/netstandard1.3/System.Globalization.xml", "ref/netstandard1.3/de/System.Globalization.xml", "ref/netstandard1.3/es/System.Globalization.xml", "ref/netstandard1.3/fr/System.Globalization.xml", "ref/netstandard1.3/it/System.Globalization.xml", "ref/netstandard1.3/ja/System.Globalization.xml", "ref/netstandard1.3/ko/System.Globalization.xml", "ref/netstandard1.3/ru/System.Globalization.xml", "ref/netstandard1.3/zh-hans/System.Globalization.xml", "ref/netstandard1.3/zh-hant/System.Globalization.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.globalization.4.0.11.nupkg.sha512", "system.globalization.nuspec"]}, "System.IdentityModel.Tokens.Jwt/6.34.0": {"sha512": "c0misfmFT3QxKY+a16PGlj+DtiUzoPaf26m2avyPZaLRc9vlIdLtmovfRY5MqN+y/SEoBSRXrgVaeZGPgFQQ6w==", "type": "package", "path": "system.identitymodel.tokens.jwt/6.34.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/System.IdentityModel.Tokens.Jwt.dll", "lib/net45/System.IdentityModel.Tokens.Jwt.xml", "lib/net461/System.IdentityModel.Tokens.Jwt.dll", "lib/net461/System.IdentityModel.Tokens.Jwt.xml", "lib/net462/System.IdentityModel.Tokens.Jwt.dll", "lib/net462/System.IdentityModel.Tokens.Jwt.xml", "lib/net472/System.IdentityModel.Tokens.Jwt.dll", "lib/net472/System.IdentityModel.Tokens.Jwt.xml", "lib/net6.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net6.0/System.IdentityModel.Tokens.Jwt.xml", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.xml", "system.identitymodel.tokens.jwt.6.34.0.nupkg.sha512", "system.identitymodel.tokens.jwt.nuspec"]}, "System.IO/4.1.0": {"sha512": "3KlTJceQc3gnGIaHZ7UBZO26SHL1SHE4ddrmiwumFnId+CEHP+O8r386tZKaE6zlk5/mF8vifMBzHj9SaXN+mQ==", "type": "package", "path": "system.io/4.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.IO.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.IO.dll", "ref/netcore50/System.IO.dll", "ref/netcore50/System.IO.xml", "ref/netcore50/de/System.IO.xml", "ref/netcore50/es/System.IO.xml", "ref/netcore50/fr/System.IO.xml", "ref/netcore50/it/System.IO.xml", "ref/netcore50/ja/System.IO.xml", "ref/netcore50/ko/System.IO.xml", "ref/netcore50/ru/System.IO.xml", "ref/netcore50/zh-hans/System.IO.xml", "ref/netcore50/zh-hant/System.IO.xml", "ref/netstandard1.0/System.IO.dll", "ref/netstandard1.0/System.IO.xml", "ref/netstandard1.0/de/System.IO.xml", "ref/netstandard1.0/es/System.IO.xml", "ref/netstandard1.0/fr/System.IO.xml", "ref/netstandard1.0/it/System.IO.xml", "ref/netstandard1.0/ja/System.IO.xml", "ref/netstandard1.0/ko/System.IO.xml", "ref/netstandard1.0/ru/System.IO.xml", "ref/netstandard1.0/zh-hans/System.IO.xml", "ref/netstandard1.0/zh-hant/System.IO.xml", "ref/netstandard1.3/System.IO.dll", "ref/netstandard1.3/System.IO.xml", "ref/netstandard1.3/de/System.IO.xml", "ref/netstandard1.3/es/System.IO.xml", "ref/netstandard1.3/fr/System.IO.xml", "ref/netstandard1.3/it/System.IO.xml", "ref/netstandard1.3/ja/System.IO.xml", "ref/netstandard1.3/ko/System.IO.xml", "ref/netstandard1.3/ru/System.IO.xml", "ref/netstandard1.3/zh-hans/System.IO.xml", "ref/netstandard1.3/zh-hant/System.IO.xml", "ref/netstandard1.5/System.IO.dll", "ref/netstandard1.5/System.IO.xml", "ref/netstandard1.5/de/System.IO.xml", "ref/netstandard1.5/es/System.IO.xml", "ref/netstandard1.5/fr/System.IO.xml", "ref/netstandard1.5/it/System.IO.xml", "ref/netstandard1.5/ja/System.IO.xml", "ref/netstandard1.5/ko/System.IO.xml", "ref/netstandard1.5/ru/System.IO.xml", "ref/netstandard1.5/zh-hans/System.IO.xml", "ref/netstandard1.5/zh-hant/System.IO.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.4.1.0.nupkg.sha512", "system.io.nuspec"]}, "System.IO.FileSystem/4.0.1": {"sha512": "IBErlVq5jOggAD69bg1t0pJcHaDbJbWNUZTPI96fkYWzwYbN6D9wRHMULLDd9dHsl7C2YsxXL31LMfPI1SWt8w==", "type": "package", "path": "system.io.filesystem/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.IO.FileSystem.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.IO.FileSystem.dll", "ref/netstandard1.3/System.IO.FileSystem.dll", "ref/netstandard1.3/System.IO.FileSystem.xml", "ref/netstandard1.3/de/System.IO.FileSystem.xml", "ref/netstandard1.3/es/System.IO.FileSystem.xml", "ref/netstandard1.3/fr/System.IO.FileSystem.xml", "ref/netstandard1.3/it/System.IO.FileSystem.xml", "ref/netstandard1.3/ja/System.IO.FileSystem.xml", "ref/netstandard1.3/ko/System.IO.FileSystem.xml", "ref/netstandard1.3/ru/System.IO.FileSystem.xml", "ref/netstandard1.3/zh-hans/System.IO.FileSystem.xml", "ref/netstandard1.3/zh-hant/System.IO.FileSystem.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.filesystem.4.0.1.nupkg.sha512", "system.io.filesystem.nuspec"]}, "System.IO.FileSystem.Primitives/4.0.1": {"sha512": "kWkKD203JJKxJeE74p8aF8y4Qc9r9WQx4C0cHzHPrY3fv/L/IhWnyCHaFJ3H1QPOH6A93whlQ2vG5nHlBDvzWQ==", "type": "package", "path": "system.io.filesystem.primitives/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.IO.FileSystem.Primitives.dll", "lib/netstandard1.3/System.IO.FileSystem.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.IO.FileSystem.Primitives.dll", "ref/netstandard1.3/System.IO.FileSystem.Primitives.dll", "ref/netstandard1.3/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/de/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/es/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/fr/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/it/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ja/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ko/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ru/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/zh-hans/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/zh-hant/System.IO.FileSystem.Primitives.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.filesystem.primitives.4.0.1.nupkg.sha512", "system.io.filesystem.primitives.nuspec"]}, "System.IO.Pipelines/5.0.1": {"sha512": "qEePWsaq9LoEEIqhbGe6D5J8c9IqQOUuTzzV6wn1POlfdLkJliZY3OlB0j0f17uMWlqZYjH7txj+2YbyrIA8Yg==", "type": "package", "path": "system.io.pipelines/5.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.IO.Pipelines.dll", "lib/net461/System.IO.Pipelines.xml", "lib/netcoreapp3.0/System.IO.Pipelines.dll", "lib/netcoreapp3.0/System.IO.Pipelines.xml", "lib/netstandard1.3/System.IO.Pipelines.dll", "lib/netstandard1.3/System.IO.Pipelines.xml", "lib/netstandard2.0/System.IO.Pipelines.dll", "lib/netstandard2.0/System.IO.Pipelines.xml", "ref/netcoreapp2.0/System.IO.Pipelines.dll", "ref/netcoreapp2.0/System.IO.Pipelines.xml", "system.io.pipelines.5.0.1.nupkg.sha512", "system.io.pipelines.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Linq/4.1.0": {"sha512": "bQ0iYFOQI0nuTnt+NQADns6ucV4DUvMdwN6CbkB1yj8i7arTGiTN5eok1kQwdnnNWSDZfIUySQY+J3d5KjWn0g==", "type": "package", "path": "system.linq/4.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Linq.dll", "lib/netcore50/System.Linq.dll", "lib/netstandard1.6/System.Linq.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Linq.dll", "ref/netcore50/System.Linq.dll", "ref/netcore50/System.Linq.xml", "ref/netcore50/de/System.Linq.xml", "ref/netcore50/es/System.Linq.xml", "ref/netcore50/fr/System.Linq.xml", "ref/netcore50/it/System.Linq.xml", "ref/netcore50/ja/System.Linq.xml", "ref/netcore50/ko/System.Linq.xml", "ref/netcore50/ru/System.Linq.xml", "ref/netcore50/zh-hans/System.Linq.xml", "ref/netcore50/zh-hant/System.Linq.xml", "ref/netstandard1.0/System.Linq.dll", "ref/netstandard1.0/System.Linq.xml", "ref/netstandard1.0/de/System.Linq.xml", "ref/netstandard1.0/es/System.Linq.xml", "ref/netstandard1.0/fr/System.Linq.xml", "ref/netstandard1.0/it/System.Linq.xml", "ref/netstandard1.0/ja/System.Linq.xml", "ref/netstandard1.0/ko/System.Linq.xml", "ref/netstandard1.0/ru/System.Linq.xml", "ref/netstandard1.0/zh-hans/System.Linq.xml", "ref/netstandard1.0/zh-hant/System.Linq.xml", "ref/netstandard1.6/System.Linq.dll", "ref/netstandard1.6/System.Linq.xml", "ref/netstandard1.6/de/System.Linq.xml", "ref/netstandard1.6/es/System.Linq.xml", "ref/netstandard1.6/fr/System.Linq.xml", "ref/netstandard1.6/it/System.Linq.xml", "ref/netstandard1.6/ja/System.Linq.xml", "ref/netstandard1.6/ko/System.Linq.xml", "ref/netstandard1.6/ru/System.Linq.xml", "ref/netstandard1.6/zh-hans/System.Linq.xml", "ref/netstandard1.6/zh-hant/System.Linq.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.linq.4.1.0.nupkg.sha512", "system.linq.nuspec"]}, "System.Linq.Dynamic.Core/1.2.20": {"sha512": "JyggojPaq2QM603WCwghxxwNmi8ESDLCSTTjfq/TVFvj0J2XjXfvfa0X7pe8NOruF4CbPG2K46AWmgvk0Umoqg==", "type": "package", "path": "system.linq.dynamic.core/1.2.20", "files": [".nupkg.metadata", ".signature.p7s", "lib/net35/System.Linq.Dynamic.Core.dll", "lib/net35/System.Linq.Dynamic.Core.pdb", "lib/net35/System.Linq.Dynamic.Core.xml", "lib/net40/System.Linq.Dynamic.Core.dll", "lib/net40/System.Linq.Dynamic.Core.pdb", "lib/net40/System.Linq.Dynamic.Core.xml", "lib/net45/System.Linq.Dynamic.Core.dll", "lib/net45/System.Linq.Dynamic.Core.pdb", "lib/net45/System.Linq.Dynamic.Core.xml", "lib/net452/System.Linq.Dynamic.Core.dll", "lib/net452/System.Linq.Dynamic.Core.pdb", "lib/net452/System.Linq.Dynamic.Core.xml", "lib/net46/System.Linq.Dynamic.Core.dll", "lib/net46/System.Linq.Dynamic.Core.pdb", "lib/net46/System.Linq.Dynamic.Core.xml", "lib/net5.0/System.Linq.Dynamic.Core.dll", "lib/net5.0/System.Linq.Dynamic.Core.pdb", "lib/net5.0/System.Linq.Dynamic.Core.xml", "lib/net6.0/System.Linq.Dynamic.Core.dll", "lib/net6.0/System.Linq.Dynamic.Core.pdb", "lib/net6.0/System.Linq.Dynamic.Core.xml", "lib/netcoreapp2.1/System.Linq.Dynamic.Core.dll", "lib/netcoreapp2.1/System.Linq.Dynamic.Core.pdb", "lib/netcoreapp2.1/System.Linq.Dynamic.Core.xml", "lib/netcoreapp3.1/System.Linq.Dynamic.Core.dll", "lib/netcoreapp3.1/System.Linq.Dynamic.Core.pdb", "lib/netcoreapp3.1/System.Linq.Dynamic.Core.xml", "lib/netstandard1.3/System.Linq.Dynamic.Core.dll", "lib/netstandard1.3/System.Linq.Dynamic.Core.pdb", "lib/netstandard1.3/System.Linq.Dynamic.Core.xml", "lib/netstandard2.0/System.Linq.Dynamic.Core.dll", "lib/netstandard2.0/System.Linq.Dynamic.Core.pdb", "lib/netstandard2.0/System.Linq.Dynamic.Core.xml", "lib/netstandard2.1/System.Linq.Dynamic.Core.dll", "lib/netstandard2.1/System.Linq.Dynamic.Core.pdb", "lib/netstandard2.1/System.Linq.Dynamic.Core.xml", "lib/uap10.0.10240/System.Linq.Dynamic.Core.dll", "lib/uap10.0.10240/System.Linq.Dynamic.Core.pdb", "lib/uap10.0.10240/System.Linq.Dynamic.Core.pri", "lib/uap10.0.10240/System.Linq.Dynamic.Core.xml", "system.linq.dynamic.core.1.2.20.nupkg.sha512", "system.linq.dynamic.core.nuspec"]}, "System.Linq.Expressions/4.1.0": {"sha512": "I+y02iqkgmCAyfbqOmSDOgqdZQ5tTj80Akm5BPSS8EeB0VGWdy6X1KCoYe8Pk6pwDoAKZUOdLVxnTJcExiv5zw==", "type": "package", "path": "system.linq.expressions/4.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Linq.Expressions.dll", "lib/netcore50/System.Linq.Expressions.dll", "lib/netstandard1.6/System.Linq.Expressions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Linq.Expressions.dll", "ref/netcore50/System.Linq.Expressions.dll", "ref/netcore50/System.Linq.Expressions.xml", "ref/netcore50/de/System.Linq.Expressions.xml", "ref/netcore50/es/System.Linq.Expressions.xml", "ref/netcore50/fr/System.Linq.Expressions.xml", "ref/netcore50/it/System.Linq.Expressions.xml", "ref/netcore50/ja/System.Linq.Expressions.xml", "ref/netcore50/ko/System.Linq.Expressions.xml", "ref/netcore50/ru/System.Linq.Expressions.xml", "ref/netcore50/zh-hans/System.Linq.Expressions.xml", "ref/netcore50/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.0/System.Linq.Expressions.dll", "ref/netstandard1.0/System.Linq.Expressions.xml", "ref/netstandard1.0/de/System.Linq.Expressions.xml", "ref/netstandard1.0/es/System.Linq.Expressions.xml", "ref/netstandard1.0/fr/System.Linq.Expressions.xml", "ref/netstandard1.0/it/System.Linq.Expressions.xml", "ref/netstandard1.0/ja/System.Linq.Expressions.xml", "ref/netstandard1.0/ko/System.Linq.Expressions.xml", "ref/netstandard1.0/ru/System.Linq.Expressions.xml", "ref/netstandard1.0/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.0/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.3/System.Linq.Expressions.dll", "ref/netstandard1.3/System.Linq.Expressions.xml", "ref/netstandard1.3/de/System.Linq.Expressions.xml", "ref/netstandard1.3/es/System.Linq.Expressions.xml", "ref/netstandard1.3/fr/System.Linq.Expressions.xml", "ref/netstandard1.3/it/System.Linq.Expressions.xml", "ref/netstandard1.3/ja/System.Linq.Expressions.xml", "ref/netstandard1.3/ko/System.Linq.Expressions.xml", "ref/netstandard1.3/ru/System.Linq.Expressions.xml", "ref/netstandard1.3/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.3/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.6/System.Linq.Expressions.dll", "ref/netstandard1.6/System.Linq.Expressions.xml", "ref/netstandard1.6/de/System.Linq.Expressions.xml", "ref/netstandard1.6/es/System.Linq.Expressions.xml", "ref/netstandard1.6/fr/System.Linq.Expressions.xml", "ref/netstandard1.6/it/System.Linq.Expressions.xml", "ref/netstandard1.6/ja/System.Linq.Expressions.xml", "ref/netstandard1.6/ko/System.Linq.Expressions.xml", "ref/netstandard1.6/ru/System.Linq.Expressions.xml", "ref/netstandard1.6/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.6/zh-hant/System.Linq.Expressions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Linq.Expressions.dll", "system.linq.expressions.4.1.0.nupkg.sha512", "system.linq.expressions.nuspec"]}, "System.Memory/4.5.4": {"sha512": "1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "type": "package", "path": "system.memory/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.4.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ObjectModel/4.0.12": {"sha512": "tAgJM1xt3ytyMoW4qn4wIqgJYm7L7TShRZG4+Q4Qsi2PCcj96pXN7nRywS9KkB3p/xDUjc2HSwP9SROyPYDYKQ==", "type": "package", "path": "system.objectmodel/4.0.12", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.ObjectModel.dll", "lib/netstandard1.3/System.ObjectModel.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.ObjectModel.dll", "ref/netcore50/System.ObjectModel.xml", "ref/netcore50/de/System.ObjectModel.xml", "ref/netcore50/es/System.ObjectModel.xml", "ref/netcore50/fr/System.ObjectModel.xml", "ref/netcore50/it/System.ObjectModel.xml", "ref/netcore50/ja/System.ObjectModel.xml", "ref/netcore50/ko/System.ObjectModel.xml", "ref/netcore50/ru/System.ObjectModel.xml", "ref/netcore50/zh-hans/System.ObjectModel.xml", "ref/netcore50/zh-hant/System.ObjectModel.xml", "ref/netstandard1.0/System.ObjectModel.dll", "ref/netstandard1.0/System.ObjectModel.xml", "ref/netstandard1.0/de/System.ObjectModel.xml", "ref/netstandard1.0/es/System.ObjectModel.xml", "ref/netstandard1.0/fr/System.ObjectModel.xml", "ref/netstandard1.0/it/System.ObjectModel.xml", "ref/netstandard1.0/ja/System.ObjectModel.xml", "ref/netstandard1.0/ko/System.ObjectModel.xml", "ref/netstandard1.0/ru/System.ObjectModel.xml", "ref/netstandard1.0/zh-hans/System.ObjectModel.xml", "ref/netstandard1.0/zh-hant/System.ObjectModel.xml", "ref/netstandard1.3/System.ObjectModel.dll", "ref/netstandard1.3/System.ObjectModel.xml", "ref/netstandard1.3/de/System.ObjectModel.xml", "ref/netstandard1.3/es/System.ObjectModel.xml", "ref/netstandard1.3/fr/System.ObjectModel.xml", "ref/netstandard1.3/it/System.ObjectModel.xml", "ref/netstandard1.3/ja/System.ObjectModel.xml", "ref/netstandard1.3/ko/System.ObjectModel.xml", "ref/netstandard1.3/ru/System.ObjectModel.xml", "ref/netstandard1.3/zh-hans/System.ObjectModel.xml", "ref/netstandard1.3/zh-hant/System.ObjectModel.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.objectmodel.4.0.12.nupkg.sha512", "system.objectmodel.nuspec"]}, "System.Reactive/5.0.0": {"sha512": "erBZjkQHWL9jpasCE/0qKAryzVBJFxGHVBAvgRN1bzM0q2s1S4oYREEEL0Vb+1kA/6BKb5FjUZMp5VXmy+gzkQ==", "type": "package", "path": "system.reactive/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "build/net5.0/_._", "build/netcoreapp3.1/System.Reactive.dll", "build/netcoreapp3.1/System.Reactive.targets", "build/netcoreapp3.1/System.Reactive.xml", "buildTransitive/net5.0/_._", "buildTransitive/netcoreapp3.1/System.Reactive.targets", "lib/net472/System.Reactive.dll", "lib/net472/System.Reactive.xml", "lib/net5.0-windows10.0.19041/System.Reactive.dll", "lib/net5.0-windows10.0.19041/System.Reactive.xml", "lib/net5.0/System.Reactive.dll", "lib/net5.0/System.Reactive.xml", "lib/netcoreapp3.1/_._", "lib/netstandard2.0/System.Reactive.dll", "lib/netstandard2.0/System.Reactive.xml", "lib/uap10.0.16299/System.Reactive.dll", "lib/uap10.0.16299/System.Reactive.pri", "lib/uap10.0.16299/System.Reactive.xml", "system.reactive.5.0.0.nupkg.sha512", "system.reactive.nuspec"]}, "System.Reflection/4.1.0": {"sha512": "JCKANJ0TI7kzoQzuwB/OoJANy1Lg338B6+JVacPl4TpUwi3cReg3nMLplMq2uqYfHFQpKIlHAUVAJlImZz/4ng==", "type": "package", "path": "system.reflection/4.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Reflection.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Reflection.dll", "ref/netcore50/System.Reflection.dll", "ref/netcore50/System.Reflection.xml", "ref/netcore50/de/System.Reflection.xml", "ref/netcore50/es/System.Reflection.xml", "ref/netcore50/fr/System.Reflection.xml", "ref/netcore50/it/System.Reflection.xml", "ref/netcore50/ja/System.Reflection.xml", "ref/netcore50/ko/System.Reflection.xml", "ref/netcore50/ru/System.Reflection.xml", "ref/netcore50/zh-hans/System.Reflection.xml", "ref/netcore50/zh-hant/System.Reflection.xml", "ref/netstandard1.0/System.Reflection.dll", "ref/netstandard1.0/System.Reflection.xml", "ref/netstandard1.0/de/System.Reflection.xml", "ref/netstandard1.0/es/System.Reflection.xml", "ref/netstandard1.0/fr/System.Reflection.xml", "ref/netstandard1.0/it/System.Reflection.xml", "ref/netstandard1.0/ja/System.Reflection.xml", "ref/netstandard1.0/ko/System.Reflection.xml", "ref/netstandard1.0/ru/System.Reflection.xml", "ref/netstandard1.0/zh-hans/System.Reflection.xml", "ref/netstandard1.0/zh-hant/System.Reflection.xml", "ref/netstandard1.3/System.Reflection.dll", "ref/netstandard1.3/System.Reflection.xml", "ref/netstandard1.3/de/System.Reflection.xml", "ref/netstandard1.3/es/System.Reflection.xml", "ref/netstandard1.3/fr/System.Reflection.xml", "ref/netstandard1.3/it/System.Reflection.xml", "ref/netstandard1.3/ja/System.Reflection.xml", "ref/netstandard1.3/ko/System.Reflection.xml", "ref/netstandard1.3/ru/System.Reflection.xml", "ref/netstandard1.3/zh-hans/System.Reflection.xml", "ref/netstandard1.3/zh-hant/System.Reflection.xml", "ref/netstandard1.5/System.Reflection.dll", "ref/netstandard1.5/System.Reflection.xml", "ref/netstandard1.5/de/System.Reflection.xml", "ref/netstandard1.5/es/System.Reflection.xml", "ref/netstandard1.5/fr/System.Reflection.xml", "ref/netstandard1.5/it/System.Reflection.xml", "ref/netstandard1.5/ja/System.Reflection.xml", "ref/netstandard1.5/ko/System.Reflection.xml", "ref/netstandard1.5/ru/System.Reflection.xml", "ref/netstandard1.5/zh-hans/System.Reflection.xml", "ref/netstandard1.5/zh-hant/System.Reflection.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.4.1.0.nupkg.sha512", "system.reflection.nuspec"]}, "System.Reflection.Emit/4.0.1": {"sha512": "P2wqAj72fFjpP6wb9nSfDqNBMab+2ovzSDzUZK7MVIm54tBJEPr9jWfSjjoTpPwj1LeKcmX3vr0ttyjSSFM47g==", "type": "package", "path": "system.reflection.emit/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.dll", "lib/netstandard1.3/System.Reflection.Emit.dll", "lib/xamarinmac20/_._", "ref/MonoAndroid10/_._", "ref/net45/_._", "ref/netstandard1.1/System.Reflection.Emit.dll", "ref/netstandard1.1/System.Reflection.Emit.xml", "ref/netstandard1.1/de/System.Reflection.Emit.xml", "ref/netstandard1.1/es/System.Reflection.Emit.xml", "ref/netstandard1.1/fr/System.Reflection.Emit.xml", "ref/netstandard1.1/it/System.Reflection.Emit.xml", "ref/netstandard1.1/ja/System.Reflection.Emit.xml", "ref/netstandard1.1/ko/System.Reflection.Emit.xml", "ref/netstandard1.1/ru/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hans/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hant/System.Reflection.Emit.xml", "ref/xamarinmac20/_._", "system.reflection.emit.4.0.1.nupkg.sha512", "system.reflection.emit.nuspec"]}, "System.Reflection.Emit.ILGeneration/4.0.1": {"sha512": "Ov6dU8Bu15Bc7zuqttgHF12J5lwSWyTf1S+FJouUXVMSqImLZzYaQ+vRr1rQ0OZ0HqsrwWl4dsKHELckQkVpgA==", "type": "package", "path": "system.reflection.emit.ilgeneration/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.ILGeneration.dll", "lib/netstandard1.3/System.Reflection.Emit.ILGeneration.dll", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "ref/net45/_._", "ref/netstandard1.0/System.Reflection.Emit.ILGeneration.dll", "ref/netstandard1.0/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/de/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/es/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/it/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.ILGeneration.xml", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "runtimes/aot/lib/netcore50/_._", "system.reflection.emit.ilgeneration.4.0.1.nupkg.sha512", "system.reflection.emit.ilgeneration.nuspec"]}, "System.Reflection.Emit.Lightweight/4.0.1": {"sha512": "sSzHHXueZ5Uh0OLpUQprhr+ZYJrLPA2Cmr4gn0wj9+FftNKXx8RIMKvO9qnjk2ebPYUjZ+F2ulGdPOsvj+MEjA==", "type": "package", "path": "system.reflection.emit.lightweight/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.Lightweight.dll", "lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "ref/net45/_._", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.dll", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/de/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/es/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/it/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.Lightweight.xml", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "runtimes/aot/lib/netcore50/_._", "system.reflection.emit.lightweight.4.0.1.nupkg.sha512", "system.reflection.emit.lightweight.nuspec"]}, "System.Reflection.Extensions/4.0.1": {"sha512": "GYrtRsZcMuHF3sbmRHfMYpvxZoIN2bQGrYGerUiWLEkqdEUQZhH3TRSaC/oI4wO0II1RKBPlpIa1TOMxIcOOzQ==", "type": "package", "path": "system.reflection.extensions/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Extensions.dll", "ref/netcore50/System.Reflection.Extensions.xml", "ref/netcore50/de/System.Reflection.Extensions.xml", "ref/netcore50/es/System.Reflection.Extensions.xml", "ref/netcore50/fr/System.Reflection.Extensions.xml", "ref/netcore50/it/System.Reflection.Extensions.xml", "ref/netcore50/ja/System.Reflection.Extensions.xml", "ref/netcore50/ko/System.Reflection.Extensions.xml", "ref/netcore50/ru/System.Reflection.Extensions.xml", "ref/netcore50/zh-hans/System.Reflection.Extensions.xml", "ref/netcore50/zh-hant/System.Reflection.Extensions.xml", "ref/netstandard1.0/System.Reflection.Extensions.dll", "ref/netstandard1.0/System.Reflection.Extensions.xml", "ref/netstandard1.0/de/System.Reflection.Extensions.xml", "ref/netstandard1.0/es/System.Reflection.Extensions.xml", "ref/netstandard1.0/fr/System.Reflection.Extensions.xml", "ref/netstandard1.0/it/System.Reflection.Extensions.xml", "ref/netstandard1.0/ja/System.Reflection.Extensions.xml", "ref/netstandard1.0/ko/System.Reflection.Extensions.xml", "ref/netstandard1.0/ru/System.Reflection.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.extensions.4.0.1.nupkg.sha512", "system.reflection.extensions.nuspec"]}, "System.Reflection.Primitives/4.0.1": {"sha512": "4inTox4wTBaDhB7V3mPvp9XlCbeGYWVEM9/fXALd52vNEAVisc1BoVWQPuUuD0Ga//dNbA/WeMy9u9mzLxGTHQ==", "type": "package", "path": "system.reflection.primitives/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Primitives.dll", "ref/netcore50/System.Reflection.Primitives.xml", "ref/netcore50/de/System.Reflection.Primitives.xml", "ref/netcore50/es/System.Reflection.Primitives.xml", "ref/netcore50/fr/System.Reflection.Primitives.xml", "ref/netcore50/it/System.Reflection.Primitives.xml", "ref/netcore50/ja/System.Reflection.Primitives.xml", "ref/netcore50/ko/System.Reflection.Primitives.xml", "ref/netcore50/ru/System.Reflection.Primitives.xml", "ref/netcore50/zh-hans/System.Reflection.Primitives.xml", "ref/netcore50/zh-hant/System.Reflection.Primitives.xml", "ref/netstandard1.0/System.Reflection.Primitives.dll", "ref/netstandard1.0/System.Reflection.Primitives.xml", "ref/netstandard1.0/de/System.Reflection.Primitives.xml", "ref/netstandard1.0/es/System.Reflection.Primitives.xml", "ref/netstandard1.0/fr/System.Reflection.Primitives.xml", "ref/netstandard1.0/it/System.Reflection.Primitives.xml", "ref/netstandard1.0/ja/System.Reflection.Primitives.xml", "ref/netstandard1.0/ko/System.Reflection.Primitives.xml", "ref/netstandard1.0/ru/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.primitives.4.0.1.nupkg.sha512", "system.reflection.primitives.nuspec"]}, "System.Reflection.TypeExtensions/4.1.0": {"sha512": "tsQ/ptQ3H5FYfON8lL4MxRk/8kFyE0A+tGPXmVP967cT/gzLHYxIejIYSxp4JmIeFHVP78g/F2FE1mUUTbDtrg==", "type": "package", "path": "system.reflection.typeextensions/4.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Reflection.TypeExtensions.dll", "lib/net462/System.Reflection.TypeExtensions.dll", "lib/netcore50/System.Reflection.TypeExtensions.dll", "lib/netstandard1.5/System.Reflection.TypeExtensions.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Reflection.TypeExtensions.dll", "ref/net462/System.Reflection.TypeExtensions.dll", "ref/netstandard1.3/System.Reflection.TypeExtensions.dll", "ref/netstandard1.3/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/de/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/es/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/fr/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/it/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ja/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ko/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ru/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/zh-hans/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/zh-hant/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/System.Reflection.TypeExtensions.dll", "ref/netstandard1.5/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/de/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/es/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/fr/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/it/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ja/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ko/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ru/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/zh-hans/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/zh-hant/System.Reflection.TypeExtensions.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.TypeExtensions.dll", "system.reflection.typeextensions.4.1.0.nupkg.sha512", "system.reflection.typeextensions.nuspec"]}, "System.Resources.ResourceManager/4.0.1": {"sha512": "TxwVeUNoTgUOdQ09gfTjvW411MF+w9MBYL7AtNVc+HtBCFlutPLhUCdZjNkjbhj3bNQWMdHboF0KIWEOjJssbA==", "type": "package", "path": "system.resources.resourcemanager/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Resources.ResourceManager.dll", "ref/netcore50/System.Resources.ResourceManager.xml", "ref/netcore50/de/System.Resources.ResourceManager.xml", "ref/netcore50/es/System.Resources.ResourceManager.xml", "ref/netcore50/fr/System.Resources.ResourceManager.xml", "ref/netcore50/it/System.Resources.ResourceManager.xml", "ref/netcore50/ja/System.Resources.ResourceManager.xml", "ref/netcore50/ko/System.Resources.ResourceManager.xml", "ref/netcore50/ru/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hans/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hant/System.Resources.ResourceManager.xml", "ref/netstandard1.0/System.Resources.ResourceManager.dll", "ref/netstandard1.0/System.Resources.ResourceManager.xml", "ref/netstandard1.0/de/System.Resources.ResourceManager.xml", "ref/netstandard1.0/es/System.Resources.ResourceManager.xml", "ref/netstandard1.0/fr/System.Resources.ResourceManager.xml", "ref/netstandard1.0/it/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ja/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ko/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ru/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hans/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hant/System.Resources.ResourceManager.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.resources.resourcemanager.4.0.1.nupkg.sha512", "system.resources.resourcemanager.nuspec"]}, "System.Runtime/4.3.0": {"sha512": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "type": "package", "path": "system.runtime/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.dll", "lib/portable-net45+win8+wp80+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.dll", "ref/netcore50/System.Runtime.dll", "ref/netcore50/System.Runtime.xml", "ref/netcore50/de/System.Runtime.xml", "ref/netcore50/es/System.Runtime.xml", "ref/netcore50/fr/System.Runtime.xml", "ref/netcore50/it/System.Runtime.xml", "ref/netcore50/ja/System.Runtime.xml", "ref/netcore50/ko/System.Runtime.xml", "ref/netcore50/ru/System.Runtime.xml", "ref/netcore50/zh-hans/System.Runtime.xml", "ref/netcore50/zh-hant/System.Runtime.xml", "ref/netstandard1.0/System.Runtime.dll", "ref/netstandard1.0/System.Runtime.xml", "ref/netstandard1.0/de/System.Runtime.xml", "ref/netstandard1.0/es/System.Runtime.xml", "ref/netstandard1.0/fr/System.Runtime.xml", "ref/netstandard1.0/it/System.Runtime.xml", "ref/netstandard1.0/ja/System.Runtime.xml", "ref/netstandard1.0/ko/System.Runtime.xml", "ref/netstandard1.0/ru/System.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.xml", "ref/netstandard1.2/System.Runtime.dll", "ref/netstandard1.2/System.Runtime.xml", "ref/netstandard1.2/de/System.Runtime.xml", "ref/netstandard1.2/es/System.Runtime.xml", "ref/netstandard1.2/fr/System.Runtime.xml", "ref/netstandard1.2/it/System.Runtime.xml", "ref/netstandard1.2/ja/System.Runtime.xml", "ref/netstandard1.2/ko/System.Runtime.xml", "ref/netstandard1.2/ru/System.Runtime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.xml", "ref/netstandard1.3/System.Runtime.dll", "ref/netstandard1.3/System.Runtime.xml", "ref/netstandard1.3/de/System.Runtime.xml", "ref/netstandard1.3/es/System.Runtime.xml", "ref/netstandard1.3/fr/System.Runtime.xml", "ref/netstandard1.3/it/System.Runtime.xml", "ref/netstandard1.3/ja/System.Runtime.xml", "ref/netstandard1.3/ko/System.Runtime.xml", "ref/netstandard1.3/ru/System.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Runtime.xml", "ref/netstandard1.5/System.Runtime.dll", "ref/netstandard1.5/System.Runtime.xml", "ref/netstandard1.5/de/System.Runtime.xml", "ref/netstandard1.5/es/System.Runtime.xml", "ref/netstandard1.5/fr/System.Runtime.xml", "ref/netstandard1.5/it/System.Runtime.xml", "ref/netstandard1.5/ja/System.Runtime.xml", "ref/netstandard1.5/ko/System.Runtime.xml", "ref/netstandard1.5/ru/System.Runtime.xml", "ref/netstandard1.5/zh-hans/System.Runtime.xml", "ref/netstandard1.5/zh-hant/System.Runtime.xml", "ref/portable-net45+win8+wp80+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.4.3.0.nupkg.sha512", "system.runtime.nuspec"]}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"sha512": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt"]}, "System.Runtime.Extensions/4.1.0": {"sha512": "CUOHjTT/vgP0qGW22U4/hDlOqXmcPq5YicBaXdUR2UiUoLwBT+olO6we4DVbq57jeX5uXH2uerVZhf0qGj+sVQ==", "type": "package", "path": "system.runtime.extensions/4.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.xml", "ref/netcore50/de/System.Runtime.Extensions.xml", "ref/netcore50/es/System.Runtime.Extensions.xml", "ref/netcore50/fr/System.Runtime.Extensions.xml", "ref/netcore50/it/System.Runtime.Extensions.xml", "ref/netcore50/ja/System.Runtime.Extensions.xml", "ref/netcore50/ko/System.Runtime.Extensions.xml", "ref/netcore50/ru/System.Runtime.Extensions.xml", "ref/netcore50/zh-hans/System.Runtime.Extensions.xml", "ref/netcore50/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.0/System.Runtime.Extensions.dll", "ref/netstandard1.0/System.Runtime.Extensions.xml", "ref/netstandard1.0/de/System.Runtime.Extensions.xml", "ref/netstandard1.0/es/System.Runtime.Extensions.xml", "ref/netstandard1.0/fr/System.Runtime.Extensions.xml", "ref/netstandard1.0/it/System.Runtime.Extensions.xml", "ref/netstandard1.0/ja/System.Runtime.Extensions.xml", "ref/netstandard1.0/ko/System.Runtime.Extensions.xml", "ref/netstandard1.0/ru/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.3/System.Runtime.Extensions.dll", "ref/netstandard1.3/System.Runtime.Extensions.xml", "ref/netstandard1.3/de/System.Runtime.Extensions.xml", "ref/netstandard1.3/es/System.Runtime.Extensions.xml", "ref/netstandard1.3/fr/System.Runtime.Extensions.xml", "ref/netstandard1.3/it/System.Runtime.Extensions.xml", "ref/netstandard1.3/ja/System.Runtime.Extensions.xml", "ref/netstandard1.3/ko/System.Runtime.Extensions.xml", "ref/netstandard1.3/ru/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.5/System.Runtime.Extensions.dll", "ref/netstandard1.5/System.Runtime.Extensions.xml", "ref/netstandard1.5/de/System.Runtime.Extensions.xml", "ref/netstandard1.5/es/System.Runtime.Extensions.xml", "ref/netstandard1.5/fr/System.Runtime.Extensions.xml", "ref/netstandard1.5/it/System.Runtime.Extensions.xml", "ref/netstandard1.5/ja/System.Runtime.Extensions.xml", "ref/netstandard1.5/ko/System.Runtime.Extensions.xml", "ref/netstandard1.5/ru/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hant/System.Runtime.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.extensions.4.1.0.nupkg.sha512", "system.runtime.extensions.nuspec"]}, "System.Runtime.Handles/4.0.1": {"sha512": "nCJvEKguXEvk2ymk1gqj625vVnlK3/xdGzx0vOKicQkoquaTBJTP13AIYkocSUwHCLNBwUbXTqTWGDxBTWpt7g==", "type": "package", "path": "system.runtime.handles/4.0.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/_._", "ref/netstandard1.3/System.Runtime.Handles.dll", "ref/netstandard1.3/System.Runtime.Handles.xml", "ref/netstandard1.3/de/System.Runtime.Handles.xml", "ref/netstandard1.3/es/System.Runtime.Handles.xml", "ref/netstandard1.3/fr/System.Runtime.Handles.xml", "ref/netstandard1.3/it/System.Runtime.Handles.xml", "ref/netstandard1.3/ja/System.Runtime.Handles.xml", "ref/netstandard1.3/ko/System.Runtime.Handles.xml", "ref/netstandard1.3/ru/System.Runtime.Handles.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Handles.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Handles.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.handles.4.0.1.nupkg.sha512", "system.runtime.handles.nuspec"]}, "System.Runtime.InteropServices/4.1.0": {"sha512": "16eu3kjHS633yYdkjwShDHZLRNMKVi/s0bY8ODiqJ2RfMhDMAwxZaUaWVnZ2P71kr/or+X9o/xFWtNqz8ivieQ==", "type": "package", "path": "system.runtime.interopservices/4.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.InteropServices.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.InteropServices.dll", "ref/netcore50/System.Runtime.InteropServices.dll", "ref/netcore50/System.Runtime.InteropServices.xml", "ref/netcore50/de/System.Runtime.InteropServices.xml", "ref/netcore50/es/System.Runtime.InteropServices.xml", "ref/netcore50/fr/System.Runtime.InteropServices.xml", "ref/netcore50/it/System.Runtime.InteropServices.xml", "ref/netcore50/ja/System.Runtime.InteropServices.xml", "ref/netcore50/ko/System.Runtime.InteropServices.xml", "ref/netcore50/ru/System.Runtime.InteropServices.xml", "ref/netcore50/zh-hans/System.Runtime.InteropServices.xml", "ref/netcore50/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.1/System.Runtime.InteropServices.dll", "ref/netstandard1.1/System.Runtime.InteropServices.xml", "ref/netstandard1.1/de/System.Runtime.InteropServices.xml", "ref/netstandard1.1/es/System.Runtime.InteropServices.xml", "ref/netstandard1.1/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.1/it/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.1/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.1/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.2/System.Runtime.InteropServices.dll", "ref/netstandard1.2/System.Runtime.InteropServices.xml", "ref/netstandard1.2/de/System.Runtime.InteropServices.xml", "ref/netstandard1.2/es/System.Runtime.InteropServices.xml", "ref/netstandard1.2/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.2/it/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.2/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.2/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.3/System.Runtime.InteropServices.dll", "ref/netstandard1.3/System.Runtime.InteropServices.xml", "ref/netstandard1.3/de/System.Runtime.InteropServices.xml", "ref/netstandard1.3/es/System.Runtime.InteropServices.xml", "ref/netstandard1.3/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.3/it/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.3/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.3/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.5/System.Runtime.InteropServices.dll", "ref/netstandard1.5/System.Runtime.InteropServices.xml", "ref/netstandard1.5/de/System.Runtime.InteropServices.xml", "ref/netstandard1.5/es/System.Runtime.InteropServices.xml", "ref/netstandard1.5/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.5/it/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.5/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.5/zh-hant/System.Runtime.InteropServices.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.interopservices.4.1.0.nupkg.sha512", "system.runtime.interopservices.nuspec"]}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"sha512": "hWPhJxc453RCa8Z29O91EmfGeZIHX1ZH2A8L6lYQVSaKzku2DfArSfMEb1/MYYzPQRJZeu0c9dmYeJKxW5Fgng==", "type": "package", "path": "system.runtime.interopservices.runtimeinformation/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/win8/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/wpa81/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/unix/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/win/lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/win/lib/netcore50/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/win/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "system.runtime.interopservices.runtimeinformation.4.0.0.nupkg.sha512", "system.runtime.interopservices.runtimeinformation.nuspec"]}, "System.Security.Cryptography.Cng/4.5.0": {"sha512": "WG3r7EyjUe9CMPFSs6bty5doUqT+q9pbI80hlNzo2SkPkZ4VTuZkGWjpp77JB8+uaL4DFPRdBsAY+DX3dBK92A==", "type": "package", "path": "system.security.cryptography.cng/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Cng.dll", "lib/net461/System.Security.Cryptography.Cng.dll", "lib/net462/System.Security.Cryptography.Cng.dll", "lib/net47/System.Security.Cryptography.Cng.dll", "lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "lib/netstandard1.3/System.Security.Cryptography.Cng.dll", "lib/netstandard1.4/System.Security.Cryptography.Cng.dll", "lib/netstandard1.6/System.Security.Cryptography.Cng.dll", "lib/netstandard2.0/System.Security.Cryptography.Cng.dll", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Cng.dll", "ref/net461/System.Security.Cryptography.Cng.dll", "ref/net461/System.Security.Cryptography.Cng.xml", "ref/net462/System.Security.Cryptography.Cng.dll", "ref/net462/System.Security.Cryptography.Cng.xml", "ref/net47/System.Security.Cryptography.Cng.dll", "ref/net47/System.Security.Cryptography.Cng.xml", "ref/netcoreapp2.0/System.Security.Cryptography.Cng.dll", "ref/netcoreapp2.0/System.Security.Cryptography.Cng.xml", "ref/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "ref/netcoreapp2.1/System.Security.Cryptography.Cng.xml", "ref/netstandard1.3/System.Security.Cryptography.Cng.dll", "ref/netstandard1.4/System.Security.Cryptography.Cng.dll", "ref/netstandard1.6/System.Security.Cryptography.Cng.dll", "ref/netstandard2.0/System.Security.Cryptography.Cng.dll", "ref/netstandard2.0/System.Security.Cryptography.Cng.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/win/lib/net46/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net462/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net47/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netstandard1.4/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.cryptography.cng.4.5.0.nupkg.sha512", "system.security.cryptography.cng.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Encoding/4.3.0": {"sha512": "BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "type": "package", "path": "system.text.encoding/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.dll", "ref/netcore50/System.Text.Encoding.xml", "ref/netcore50/de/System.Text.Encoding.xml", "ref/netcore50/es/System.Text.Encoding.xml", "ref/netcore50/fr/System.Text.Encoding.xml", "ref/netcore50/it/System.Text.Encoding.xml", "ref/netcore50/ja/System.Text.Encoding.xml", "ref/netcore50/ko/System.Text.Encoding.xml", "ref/netcore50/ru/System.Text.Encoding.xml", "ref/netcore50/zh-hans/System.Text.Encoding.xml", "ref/netcore50/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.0/System.Text.Encoding.dll", "ref/netstandard1.0/System.Text.Encoding.xml", "ref/netstandard1.0/de/System.Text.Encoding.xml", "ref/netstandard1.0/es/System.Text.Encoding.xml", "ref/netstandard1.0/fr/System.Text.Encoding.xml", "ref/netstandard1.0/it/System.Text.Encoding.xml", "ref/netstandard1.0/ja/System.Text.Encoding.xml", "ref/netstandard1.0/ko/System.Text.Encoding.xml", "ref/netstandard1.0/ru/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.3/System.Text.Encoding.dll", "ref/netstandard1.3/System.Text.Encoding.xml", "ref/netstandard1.3/de/System.Text.Encoding.xml", "ref/netstandard1.3/es/System.Text.Encoding.xml", "ref/netstandard1.3/fr/System.Text.Encoding.xml", "ref/netstandard1.3/it/System.Text.Encoding.xml", "ref/netstandard1.3/ja/System.Text.Encoding.xml", "ref/netstandard1.3/ko/System.Text.Encoding.xml", "ref/netstandard1.3/ru/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.4.3.0.nupkg.sha512", "system.text.encoding.nuspec"]}, "System.Text.Encoding.CodePages/6.0.0": {"sha512": "ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "type": "package", "path": "system.text.encoding.codepages/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Text.Encoding.CodePages.targets", "buildTransitive/netcoreapp3.1/_._", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Text.Encoding.CodePages.dll", "lib/net461/System.Text.Encoding.CodePages.xml", "lib/net6.0/System.Text.Encoding.CodePages.dll", "lib/net6.0/System.Text.Encoding.CodePages.xml", "lib/netcoreapp3.1/System.Text.Encoding.CodePages.dll", "lib/netcoreapp3.1/System.Text.Encoding.CodePages.xml", "lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "lib/netstandard2.0/System.Text.Encoding.CodePages.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net461/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net461/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/netcoreapp3.1/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netcoreapp3.1/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netstandard2.0/System.Text.Encoding.CodePages.xml", "system.text.encoding.codepages.6.0.0.nupkg.sha512", "system.text.encoding.codepages.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Encodings.Web/4.7.2": {"sha512": "iTUgB/WtrZ1sWZs84F2hwyQhiRH6QNjQv2DkwrH+WP6RoFga2Q1m3f9/Q7FG8cck8AdHitQkmkXSY8qylcDmuA==", "type": "package", "path": "system.text.encodings.web/4.7.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Text.Encodings.Web.dll", "lib/net461/System.Text.Encodings.Web.xml", "lib/netstandard1.0/System.Text.Encodings.Web.dll", "lib/netstandard1.0/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "lib/netstandard2.1/System.Text.Encodings.Web.dll", "lib/netstandard2.1/System.Text.Encodings.Web.xml", "system.text.encodings.web.4.7.2.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Json/4.7.2": {"sha512": "TcMd95wcrubm9nHvJEQs70rC0H/8omiSGGpU4FQ/ZA1URIqD4pjmFJh2Mfv1yH1eHgJDWTi2hMDXwTET+zOOyg==", "type": "package", "path": "system.text.json/4.7.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Text.Json.dll", "lib/net461/System.Text.Json.xml", "lib/netcoreapp3.0/System.Text.Json.dll", "lib/netcoreapp3.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.4.7.2.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Threading/4.0.11": {"sha512": "N+3xqIcg3VDKyjwwCGaZ9HawG9aC6cSDI+s7ROma310GQo8vilFZa86hqKppwTHleR/G0sfOzhvgnUxWCR/DrQ==", "type": "package", "path": "system.threading/4.0.11", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Threading.dll", "lib/netstandard1.3/System.Threading.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.dll", "ref/netcore50/System.Threading.xml", "ref/netcore50/de/System.Threading.xml", "ref/netcore50/es/System.Threading.xml", "ref/netcore50/fr/System.Threading.xml", "ref/netcore50/it/System.Threading.xml", "ref/netcore50/ja/System.Threading.xml", "ref/netcore50/ko/System.Threading.xml", "ref/netcore50/ru/System.Threading.xml", "ref/netcore50/zh-hans/System.Threading.xml", "ref/netcore50/zh-hant/System.Threading.xml", "ref/netstandard1.0/System.Threading.dll", "ref/netstandard1.0/System.Threading.xml", "ref/netstandard1.0/de/System.Threading.xml", "ref/netstandard1.0/es/System.Threading.xml", "ref/netstandard1.0/fr/System.Threading.xml", "ref/netstandard1.0/it/System.Threading.xml", "ref/netstandard1.0/ja/System.Threading.xml", "ref/netstandard1.0/ko/System.Threading.xml", "ref/netstandard1.0/ru/System.Threading.xml", "ref/netstandard1.0/zh-hans/System.Threading.xml", "ref/netstandard1.0/zh-hant/System.Threading.xml", "ref/netstandard1.3/System.Threading.dll", "ref/netstandard1.3/System.Threading.xml", "ref/netstandard1.3/de/System.Threading.xml", "ref/netstandard1.3/es/System.Threading.xml", "ref/netstandard1.3/fr/System.Threading.xml", "ref/netstandard1.3/it/System.Threading.xml", "ref/netstandard1.3/ja/System.Threading.xml", "ref/netstandard1.3/ko/System.Threading.xml", "ref/netstandard1.3/ru/System.Threading.xml", "ref/netstandard1.3/zh-hans/System.Threading.xml", "ref/netstandard1.3/zh-hant/System.Threading.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Threading.dll", "system.threading.4.0.11.nupkg.sha512", "system.threading.nuspec"]}, "System.Threading.Tasks/4.0.11": {"sha512": "k1S4Gc6IGwtHGT8188RSeGaX86Qw/wnrgNLshJvsdNUOPP9etMmo8S07c+UlOAx4K/xLuN9ivA1bD0LVurtIxQ==", "type": "package", "path": "system.threading.tasks/4.0.11", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.Tasks.dll", "ref/netcore50/System.Threading.Tasks.xml", "ref/netcore50/de/System.Threading.Tasks.xml", "ref/netcore50/es/System.Threading.Tasks.xml", "ref/netcore50/fr/System.Threading.Tasks.xml", "ref/netcore50/it/System.Threading.Tasks.xml", "ref/netcore50/ja/System.Threading.Tasks.xml", "ref/netcore50/ko/System.Threading.Tasks.xml", "ref/netcore50/ru/System.Threading.Tasks.xml", "ref/netcore50/zh-hans/System.Threading.Tasks.xml", "ref/netcore50/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.0/System.Threading.Tasks.dll", "ref/netstandard1.0/System.Threading.Tasks.xml", "ref/netstandard1.0/de/System.Threading.Tasks.xml", "ref/netstandard1.0/es/System.Threading.Tasks.xml", "ref/netstandard1.0/fr/System.Threading.Tasks.xml", "ref/netstandard1.0/it/System.Threading.Tasks.xml", "ref/netstandard1.0/ja/System.Threading.Tasks.xml", "ref/netstandard1.0/ko/System.Threading.Tasks.xml", "ref/netstandard1.0/ru/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.3/System.Threading.Tasks.dll", "ref/netstandard1.3/System.Threading.Tasks.xml", "ref/netstandard1.3/de/System.Threading.Tasks.xml", "ref/netstandard1.3/es/System.Threading.Tasks.xml", "ref/netstandard1.3/fr/System.Threading.Tasks.xml", "ref/netstandard1.3/it/System.Threading.Tasks.xml", "ref/netstandard1.3/ja/System.Threading.Tasks.xml", "ref/netstandard1.3/ko/System.Threading.Tasks.xml", "ref/netstandard1.3/ru/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hant/System.Threading.Tasks.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.4.0.11.nupkg.sha512", "system.threading.tasks.nuspec"]}, "System.Threading.Tasks.Extensions/4.5.1": {"sha512": "WSKUTtLhPR8gllzIWO2x6l4lmAIfbyMAiTlyXAis4QBDonXK4b4S6F8zGARX4/P8wH3DH+sLdhamCiHn+fTU1A==", "type": "package", "path": "system.threading.tasks.extensions/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/netstandard1.0/System.Threading.Tasks.Extensions.dll", "ref/netstandard1.0/System.Threading.Tasks.Extensions.xml", "ref/netstandard2.0/System.Threading.Tasks.Extensions.dll", "ref/netstandard2.0/System.Threading.Tasks.Extensions.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.1.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Domain/1.0.0": {"type": "project", "path": "../Domain/Domain.csproj", "msbuildProject": "../Domain/Domain.csproj"}, "Entity/1.0.0": {"type": "project", "path": "../Entity/Entity.csproj", "msbuildProject": "../Entity/Entity.csproj"}, "ErrorCodeGenerator/1.0.0": {"type": "project", "path": "../ErrorCodeGenerator/ErrorCodeGenerator.csproj", "msbuildProject": "../ErrorCodeGenerator/ErrorCodeGenerator.csproj"}, "Helper/1.0.0": {"type": "project", "path": "../Helper/Helper.csproj", "msbuildProject": "../Helper/Helper.csproj"}, "Infrastructure/1.0.0": {"type": "project", "path": "../Infrastructure/Infrastructure.csproj", "msbuildProject": "../Infrastructure/Infrastructure.csproj"}, "PostgreDataContext/1.0.0": {"type": "project", "path": "../PostgreDataContext/PostgreDataContext.csproj", "msbuildProject": "../PostgreDataContext/PostgreDataContext.csproj"}}, "projectFileDependencyGroups": {"net6.0": ["Infrastructure >= 1.0.0", "PostgreDataContext >= 1.0.0"]}, "packageFolders": {"/Users/<USER>/.nuget/packages/": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EventProcessorService/EventProcessor.csproj", "projectName": "EventProcessor", "projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EventProcessorService/EventProcessor.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/EventProcessorService/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Infrastructure/Infrastructure.csproj": {"projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/Infrastructure/Infrastructure.csproj"}, "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/PostgreDataContext/PostgreDataContext.csproj": {"projectPath": "/Users/<USER>/Documents/Projects/bizleap-healthcare/Frontend/smartkarte-server/PostgreDataContext/PostgreDataContext.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.404/RuntimeIdentifierGraph.json"}}}}